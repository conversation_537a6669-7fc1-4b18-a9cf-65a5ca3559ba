<?php
//include classes and config file here	
include('includes/validatesystemuserlogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsPlanMaster.php');

$planId = 0;
$title = '';
$price = '';
$contactLimit = '';
$cardLimit = '';
$subCardLimit = '';
$imageInfographSectionLimit = '';
$videoInfographSectionLimit = '';
$allowThemeEditing = '';
$imageCarouselLimit = '';
$imageInfographLimit = '';
$videoInfographLimit = '';
$formLimit = '';
$allowLogoInclusion = '';
$allowPhotoInclusion = '';
$allowVideoProfile = '';
$isDedicatedURL = '';
$isCrmIntergration = '';
$isCouponServices = '';
$isWhiteLabel = '';
$isPDFSubscription = '';
$shoppingCart = '';
$chatRoom = '';
$pushNotification = '';
$email = 0;
$smschk = 0;
$surveychk = 0;
$customQrFormchk =0;
$geolocation = 0;
$socialfeed = 0;
$statistics = 0;
$support = 0;
$todo = 0;
$probizcaplus = 0;
$invoice = 0;
$infoContentLimit = 0;
$infoSectionLiimit = 0;
$quarterlyAmount = 0;
$semiAnnualAmount = 0;
$annualAmount = 0;
$galleryImageLimit = 0;
$type = 0;
$durationDays = 0;

$pageTitle = 'Add';
$buttonTitle = 'Save';

// $planId = 0;
if (isset($_GET['id'])) {
	$pageTitle = 'Edit';
	$buttonTitle = 'Update';
	$planId = DecodeQueryData($_GET['id']);
	$objPlanMaster = new clsPlanMaster();
	$row = $objPlanMaster->GetPlanDetails($planId);
	unset($objPlanMaster);

	$title = stripslashes($row['title']);
	$price = $row['price'];
	$quarterlyAmount = $row['quarterlyAmount'];
	$semiAnnualAmount = $row['semiAnnualAmount'];
	$annualAmount = $row['annualAmount'];
	$contactLimit = $row['contactLimit'];
	$cardLimit = $row['cardLimit'];
	$subCardLimit = $row['subCardLimit'] ? $row['subCardLimit'] : 0;
	$imageInfographSectionLimit = $row['imageInfographSectionLimit'];
	$videoInfographSectionLimit = $row['videoInfographSectionLimit'];
	$allowThemeEditing = $row['allowThemeEditing'];
	$imageCarouselLimit = $row['imageCarouselLimit'];
	$imageInfographLimit = $row['imageInfographLimit'];
	$videoInfographLimit = $row['videoInfographLimit'];
	$formLimit = $row['formLimit'];
	$allowLogoInclusion = $row['allowLogoInclusion'];
	$allowPhotoInclusion = $row['allowPhotoInclusion'];
	$allowVideoProfile = $row['allowVideoProfile'];
	$isDedicatedURL = $row['isDedicatedURL'];
	$isCrmIntergration = $row['isCrmIntergration'];
	$isCouponServices = $row['isCouponServices'];
	$isWhiteLabel = $row['isWhiteLabel'];
	$isPDFSubscription = $row['isPDFSubscription'];
	$pushNotification = $row['pushNotification'];
	$chatRoom = $row['chatRoom'];
	$shoppingCart = $row['shoppingCart'];
	$email = stripslashes($row['email']);
	$defaultEmail = stripslashes($row['defaultEmail']);
	$smschk = stripslashes($row['smschk']);
	$defaultSms = stripslashes($row['defaultSms']);
	$surveychk = stripslashes($row['surveychk']);

	$customQrFormchk = stripslashes($row['customQrFormchk']);

	$geolocation = stripslashes($row['geolocation']);
	$socialfeed = stripslashes($row['socialfeed']);
	$statistics = stripslashes($row['statistics']);
	$support = stripslashes($row['support']);
	$todo = stripslashes($row['todo']);
	$probizcaplus = stripslashes($row['probizcaplus']);
	$invoice = stripslashes($row['invoice']);
	$infoSectionLiimit = stripslashes($row['infoSectionLiimit']);
	$infoContentLimit = stripslashes($row['infoContentLimit']);
	$galleryImageLimit = stripslashes($row['galleryImageLimit']);
	$type = stripslashes($row['type']);

	$durationDays = stripslashes($row['durationDays']);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<title><?php echo ($pageTitle); ?> Plan</title>
	<?php include("includes/headercss.php") ?>
	<?php include("includes/datatablecss.php") ?>
	<link href="<?php echo BASE_PATH; ?>/superadmin/vendors/select2/dist/css/select2.min.css" rel="stylesheet">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/superadmin/vendors/alertifyjs/css/alertify.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/superadmin/vendors/inputmask/form-extended.css">
</head>

<body class="nav-md">
	<div class="container body">
		<div class="main_container">
			<?php include("includes/leftnav.php") ?>
			<?php include("includes/header.php") ?>
			<!-- page content -->
			<div class="right_col" role="main">
				<div class="">
					<div class="clearfix"></div>
					<div class="row">
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="x_panel">
								<div class="x_title">
									<h2><?php echo ($pageTitle); ?> Plan</h2>
									<ul class="nav navbar-right panel_toolbox">
										<li><a href="viewsubscriptionplan.html"><i class="fa fa-backward"></i> Back</a></li>
									</ul>
									<div class="clearfix"></div>
								</div>
								<div class="x_content">
									<form id="userForm" data-parsley-validate class="form-horizontal form-label-left" action="addsubscriptionplanmastersubmit.html" method="POST" enctype="multipart/form-data">
										<input type="hidden" name="planId" value="<?php echo ($planId); ?>">
										<div class="row">
											<div class="col-md-12">
												<h2 class="ml-4">Basic Details</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="title">Title</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="title" name="title" type="text" placeholder="" value="<?php echo ($title); ?>" class="form-control input-md required-input" required>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="price">Setup Fee</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="price" name="price" type="text" placeholder="" value="<?php echo ($price); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="quarterlyAmount">Quarterly</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="quarterlyAmount" name="quarterlyAmount" type="text" placeholder="" value="<?php echo ($quarterlyAmount); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="semiAnnualAmount">Semi Annual</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="semiAnnualAmount" name="semiAnnualAmount" type="text" placeholder="" value="<?php echo ($semiAnnualAmount); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="annualAmount">Annually</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="annualAmount" name="annualAmount" type="text" placeholder="" value="<?php echo ($annualAmount); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="contactLimit">Contact Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="1" id="contactLimit" name="contactLimit" type="number" placeholder="" value="<?php echo ($contactLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="cardLimit">Card Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="1" id="cardLimit" name="cardLimit" type="number" placeholder="" value="<?php echo ($cardLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="subCardLimit">Sub Card Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="subCardLimit" name="subCardLimit" type="number" placeholder="" value="<?php echo ($subCardLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="formLimit">Form Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="formLimit" name="formLimit" type="number" placeholder="" value="<?php echo ($formLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group  mt-5 row">
															<div class="col-md-6 mt-5" style="padding:5px; text-align:center;">
																<input class="form-check-input" type="radio" name="type" id="type" value="1" <?php if ($type == 1) {
																																					echo "checked";
																																				} ?>>
																<label class="form-check-label" for="type">
																	Private
																</label>
															</div>
															<div class="col-md-6 mt-5" style="padding:5px;">
																<input class="form-check-input" type="radio" name="type" id="type" value="0" <?php if ($type == 0) {
																																					echo "checked";
																																				} ?>>
																<label class="form-check-label" for="type">
																	Public
																</label>
															</div>
														</div>
													</div>
													<?php if($planId == 11) { ?>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="durationDays">Trial Duration</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="durationDays" name="durationDays" type="text" placeholder="Duration in Days" value="<?php echo ($durationDays); ?>" class="form-control input-md required-input number" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<?php } ?>
												</div>
											</div>
											<div class="col-md-12">
												<h2 class="ml-4">Gallery</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="imageCarouselLimit">Album Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="imageCarouselLimit" name="imageCarouselLimit" type="number" placeholder="" value="<?php echo ($imageCarouselLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="galleryImageLimit">Gallery Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="galleryImageLimit" name="galleryImageLimit" type="number" placeholder="" value="<?php echo ($galleryImageLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-12">
												<h2 class="ml-4">Infographic</h2>
												<div class="ln_solid"></div>
												<div class="row" style="display:none;">
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="imageInfographSectionLimit">Image Info. Sections Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" max="5" id="imageInfographSectionLimit" name="imageInfographSectionLimit" type="number" placeholder="" value="<?php echo ($imageInfographSectionLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="5" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="imageInfographLimit">Image Content Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="imageInfographLimit" name="imageInfographLimit" type="number" placeholder="" value="<?php echo ($imageInfographLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="videoInfographSectionLimit">Video Info. Sections Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="videoInfographSectionLimit" name="videoInfographSectionLimit" type="number" placeholder="" value="<?php echo ($videoInfographSectionLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="2" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="videoInfographSectionLimit">Video Content Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="videoInfographLimit" name="videoInfographLimit" type="number" placeholder="" value="<?php echo ($videoInfographLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="infoSectionLiimit">Sections Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="infoSectionLiimit" name="infoSectionLiimit" type="number" placeholder="" value="<?php echo ($infoSectionLiimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="2" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="infoContentLimit">Content Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="infoContentLimit" name="infoContentLimit" type="number" placeholder="" value="<?php echo ($infoContentLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-12">
												<h2 class="ml-4">Sms/Email</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="infoSectionLiimit">SMS Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input  min="0" id="defaultSms" name="defaultSms" type="number" placeholder="" value="<?php echo ($defaultSms);  ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="infoContentLimit">Email Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="defaultEmail" name="defaultEmail" type="number" placeholder="" value="<?php echo ($defaultEmail); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="ln_solid"></div>
										<div class="row">
											<div class="col-md-6">
												<h2 class="ml-4">Themes/Profile Details</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="imageCarouselLimit">Dedicated URL.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isDedicatedURL" id="isDedicatedURL" <?php echo ($isDedicatedURL ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="cardLimit">Edit Theme.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowThemeEditing" id="allowThemeEditing" <?php echo ($allowThemeEditing ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="allowLogoInclusion">Logo Inclusion.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowLogoInclusion" id="allowLogoInclusion" <?php echo ($allowLogoInclusion ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="contactLimit">Photo Inclusion.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowPhotoInclusion" id="allowPhotoInclusion" <?php echo ($allowPhotoInclusion ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="subCardLimit">Video Profile.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowVideoProfile" id="allowVideoProfile" <?php echo ($allowVideoProfile  ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="formLimit">White Label</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isWhiteLabel" id="isWhiteLabel" <?php echo ($isWhiteLabel  ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-6">
												<h2 class="ml-4">Additional Addon's</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="isCrmIntergration">Allow CRM.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isCrmIntergration" id="isCrmIntergration" <?php echo ($isCrmIntergration   ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="isPDFSubscription">Allow Paid Contents</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isPDFSubscription" id="isPDFSubscription" <?php echo ($isPDFSubscription ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="isCouponServices">Coupons/Incentives</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isCouponServices" id="isCouponServices" <?php echo ($isCouponServices ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="chatRoom">Chat Room</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="chatRoom" id="chatRoom" <?php echo ($chatRoom ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="email">Email</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="email" id="email" <?php echo ($email ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="smschk">SMS</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="smschk" id="smschk" <?php echo ($smschk ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="geolocation">Geolocation</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="geolocation" id="geolocation" <?php echo ($geolocation ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="invoice">Invoice</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="invoice" id="invoice" <?php echo ($invoice ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="pushNotification">Push Notification</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="pushNotification" id="pushNotification" <?php echo ($pushNotification ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="probizcaplus">ProBizCa +</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="probizcaplus" id="probizcaplus" <?php echo ($probizcaplus ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="shoppingCart">Shopping Cart</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="shoppingCart" id="shoppingCart" <?php echo ($shoppingCart ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="statistics">Statistics</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="statistics" id="statistics" <?php echo ($statistics ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="support">Support</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="support" id="support" <?php echo ($support ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="todo">To Do</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="todo" id="todo" <?php echo ($todo ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="survey">Survey</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="surveychk" id="surveychk" <?php echo ($surveychk ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="customQrFormchk">Custom QR Form</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="customQrFormchk" id="customQrFormchk" <?php echo ($customQrFormchk ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="ln_solid"></div>
										<div class="form-group text-center">
											<div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3">
												<button type="submit" class="btn btn-success" name="btnSave"><?php echo ($buttonTitle); ?></button>
												<a type="submit" class="btn btn-primary" href="viewsubscriptionplan.html">Cancel</a>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- /page content -->
			<?php include("includes/footer.php") ?>
		</div>
	</div>
	<?php include("includes/footerjs.php") ?>
	<?php include("includes/datatablejs.php") ?>
	<script src="<?php echo BASE_PATH; ?>/superadmin/build/js/custom.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/parsleyjs/parsley.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/select2/dist/js/select2.full.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/jquery.cascadingdropdown.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/alertifyjs/alertify.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/inputmask/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/inputmask/extended/form-inputmask.js"></script>
	<script src="vendors/jquery.lazyload.js"></script>
	<script type="text/javascript">
		//validate input number with decimal
		$('.number').keypress(function(event) {
			var $this = $(this);
			if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
				((event.which < 48 || event.which > 57) &&
					(event.which != 0 && event.which != 8))) {
				event.preventDefault();
			}

			var text = $(this).val();
			if ((event.which == 46) && (text.indexOf('.') == -1)) {
				setTimeout(function() {
					if ($this.val().substring($this.val().indexOf('.')).length > 3) {
						$this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
					}
				}, 1);
			}

			if ((text.indexOf('.') != -1) &&
				(text.substring(text.indexOf('.')).length > 2) &&
				(event.which != 0 && event.which != 8) &&
				($(this)[0].selectionStart >= text.length - 2)) {
				event.preventDefault();
			}
		});

		$('.number').bind("paste", function(e) {
			var text = e.originalEvent.clipboardData.getData('Text');
			if ($.isNumeric(text)) {
				if ((text.substring(text.indexOf('.')).length > 3) && (text.indexOf('.') > -1)) {
					e.preventDefault();
					$(this).val(text.substring(0, text.indexOf('.') + 3));
				}
			} else {
				e.preventDefault();
			}
		});

		//form validation
		$('#userForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});

		//number validation 
		$('#mobileNo').keypress(function(event) {
			var keycode = event.which;
			if (!(event.shiftKey == false && (keycode == 46 || keycode == 8 || keycode == 37 || keycode == 39 || (keycode >= 48 && keycode <= 57)))) {
				event.preventDefault();
			}
		});
	</script>
</body>

</html>