let socket;
let isConnected = false;
function WebSocketConnectionStart(loggedBusinessId, loggedUserEmail, loggedUserId, role) {

    /* WebSocket Connection Start*/
    // console.log(sessionStorage.getItem('socketConnected'));



    $.ajax({
        type: "POST",
        url: '../ajax/ajax_update_user_login_status_to_chat.html',
        data: {
            'loggedBusinessId': loggedBusinessId,
            'LoggedUserEmail': loggedUserEmail,
            'loggedUserId': loggedUserId,
            'roleId': role,
            'isOnline': 1,
        },
        dataType: 'json',
        success: function (data) {

            console.log("Chat data ", data);

        }
    });
}

let newWindow = null
let chatAppWindow = null;
function probizcaChatAuth(conversationId, authorRole) {


		// var userconversationId = (conversationId > 0) ? conversationId : 0;

		var loggedBusinessId = $('#loggedBusinessId').val();
		var isPrimary = $('#isPrimary').val();
		if(loggedBusinessId)
		{
			var loggedUserId = $('#loggedEmployeeId').val();
			
			var roleId = (isPrimary == 1) ? 2 : 3;
		}
		else if(isPrimary){
			var loggedUserId = $('#systemUserId').val();
			var roleId = 1;
		}
		else{
			var loggedUserId = $('#loggedUserId').val();
			var roleId = $('#roleId').val();
			roleId = (roleId == 4) ? roleId : 5;	

		}
		// alert(isPrimary);

		$.ajax({
			type: "GET",
			url: './../ajax/ajax_authenticat_chat_user.html',
			data: {
				'loggedBusinessId': loggedBusinessId,
				'loggedUserId': loggedUserId,
				'roleId': roleId,
				'userconversationId': conversationId,
				'authorRole': authorRole

			},
			// dataType: "json",
			success: function (data) {
				console.log(data);
				// alert(data);

				// window.open(data, "_blank");
				// setTimeout(() => {
				// 	console.log('inside here');
				// 	GetChatNotificationsList();


				// }, 15000);


				 if (isValidURL(data)) {
                // $('.custom-popover').css("display", "none");
                // $('#backdrop').css("display", "none");
                console.log("Valid URL");
                if (newWindow && newWindow.window !== null) {
                    console.log("if");
                    newWindow.focus();
                } else {
                    console.log("else");
                    // newWindow = window.open(data, "_blank");
                    console.log("Opening new Chat APP window.");
                    newWindow = window.open(data, "chatAppWindow");

                    if (newWindow) {
                        newWindow.name = "chatAppWindow"; // Set unique name

                        // Store window details in localStorage
                        localStorage.setItem('chatAppWindow', JSON.stringify({
                            url: data,
                            windowName: newWindow.name,
                            isOpen: true
                        }));
                    }
                }

                setTimeout(() => {
                    // console.log('inside here');
                    GetChatNotificationsList(roleId, loggedUserEmail, loggedBusinessId, loggedUserId);
                }, 5000);

                console.log("New Window ", newWindow);

            } else {
                alertify.error(data);
            }

			}

		});

	}

	function isValidURL(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

    function SendDeviceTokenToChatApp(currentToken, businessId, role, loggedUserEmail, ref_userId, xpikey, domain='',isSuperAdmin=0) {
        
        var businessId = businessId;
        var role = role;
        var loggedUserEmail = loggedUserEmail;
        var ref_userId = ref_userId;
        var xpikey = xpikey;
        var isSuperAdmin = isSuperAdmin;

        $.ajax({
            type: "POST",
            url: '../ajax/ajax_send_device_token.html',
            data: {
                businessId: businessId,
                isSuperAdmin: isSuperAdmin,
                ref_userId: ref_userId,
                email: loggedUserEmail,
                role: role,
                fcmToken: currentToken
            },
            dataType: 'json',
            success: function (data) {

                console.log(data);

            }
        });

        
	}


function isValidURL(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function GetChatNotificationsList(role, loggedUserEmail, loggedBusinessId, loggedUserId) {
    
	var role = role;
    var loggedUserEmail = loggedUserEmail;
    var parent_id = loggedBusinessId;
    var loggedUserId = loggedUserId;

	var prbNotiCout = $('#notificationCount').val();

		$.ajax({
			type: "GET",
			url: "../ajax/ajax_get_chat_notifications.html",
			data: {
				loggedUserEmail: loggedUserEmail,
				loggedUserId: loggedUserId,
				parent_id: parent_id,
				role: role
			},
			dataType: 'json',
			success: function(response) {
				console.log(response);
				if (response.response.statusCode == 200) {

					var notificationCount = response.response.data.count;
					// console.log("notificationCount :", notificationCount);

					var probizcaNotificationCount = prbNotiCout;

					var totalnotiCount = parseInt(probizcaNotificationCount) + parseInt(notificationCount);

					$('#countValue').text(totalnotiCount);

					$('#countValue2').text(notificationCount + ' New');

					$('#chatNotification').empty();

					// Group notifications by _id
					var groupedNotifications = {};
					response.response.data.data.forEach(function(notification) {
						if (!groupedNotifications[notification._id]) {
							groupedNotifications[notification._id] = {
								authorId: notification.authorId,
								authorFirstName: notification.authorFirstName,
								authorLastName: notification.authorLastName,
								authorRole: notification.authorRole,
								conversationId: notification.conversationId,
								authorProfilePic: notification.authorProfilePic ? notification.authorProfilePic : 'https://dev.probizca.net/upload/default-user.png',
								notifications: []
							};
						}
						groupedNotifications[notification._id].notifications.push(notification);
					});

					// Loop through grouped notifications
					for (var authorId in groupedNotifications) {
						if (groupedNotifications.hasOwnProperty(authorId)) {
							var author = groupedNotifications[authorId];
							var conversationId = (author.conversationId);
							var authorRole = (author.authorRole);

							// Create HTML for author section
							var authorHtml = `<a href="javascript:void(0);" onclick="probizcaChatAuth('${conversationId}','${authorRole}')" class="viewNotificationPopup">
					<div class="author-section">
						<ul class="notification-list">`;

							// Loop through notifications for this author
							author.notifications.forEach(function(notification) {
								// Get the timestamp for the current notification
								var timestamp = notification.updatedAt;

								// Calculate the time ago for this notification
								var time = getTimeAgo(timestamp);

								// Create HTML for each notification
								authorHtml += `<li>
						<div class="notification" style="display: flex;align-items: center;">
							<div class="notification-avatar">
								<img src="${author.authorProfilePic}" alt="${author.authorFirstName}'s avatar">
							</div>
							<div class="notification-details">
								<h4>${author.authorFirstName} ${author.authorLastName}</h4>
								<h6 class="notification-heading">You have ${notification.count} new messages</h6>
								<p class="notification-text">${time}</p>
							</div>
						</div>
						<hr>
					</li>`;
							});

							// Close author section HTML
							authorHtml += `</ul></div></a>`;

							// Append author section HTML to chatNotification element
							$('#chatNotification').append(authorHtml);
						}
					}
				} else {
					$('#chatNotification').empty();

					var probizcaNotificationCount = prbNotiCout;
					// console.log(probizcaNotificationCount);
					var totalnotiCount = parseInt(probizcaNotificationCount) + parseInt(0);
					$('#countValue').text(totalnotiCount);
					$('#countValue2').text('');
				}
			},
			error: function(xhr, status, error) {
				// Handle errors
				// console.error(xhr.responseText);
			}
		});
	}

	function getTimeAgo(timestamp) {
		const date = new Date(timestamp);
		const currentDate = new Date();
		const differenceMs = currentDate - date;
		const secondsAgo = Math.floor(differenceMs / 1000);
		const minutesAgo = Math.floor(secondsAgo / 60);
		const hoursAgo = Math.floor(minutesAgo / 60);
		const daysAgo = Math.floor(hoursAgo / 24);
		const monthsAgo = Math.floor(daysAgo / 30);
		const yearsAgo = Math.floor(daysAgo / 365);

		if (yearsAgo > 0) {
			return yearsAgo === 1 ? "1 year ago" : `${yearsAgo} years ago`;
		} else if (monthsAgo > 0) {
			return monthsAgo === 1 ? "1 month ago" : `${monthsAgo} months ago`;
		} else if (daysAgo > 0) {
			return daysAgo === 1 ? "1 day ago" : `${daysAgo} days ago`;
		} else if (hoursAgo > 0) {
			return hoursAgo === 1 ? "1 hour ago" : `${hoursAgo} hours ago`;
		} else if (minutesAgo > 0) {
			return minutesAgo === 1 ? "1 minute ago" : `${minutesAgo} minutes ago`;
		} else {
			return "just now";
		}
	}

$('#logoutButton').on('click', function () {

    closeWindow();
    // Ensure the audit log window closes before redirecting
    setTimeout(function () {
        console.log("Redirecting to logout page.");
        window.location.href = './logout.html';
    }, 300); // Delay of 300ms to ensure proper closure

});


// Function to close the audit log window on logout
function closeWindow() {
    const storedData = localStorage.getItem('chatAppWindow');
    console.log('storedData:', storedData);

    if (storedData) {
        const { windowName, isOpen } = JSON.parse(storedData);

        if (isOpen) {
            console.log('Trying to close audit log window:', windowName);

            // Find the existing audit log window
            chatAppWindow = window.open('', windowName); // Try to get existing window

            if (chatAppWindow && !chatAppWindow.closed) {
                console.log("Closing existing audit log window.");
                chatAppWindow.close();
            }

            // Remove from localStorage
            localStorage.removeItem('chatAppWindow');
        }
    }
}
