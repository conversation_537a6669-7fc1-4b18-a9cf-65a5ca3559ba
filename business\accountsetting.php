<?php
//include classes and config file here	
include('./includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsEmployee.php');
include('../class/clsBusinessPlanMaster.php');
include('../class/clsPlanMaster.php');
include('../class/clsBusiness.php');
include('../class/clsBusinessWallet.php');
include('../class/clsAddonMaster.php');
include('../class/clsChatApp.php');



//variables
$countries = '';
$dbCountryId = '224';
$stateId = 0;
$businessGroupId  = 0;
$sortOrder   = 0;
$businessId = $_SESSION["loggedBusinessId"];
$employeeId = $_SESSION["loggedEmployeeId"];
$firstName = '';
$lastName = '';
$email = '';
$phoneNo = '';
$address = '';
$city = '';
$zipCode = '';
$imageName = '';
$employeeImagePath = '';
$pageTitle = 'Edit';
$buttonTitle = 'Update';
$businesscountryId = 0;
$businessdbStateId = 0;
$planId = 0;
$title = '';
$price = '';
$contactLimit = '';
$cardLimit = '';
$subCardLimit = '';
$imageInfographSectionLimit = '';
$videoInfographSectionLimit = '';
$allowThemeEditing = '';
$imageCarouselLimit = '';
$imageInfographLimit = '';
$videoInfographLimit = '';
$formLimit = '';
$allowLogoInclusion = '';
$allowPhotoInclusion = '';
$allowVideoProfile = '';
$isDedicatedURL = '';
$isCrmIntergration = '';
$isCouponServices = '';
$isWhiteLabel = '';
$isPDFSubscription = '';
$shoppingCart = '';
$chatRoom = '';
$pushNotification = '';
$planmasters = '';
$businessName = '';
$pageTitle = 'Add';
$buttonTitle = 'Save';
$businessName = '';
$email = '';
$phoneNo = '';
$address = '';
$city = '';
$dbStateId = 0;
$countryId = 0;
$zipCode = '';
$logoName = '';
$website = '';
$businessCode = '';
$slug = '';
$link_domain_name = '';
$slogan = '';
$aboutusTitle = '';
$description = '';
$whats_app_number = '';
$map_address = '';
$tracking_id = '';
$google_plus = '';
$facebook = '';
$twitter = '';
$linked_in = '';
$youtube = '';
$instagram = '';
$google_play_app_url = '';
$itune_app_url = '';
$approvedStatus = '';
$businessLogo = '';
$aboutusImage = '';
$email = '';
$defaultEmail  = '';
$smschk = '';
$defaultSms  = '';
$geolocation = 0;
$socialfeed = 0;
$statistics = 0;
$support = 0;
$todo = 0;
$bPlanId = 0;
$probizcaplus = 0;
$invoice = 0;
$walletAmount = 0.00;
$walletCreditAmount = 0.00;
$walletDebitAmount = 0.00;
$allTransactions = '';
$paymentType = '1'; //1-annual,2-semi,3-quertrly
$isSetupFee = '';
$birthDate  = "";

//object
$objEmployee = new clsEmployee();

$loggedEmail = $_SESSION["loggedEmail"];
$primaryBusinessDetail = $objEmployee->getPrimaryBusinessDetail($loggedEmail);
$primaryBusinessName = $primaryBusinessDetail['businessName'];

$objBusinessWallet = new clsBusinessWallet();
$walletCreditAmount = $objBusinessWallet->GetBusinessWalletCreditAmount($businessId);
$walletDebitAmount = $objBusinessWallet->GetBusinessWalletDebitAmount($businessId);

$walletCreditAmount = isset($walletCreditAmount) ? $walletCreditAmount : '0';
$walletDebitAmount = isset($walletDebitAmount) ? $walletDebitAmount : '0';

//total balance
if ($walletCreditAmount || $walletDebitAmount)
	$walletAmount = number_format($walletCreditAmount - $walletDebitAmount, 2);

$allTransactions = $objBusinessWallet->GetBusinessWalletTransactions($businessId);

//stripe publish key
$stripeKeys = STRIPE_PUBLISHABLE_KEY;

//Get Location
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
unset($objCountryStateMaster);



//get employee details
$row = $objEmployee->GetEmployeeDetails($employeeId);
unset($objEmployee);
if ($row != '') {
	$firstName  = stripslashes($row['firstName']);
	$lastName  = stripslashes($row['lastName']);
	$email  = stripslashes($row['email']);
	$phoneNo  = stripslashes($row['phoneNo']);
	$address  = stripslashes($row['address']);
	$address2  = stripslashes($row['address2']);
	$city  = stripslashes($row['city']);
	$zipCode  = stripslashes($row['zipCode']);
	$stateId  = stripslashes($row['stateId']);
	$imageName  = stripslashes($row['imageName']);
	// echo $row['birthDate'];exit;
	if ($row['birthDate'] == "0000-00-00 00:00:00" || $row['birthDate'] == null) {
		$birthDate = '';
	} else {
		$birthDate = date("m/d/Y h:i A", strtotime($row['birthDate']));
	}
	// $birthDate = isset($row['birthDate']) &&  $row['birthDate'] != '0000-00-00 00:00:00' || $row['birthDate'] != null ? date("m/d/Y h:i A",strtotime($row['birthDate'])) : '';

	//get employee image path
	$employeeImagePath = GetEmployeeImagePath($businessId, $employeeId, $imageName);

	$objChatApp = new clsChatApp();
	$usermanagemetData = $objChatApp->getUserDetails(2, $employeeId);
	$usermanagemetId = $usermanagemetData['id'];
}

//get Country from State
$objCountryStateMaster = new clsCountryStateMaster();
$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);



//get all actibe plan
$objBusinessPlanMaster = new clsBusinessPlanMaster();
$planmasters = $objBusinessPlanMaster->GetAllActivePlans();
unset($objBusinessPlanMaster);

//get business name 
$objBusiness = new clsBusiness();

//get business details
$rows = $objBusiness->GetBusinessDetails($businessId);
$businessCode = stripslashes($rows['businessCode']);
$planId = stripslashes($rows['planId']);
$businessName = stripslashes($rows['businessName']);
$businessemail = stripslashes($rows['email']);
$businessphoneNo = stripslashes($rows['phoneNo']);
$businessaddress = stripslashes($rows['address']);
$businessaddress2 = stripslashes($rows['address2']);
$businesscity = stripslashes($rows['city']);
$businessdbStateId = stripslashes($rows['stateId']);
$businesscountryId = $objCountryStateMaster->GetParentIdFromChildId($businessdbStateId);
$businesszipCode = stripslashes($rows['zipCode']);
$businesslogoName = stripslashes($rows['logoName']);
$businesswebsite = stripslashes($rows['website']);
$businessslug = stripslashes($rows['slug']);
$facebook = stripslashes($rows['facebook']);	
$twitter = stripslashes($rows['twitter']);
$linked_in  = stripslashes($rows['linked_in']);
$youtube = stripslashes($rows['youtube']);
$instagram = stripslashes($rows['instagram']);

$purchaseEmail = $rows['totalPurchaseEmail'];
$purchaseSMS = $rows['totalPurchaseSms'];
//get image path
$businessLogo  = GetBusinessImagePath($businessId, $rows['logoName']);

// echo $businessId;exit;
$objBusinessPlanMaster = new clsBusinessPlanMaster();
$row = $objBusinessPlanMaster->GetBusinessPlanDetails($businessId, $planId = '');
unset($objBusinessPlanMaster);

if ($row == '') {
	$businessPlanDetailId = 0;
	$objPlanMaster = new clsPlanMaster();
	$row = $objPlanMaster->GetPlanDetails($planId);
	unset($objPlanMaster);
} else {
	$businessPlanDetailId = stripslashes($row['businessPlanDetailId']);
	$title = stripslashes($row['title']);
	$price = $row['price'];
	$bPlanId = $row['planId'];
	$contactLimit = $row['contactLimit'];
	$cardLimit = $row['cardLimit'];
	$subCardLimit = $row['subCardLimit'] ? $row['subCardLimit'] : 0;
	$imageInfographSectionLimit = $row['imageInfographSectionLimit'];
	$videoInfographSectionLimit = $row['videoInfographSectionLimit'];
	$allowThemeEditing = $row['allowThemeEditing'];
	$imageCarouselLimit = $row['imageCarouselLimit'];
	$imageInfographLimit = $row['imageInfographLimit'];
	$videoInfographLimit = $row['videoInfographLimit'];
	$formLimit = $row['formLimit'];
	$allowLogoInclusion = $row['allowLogoInclusion'];
	$allowPhotoInclusion = $row['allowPhotoInclusion'];
	$allowVideoProfile = $row['allowVideoProfile'];
	$isDedicatedURL = $row['isDedicatedURL'];
	$isCrmIntergration = $row['isCrmIntergration'];
	$isCouponServices = $row['isCouponServices'];
	$isWhiteLabel = $row['isWhiteLabel'];
	$isPDFSubscription = $row['isPDFSubscription'];
	$pushNotification = $row['pushNotification'];
	$chatRoom = $row['chatRoom'];
	$shoppingCart = $row['shoppingCart'];
	$chatemail = stripslashes($row['email']);
	$smschk = stripslashes($row['smschk']);
	$geolocation = stripslashes($row['geolocation']);
	$socialfeed = stripslashes($row['socialfeed']);
	$statistics = stripslashes($row['statistics']);
	$support = stripslashes($row['support']);
	$todo = stripslashes($row['todo']);
	$probizcaplus = stripslashes($row['probizcaplus']);
	$invoice = stripslashes($row['invoice']);
	$infoContentLimit = stripslashes($row['infoContentLimit']);
	$infoSectionLiimit = stripslashes($row['infoSectionLiimit']);
	$quarterlyAmount = $row['quarterlyAmount'];
	$semiAnnualAmount = $row['semiAnnualAmount'];
	$annualAmount = $row['annualAmount'];
	$isSetupFee = $row['isSetupFee'];
	$paymentType = $row['paymentType'];
	$paymentPlan = $row['paymentPlan'];


	// echo "subscriptionStartDate ".$row['subscriptionStartDate'];
	if($row['subscriptionStartDate'] == '0000-00-00 00:00:00' || $row['subscriptionStartDate'] == '12/31/1969'){
		// echo "in if";exit;
		$subscriptionStartDate = '-';
	}else{
		// echo "in else";exit;
		$subscriptionStartDate = date("m/d/Y", strtotime($row['subscriptionStartDate']));
	}
	// $subscriptionStartDate = ($row['subscriptionStartDate'] !== '0000-00-00 00:00:00' && $row['subscriptionStartDate'] !== '12/31/1969') ? stripslashes($row['subscriptionStartDate']) : '-';
	// echo "<br>";
	// echo "subscriptionEndDate ".$row['subscriptionEndDate'];
	// exit;
	if($row['subscriptionEndDate'] == '0000-00-00 00:00:00' || $row['subscriptionEndDate'] == '12/31/1969'){
		// echo "in if";exit;
		$subscriptionEndDate = '-';
	}else{
		// echo "in else";exit;
		$subscriptionEndDate = date("m/d/Y", strtotime($row['subscriptionEndDate']));
	}
	// $subscriptionEndDate = ($row['subscriptionEndDate'] !='0000-00-00 00:00:00' && $row['subscriptionEndDate'] !='12/31/1969' ) ? stripslashes($row['subscriptionEndDate']) : '-';


	

	// echo $title;exit;

	$objAddonMaster = new clsAddonMaster();
	// 1 for Email & 2 for SMS.
	$emailaddonDtls = $objAddonMaster->GetsubscriptionTypeDetails(1, $paymentPlan);
	$smsaddonDtls = $objAddonMaster->GetsubscriptionTypeDetails(2, $paymentPlan);

	$EmailDtls = $objAddonMaster->GetAddonpurchaseUsedDtls($businessId, 1);
	// $purchaseEmail = $EmailDtls['purchasedCount'];
	$purchaseEmailUsed = $EmailDtls['purUsed'];

	$SMSDtls = $objAddonMaster->GetAddonpurchaseUsedDtls($businessId, 2);
	// $purchaseSMS = $SMSDtls['purchasedCount'];
	$purchaseSMSUsed = $SMSDtls['purUsed'];

	$subscriptionType = "";
	if ($paymentPlan == 1) {
		$subscriptionType = "Quarterly";
	} else if ($paymentPlan == 2) {
		$subscriptionType = "Semi Annul";
	} else {
		$subscriptionType = "Annually";
	}
}



?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
	<title>Account Settings</title>
	<?php include('includes/headerCss.php'); ?>
	<?php include('includes/datatableCss.php'); ?>
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/assets/vendors/js/magnificpopup/css/magnific-popup.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/forms/extended/form-extended.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/selects/select2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/pickers/pickadate/pickadate.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/toggle/switchery.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/core/colors/palette-gradient.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/forms/extended/form-extended.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/icheck/icheck.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/icheck/custom.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/forms/checkboxes-radios.min.css">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
	<link rel="stylesheet" href="../assets/css/accountsetting.css">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/assets/cropper.css">


	<style>
		.row {
			margin-left: 0 !important;
			margin-right: 0 !important;
		}

		.p-0 {
			padding: 0 !important;
		}

		.flex-between {
			display: flex;
			justify-content: space-between;
		}

		.nav.nav-tabs .nav-item .nav-link.active {
			color: #fff !important;
			background-color: #1e9ff2 !important;
		}

		.subscription-card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30px 35px 0;
		}

		.header-border-bottom {
			border-bottom: 1px solid #E4E5EC;
		}

		.w-half {
			width: 50%;
		}

		.padding-x-5 {
			padding: 0 5px;
		}

		.padding-x-0 {
			padding-left: 0 !important;
			padding-right: 0 !important;
		}

		.mb-0 {
			margin-bottom: 0;
		}

		.close-parent {
			display: none !important;
		}

		/* form .form-section{
			border-bottom: none !important;
		} */
		html body .content .content-wrapper {
			padding: 2.1rem 2rem 0;
		}

		.wallet-amount-parent {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
		}

		/* .btn-warning.btn-glow{
			max-width: 230px;
		} */
		@media screen and (max-width: 768px) {
			html body .content .content-wrapper {
				padding: 1rem 0;
			}

			.subscription-card-header {
				padding: 20px 15px 0;
			}


			/* .form-control{
				padding: 0;
				height: calc(1.25em + 0.5rem + 2px);
			} */

			.heading-elements-toggle {
				display: none;
			}

			.card-body {
				padding: 0;
			}

			.card-product {
				height: 50px;
				border-bottom: 1px solid rgb(6 6 6 / 15%);
				padding: 10px 15px;
				position: relative;
			}

		}

.image-container {
    position: relative;
    display: flex
;
    justify-content: center;
    align-items: center;
    height: 200px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    width: 55% I !important;
}

		@media screen and (max-width: 500px) {
			.wallet-amount-parent {
				flex-direction: column;
				width: 100%;
			}

			.mobile-mb-10 {
				margin-bottom: 10px !important;
			}

			.mobile-display-block {
				display: block !important;
				margin-top: 28px !important;
			}
		}

		@media (max-width: 767.98px) {
			#myTab .nav-link {
				flex-direction: column;
				font-size: 12px;
				padding: 0.5rem;
				text-align: center;
			}
		}

		/* Cropped image state styling */
		.upload-area.cropped-state {
			background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
			border: 2px solid #667eea;
		}

		.upload-area.cropped-state:hover {
			background: linear-gradient(135deg, #f0f4ff 0%, #e0f0f6 100%);
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
		}

		.modal-lg {
			width: 90%;
			max-width: 1200px;
		}

		.cropped-preview {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 10px;
		}

		.success-message {
			color: #01A750;
			margin-top: 5px;
			font-weight: bold;
		}

		/* Make sure the cropper is visible in the modal */
		#cropperModal #cropperSection {
			display: block !important;
		}

		button {
			color: black !important;
			font-size: 14px !important;
		}
		.upload-section {
    margin-bottom: 30px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 100% !important;
    /* height: 320px; */
}
		/* Responsive wrapper */
.cropper-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

/* Make upload and cropper section full width on smaller screens */
@media (max-width: 1024px) {
    .upload-section,
    .cropper-section {
        width: 100% !important;
    }

    .image-container {
        width: 100%;
        height: auto;
    }

    .preview-section {
        width: 100%;
    }
}

/* Stack image and preview vertically on tablets and below */
@media (max-width: 768px) {
    .cropper-section > div {
        flex-direction: column !important;
        align-items: center !important;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .image-container {
        width: 100% !important;
        height: auto !important;
    }

    .preview-section {
        width: 100%;
        margin-top: 20px;
        text-align: center;
    }

    .preview-section canvas {
        max-width: 100%;
        height: auto;
    }

    .upload-section {
        padding: 15px;
    }
}
@media (max-width: 357px) {
form .form-actions.right {
    text-align: right;
    display: flex;
}
form .form-actions {
    border-top: 1px solid #d1d5ea;
    padding: 20px 0;
    margin-top: 20px;
    display: flex;
}
}
/* Smaller mobile adjustments */
@media (max-width: 576px) {
    .mode-buttons {
        flex-direction: column;
        width: 100%;
    }

    .mode-btn {
        width: 100%;
        text-align: center;
    }

    .action-btn {
        width: 100%;
    }

    .zoom-controls {
        /* flex-direction: column; */
        gap: 5px;
    }

    .zoom-slider {
        width: 100%;
    }

    .upload-content p {
        font-size: 1rem;
    }

    .upload-content .file-types {
        font-size: 0.8rem;
    }

    .controls {
        padding: 10px;
        gap: 15px;
    }

	button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled) {
    cursor: pointer;
    font-size: 14px  !important;
}
}
@media (max-width: 576px) {
     .cropper-section {
        width: 10% !important;
    }
}


	</style>
</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

	<!-- include header-->
	<?php include('includes/header.php'); ?>

	<!-- BEGIN: Content-->
	<div class="app-content content">
		<div class="content-overlay"></div>
		<div class="content-wrapper">
			<div class="content-header row">
				<div class="col-md-12 mb-2">
					<h3 class="content-header-title">Account Settings</h3>
					<div class="row breadcrumbs-top">
						<div class="breadcrumb-wrapper col-12">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="Dashboard.html">Dashboard</a>
								</li>
								<li class="breadcrumb-item active">Account Settings
								</li>
							</ol>
						</div>
					</div>
				</div>
				<div class="col-md-12">
					<!--- show status messages --->
					<?php
					if (isset($_GET["status"])) {
						if ($_GET["status"] == "added") {
					?>
							<div class="alert alert-success alert-dismissible mt-2" role="alert">
								<button type="button" class="close" data-dismiss="alert" aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
								<strong>Added.</strong>
							</div>

						<?php
						} else if ($_GET["status"] == "updated") {
						?>
							<div class="alert alert-success alert-dismissible mt-2" role="alert">
								<button type="button" class="close" data-dismiss="alert" aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
								<strong>Updated.</strong>
							</div>
						<?php
						}
					}
					if (isset($_GET["Walletstatus"])) {
						if ($_GET["Walletstatus"] == "updated") {

						?>
							<div class="alert alert-success alert-dismissible mt-2" role="alert">
								<button type="button" class="close" data-dismiss="alert" aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
								<strong>Credit Added to Account.</strong>
							</div>

					<?php


						}
					}
					?>
				</div>

			</div>
			<div class="content-body">
				<!-- account setting page start -->
				<section id="page-account-settings">

					<input type="hidden" name="currentPlanId" id="currentPlanId" value="<?php echo ($bPlanId); ?>">
					<input type="hidden" name="currentPlanSetupFee" id="currentPlanSetupFee" value="<?php echo ($price); ?>">
					<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
					<input type="hidden" name="businessPlanDetailId" id="businessPlanDetailId" value="<?php echo ($businessPlanDetailId); ?>">
					<div class="row">
						<!-- left menu section -->
						<div class="col-12 col-md-3 mb-2 mb-md-0">
							<ul class="nav nav-tabs d-flex d-md-block flex-row flex-md-column justify-content-around" id="myTab">
								<li class="nav-item">
									<a class="nav-link d-flex align-items-center active" id="account-pill-general" data-toggle="tab" href="#account-vertical-general">
										<i class="ft-user mr-1"></i>
										<span class="d-none d-md-inline">
											<?php echo ($_SESSION["isPrimary"] == 1) ? "Account Owner Details" : "Profile Details"; ?>
										</span>
									</a>
								</li>

								<?php if ($_SESSION["isPrimary"] == 1) { ?>
									<li class="nav-item">
										<a class="nav-link d-flex align-items-center" id="business-pill-general" data-toggle="tab" href="#business-vertical-general">
											<i class="ft-layers mr-1"></i>
											<span class="d-none d-md-inline">Business Details</span>
										</a>
									</li>
								<?php } ?>

								<li class="nav-item">
									<a class="nav-link d-flex align-items-center" id="account-pill-password" data-toggle="tab" href="#account-vertical-password">
										<i class="ft-lock mr-1"></i>
										<span class="d-none d-md-inline">Change Password</span>
									</a>
								</li>

								<?php if ($_SESSION["isPrimary"] == 1) { ?>
									<li class="nav-item">
										<a class="nav-link d-flex align-items-center" id="account-pill-social" data-toggle="tab" href="#account-vertical-social">
											<i class="ft-camera mr-1"></i>
											<span class="d-none d-md-inline">Social Links</span>
										</a>
									</li>

									<li class="nav-item">
										<a class="nav-link d-flex align-items-center" id="account-pill-connections" data-toggle="tab" href="#account-vertical-connections">
											<i class="ft-thumbs-up mr-1"></i>
											<span class="d-none d-md-inline">Subscription</span>
										</a>
									</li>

									<li class="nav-item">
										<a class="nav-link d-flex align-items-center" id="wallet-pill-connections" data-toggle="tab" href="#wallet-vertical-connections">
											<i class="ft-credit-card mr-1"></i>
											<span class="d-none d-md-inline">Wallet</span>
										</a>
									</li>

									<li class="nav-item">
										<a class="nav-link d-flex align-items-center" id="addon-pill-connections" data-toggle="tab" href="#addon-vertical-connections">
											<i class="fa fa-puzzle-piece mr-1"></i>
											<span class="d-none d-md-inline">Addons</span>
										</a>
									</li>
								<?php } ?>
							</ul>
						</div>

						<!-- right content section -->
						<div class="col-md-9">
							<input type="hidden" name="businessIds" id="businessIds" value="<?php echo ($businessId); ?>">
							<div class="card">
								<div class="card-content">
									<div class="card-body">
										<div class="tab-content">

											<div role="tabpanel" class="tab-pane active" id="account-vertical-general" aria-labelledby="account-pill-general" aria-expanded="true">
												<form class="form form-horizontal" id="employeeForm" method="post" action="profilesubmit.html" enctype="multipart/form-data">
													<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($employeeId); ?>">
													<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
													<input type="hidden" name="email" id="email" value="<?php echo ($email); ?>">
												
													<input type="hidden" name="usermanagemetId" id="usermanagemetId" value="<?php echo ($usermanagemetId); ?>">
													<h4 class="form-section"><i class="ft-user"></i>Personal Details
													</h4>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">First Name<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="firstName" class="form-control" placeholder="First Name" name="firstName" value="<?php echo ($firstName); ?>" required>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="lastName">Last Name<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="lastName" class="form-control" placeholder="Last Name" name="lastName" value="<?php echo ($lastName); ?>" required>
																</div>
															</div>
														</div>
													</div>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="address">Address 1</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="address" class="form-control" placeholder="Address 1" name="address" value="<?php echo ($address); ?>">
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="address">Address 2</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="address2" class="form-control" placeholder="Address 2" name="address2" value="<?php echo ($address2); ?>">
																</div>
															</div>
														</div>

													</div>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="city">City</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="city" class="form-control" placeholder="City" name="city" value="<?php echo ($city); ?>" oninput="charactersOnly(this.id);">
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">State</label>
																<div class="col-md-9 mx-auto">
																	<select id="stateId" name="stateId" class="select2 form-control step2" required>
																		<option value="" selected>Select</option>
																	</select>
																</div>
															</div>
														</div>

													</div>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="city">Zip
																	Code</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="zipCode" class="form-control" placeholder="Zip Code" name="zipCode" value="<?php echo ($zipCode); ?>" oninput="numberOnly(this.id);">
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput3">Country</label>
																<div class="col-md-9 mx-auto">
																	<select id="cboCountry" name="cboCountry" class="select2 form-control step1">
																		<option value="" selected>Select</option>
																		<?php

																		if ($countries != "") {
																			foreach ($countries as $row) {
																				$location_id  = $row['location_id'];
																				$name  = stripslashes($row['name']);

																		?>
																				<option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>>
																					<?php echo ($name); ?>
																				</option>
																		<?php

																			}
																		}
																		?>
																	</select>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="phoneNo">Mobile No.<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="phoneNo" class="form-control phone-inputmask" placeholder="Mobile No." name="phoneNo" value="<?php echo ($phoneNo); ?>" required>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="email">Email</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="email" class="form-control" name="email" value="<?php echo ($email); ?>" readonly>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="birthDate">Birth Date</label>
																<div class="col-md-9 mx-auto">
																	<div class="input-group date-picker-class">
																		<input type="text" class="form-control dateValidation" id="birthDate" name="birthDate" value="<?php echo ($birthDate); ?>">
																		<div class="input-group-append">
																			<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
													<h4 class="form-section"><i class="ft-image"></i> Account Profile Image</h4>
													<div class="row">
														<!-- <div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control">Image</label>
																<div class="col-md-9 mx-auto">
																	<fieldset class="form-group">
																		<div class="custom-file">
																			<input type="file" class="custom-file-input" id="inputGroupFile02" name="employeeImage" onchange="browseProfileImage(this);" value="" accept=".png, .gif, .jpeg, .jpg">
																			<label class="custom-file-label" for="inputGroupFile02" aria-describedby="inputGroupFile02">
																				<?php echo ($imageName); ?>
																			</label>
																		</div>
																	</fieldset>
																</div>
															</div>
														</div> -->
															<div class="cropper-container cropper-instance-1">
																<input type="hidden" name="imageName" id="imageName" class="fileLogo" value="<?php echo ($imageName); ?>">
																<input type="hidden" class="hasCroppedImage" value="0">
																<section class="upload-section" >
																	<div class="upload-area">
																		<input type="file" class="fileInput" accept="image/*" hidden>
																		<div class="upload-content">
																			<svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
																				<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
																				<polyline points="7,10 12,15 17,10"></polyline>
																				<line x1="12" y1="15" x2="12" y2="3"></line>
																			</svg>
																			<p>Click to browse or drag and drop an image</p>
																			<span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
																		</div>
																	</div>
																</section>

																<section class="cropper-section" style="display: none;">
																	<div class="controls">
																		<div class="control-group">
																			<div class="mode-buttons">
																				<button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
																				<button type="button" class="mode-btn" data-mode="square">Square</button>
																			</div>
																		</div>
																		<div class="control-group">
																			<button type="button" class="action-btn resetBtn">Reset</button>
																			<button type="button" class="action-btn backBtn">Back</button>
																			<button type="button" class="action-btn bgRemovelToggle" style="display: none;">Remove Background</button>
																			<button type="button" class="action-btn primary cropBtn" style="display: none;">Crop & Save</button>
																		</div>
																		<div class="control-group">
																			<label>Zoom:</label>
																			<div class="zoom-controls">
																				<button type="button" class="zoom-btn zoomOut">-</button>
																				<span class="zoom-level zoomLevel">100%</span>
																				<button type="button" class="zoom-btn zoomIn">+</button>
																				<input type="range" class="zoom-slider zoomSlider" min="20" max="300" value="100">
																			</div>
																		</div>
																	</div>
																	<div style="display: flex; gap: 20px; align-items: flex-start;">
																		<div class="image-container">
																			<img class="previewImage" src="" alt="Preview">
																			<div class="crop-overlay">
																				<div class="crop-selection"></div>
																			</div>
																		</div>
																		<div class="preview-section">
																			<h4>Cropped Preview</h4>
																			<canvas class="previewCanvas" name="previewCanvas"></canvas>
																		</div>
																	</div>
																</section>
															</div>

															<div class="col-lg-6">
															</div>
															<div class="col-lg-6">
																<div class="form-group row">
																	<label class="col-md-3 label-control"></label>
																	<div class="col-md-4 mx-auto" style="object-fit: contain;">
																		<!-- <div class="col-md-4 mx-auto" style="object-fit: contain;"> -->
																		<!-- <?php if ($employeeImagePath != '') { ?>
																			<img id="profileimg" class="circular--square w-100" src="<?php echo ($employeeImagePath); ?>">
																		<?php } ?> -->
																		<!-- </div> -->
																	</div>
																</div>
															</div>
													
													</div>
													<div class="form-actions text-right">
														<a href="employee.html">
															<button type="button" class="btn btn-outline-warning btn-glow mr-1">
																<i class="ft-x"></i> Cancel
															</button>
														</a>
														<button type="submit" class="btn btn-outline-primary btn-glow">
															<i class="la la-check-square-o"></i>
															<?php echo ($buttonTitle); ?>
														</button>
													</div>
												</form>
											</div>
											<div role="tabpanel" class="tab-pane" id="business-vertical-general" aria-labelledby="business-pill-general" aria-expanded="true">
												<form class="form form-horizontal" id="businessDetailsForm" method="post" action="updatebusinessinfosubmit.html" enctype="multipart/form-data">
													<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($employeeId); ?>">
													<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
													<input type="hidden" name="email" id="email" value="<?php echo ($email); ?>">
													<h4 class="form-section"><i class="ft-user"></i>Business Details
													</h4>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">Business Name<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="businessName" class="form-control" placeholder="Business Name" name="businessName" value="<?php echo ($businessName); ?>" required>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="lastName">Business Code<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="businessCode" class="form-control" placeholder="Code" name="businessCode" value="<?php echo ($businessCode); ?>" required readonly>
																</div>
															</div>
														</div>
													</div>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="address">Address</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="businessaddress" class="form-control" placeholder="Address" name="businessaddress" value="<?php echo ($businessaddress); ?>">
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="city">City</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="businesscity" class="form-control" placeholder="City" name="businesscity" value="<?php echo ($businesscity); ?>">
																</div>
															</div>
														</div>
													</div>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">State</label>
																<div class="col-md-9 mx-auto">
																	<select id="businessdbStateId" name="businessdbStateId" class="select2 form-control step4" required>
																		<option value="" selected>Select</option>
																	</select>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="city">Zip
																	Code</label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="businesszipCode" class="form-control" placeholder="Zip Code" name="businesszipCode" value="<?php echo ($businesszipCode); ?>">
																</div>
															</div>
														</div>
													</div>
													<div class="row">
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput3">Country</label>
																<div class="col-md-9 mx-auto">
																	<select id="cboCountry1" name="cboCountry1" class="select2 form-control step3">
																		<option value="" selected>Select</option>
																		<?php

																		if ($countries != "") {
																			foreach ($countries as $row) {
																				$location_id  = $row['location_id'];
																				$name  = stripslashes($row['name']);

																		?>
																				<option value="<?php echo ($location_id); ?>" <?php if ($businesscountryId == $location_id) {
																																?> selected="true" <?php } ?>>
																					<?php echo ($name); ?>
																				</option>
																		<?php

																			}
																		}
																		?>
																	</select>
																</div>
															</div>
														</div>
														<div class="col-lg-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="businessphoneNo">Mobile No.<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<input type="text" id="businessphoneNo" class="form-control phone-inputmask" placeholder="Mobile No." name="businessphoneNo" value="<?php echo ($businessphoneNo); ?>" required>
																</div>
															</div>
														</div>
													</div>

													<h4 class="form-section"><i class="ft-image"></i> Business Logo</h4>
													<div class="row">
														<div class="col-lg-12">
															<div class="form-group row">
																<label class="col-md-3 label-control" style="display: none;">Logo</label>
																<!-- <div class="col-md-9 mx-auto">
																	<fieldset class="form-group">
																		<div class="custom-file">
																			<input type="file" class="custom-file-input" id="inputGroupFile02" name="businessImage">
																			<label class="custom-file-label" for="inputGroupFile02" aria-describedby="inputGroupFile02">Browse
																				Logo</label>
																		</div>
																	</fieldset>
																</div> -->
																<div class="cropper-container cropper-instance-2">
																	<input type="hidden" name="businesslogoName" id="businesslogoName" class="fileLogo" value="<?php echo ($businesslogoName); ?>">
																	<input type="hidden" class="hasCroppedImage" value="0">


																	<section class="upload-section" style="width: 37%">
																		<div class="upload-area">
																			<input type="file" class="fileInput" accept="image/*" hidden>
																			<div class="upload-content">
																				<svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
																					<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
																					<polyline points="7,10 12,15 17,10"></polyline>
																					<line x1="12" y1="15" x2="12" y2="3"></line>
																				</svg>
																				<p>Click to browse or drag and drop an image</p>
																				<span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
																			</div>
																		</div>
																	</section>

																	<section class="cropper-section" style="display: none;">
																		<div class="controls">
																			<div class="control-group">
																				<div class="mode-buttons">
																					<button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
																					<button type="button" class="mode-btn" data-mode="square">Square</button>
																				</div>
																			</div>
																			<div class="control-group">
																				<button type="button" class="action-btn resetBtn">Reset</button>
																				<button type="button" class="action-btn backBtn">Back</button>
																				<button type="button" class="action-btn bgRemovelToggle" style="display: none;">Remove Background</button>
																				<button type="button" class="action-btn primary cropBtn" style="display: none;">Crop & Save</button>
																			</div>
																			<div class="control-group">
																				<label>Zoom:</label>
																				<div class="zoom-controls">
																					<button type="button" class="zoom-btn zoomOut">-</button>
																					<span class="zoom-level zoomLevel">100%</span>
																					<button type="button" class="zoom-btn zoomIn">+</button>
																					<input type="range" class="zoom-slider zoomSlider" min="20" max="300" value="100">
																				</div>
																			</div>
																		</div>
																		<div style="display: flex; gap: 20px; align-items: flex-start;">
																			<div class="image-container">
																				<img class="previewImage" src="" alt="Preview">
																				<div class="crop-overlay">
																					<div class="crop-selection"></div>
																				</div>
																			</div>
																			<div class="preview-section">
																				<h4>Cropped Preview</h4>
																				<canvas class="previewCanvas" name="previewCanvas"></canvas>
																			</div>
																		</div>
																	</section>
																</div>
																<div class="col-lg-6"></div>
																<div class="col-lg-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control"></label>
																		<div class="col-md-9 mx-auto">
																			<?php if ($businessLogo != '') { ?>
																				<!-- <img class="img-fluid" src="<?php //echo ($businessLogo); ?>"> -->
																			<?php } ?>
																		</div>
																	</div>
																</div>

															</div>
																	
														</div>
														<div class="form-actions text-right">
															<a href="employee.html">
																<button type="button" class="btn btn-outline-warning btn-glow mr-1">
																	<i class="ft-x"></i> Cancel
																</button>
															</a>
															<button type="submit" class="btn btn-outline-primary btn-glow">
																<i class="la la-check-square-o"></i>
																<?php echo ($buttonTitle); ?>
															</button>
														</div>
													</div>
												</form>
											</div>
											<div class="tab-pane fade " id="account-vertical-password" role="tabpanel" aria-labelledby="account-pill-password" aria-expanded="false">
												<input type="hidden" name="isPrimaryUser" id="isPrimaryUser" value="<?php echo $_SESSION[" isPrimaryUser"]; ?>">

												<?php if ($_SESSION["isPrimaryUser"] == 1) { ?>
													<form class="form form-horizontal" id="changePasswordForm" method="post" action="changepasswordsubmit.html" enctype="multipart/form-data">
														<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
														<h4 class="form-section"><i class="ft-lock"></i>Change Password</h4>
														<div class="row">
															<div class="col-md-8">
																<div class="form-group row">
																	<label class="col-md-3 label-control" for="userinput4">New Password<span class="validate-field">*</span></label>
																	<div class="col-md-9 mx-auto">
																		<input type="password" id="txtNewPassword" class="form-control" placeholder="New password" name="txtNewPassword" required>
																	</div>
																</div>
															</div>
															<div class="col-md-8">
																<div class="form-group row">
																	<label class="col-md-3 label-control" for="lastName">Confirm Password<span class="validate-field">*</span></label>
																	<div class="col-md-9 mx-auto">
																		<input type="password" id="txtConfirmPassword" class="form-control" placeholder="Confirm password" name="txtConfirmPassword" required>
																	</div>
																</div>
															</div>
														</div>
														<div class="form-actions text-right">
															<a href="dashboard.html">
																<button type="button" class="btn btn-outline-warning btn-glow mr-1">
																	<i class="ft-x"></i> Cancel
																</button>
															</a>
															<button type="submit" class="btn btn-outline-primary btn-glow">
																<i class="la la-check-square-o"></i> Update
															</button>
														</div>
													</form>
												<?php } else { ?>
													<div class="mt-5 text-center mb-5">
														<p><strong>To Change Password, Please login as your primary business
																account. - "
																<?php echo $primaryBusinessName; ?>"
															</strong></p>
													</div>
												<?php } ?>
											</div>
											<div class="tab-pane fade " id="account-vertical-social" role="tabpanel" aria-labelledby="account-pill-social" aria-expanded="false">
												<form class="form form-horizontal" id="socialMediaForm" method="post" action="updatebusinesssocialmedia.html" enctype="multipart/form-data">
													<input type="hidden" name="businessId" value="<?php echo ($businessId); ?>">
													<h4 class="form-section"><i class="ft-camera"></i>Social Media</h4>
													<div class="row col-sm-12">
														<div class="col-md-12">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">Facebook</label>
																<div class="col-md-9 mx-auto input-group">
																	<div class="input-group-prepend">
																		<span class="input-group-text bg-facebook border-facebook white" id="basic-addon7"><span class="la la-facebook font-medium-4"></span></span>
																	</div>
																	<input type="text" class="form-control url-input" id="facebook" name="facebook" value="<?php echo ($facebook); ?>" placeholder="https://" aria-describedby="basic-addon7">
																</div>
															</div>
														</div>
														<div class="col-md-12">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">Twitter</label>
																<div class="col-md-9 mx-auto input-group">
																	<div class="input-group-prepend">
																		<span class="input-group-text bg-twitter border-twitter  white" id="basic-addon7"><span class="la la-twitter font-medium-4"></span></span>
																	</div>
																	<input type="text" class="form-control url-input" id="twitter" name="twitter" value="<?php echo ($twitter); ?>" placeholder="https://" aria-describedby="basic-addon7">
																</div>
															</div>
														</div>
														<div class="col-md-12">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">Linkedin</label>
																<div class="col-md-9 mx-auto input-group">
																	<div class="input-group-prepend">
																		<span class="input-group-text bg-primary  border-primary  white" id="basic-addon7"><span class="la la-linkedin font-medium-4"></span></span>
																	</div>
																	<input type="text" class="form-control url-input" id="linked_in" name="linked_in" value="<?php echo ($linked_in); ?>" placeholder="https://" aria-describedby="basic-addon7">
																</div>
															</div>
														</div>
														<div class="col-md-12">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">YouTube</label>
																<div class="col-md-9 mx-auto input-group">
																	<div class="input-group-prepend">
																		<span class="input-group-text bg-danger border-danger white" id="basic-addon7"><span class="la la-youtube font-medium-4"></span></span>
																	</div>
																	<input type="text" class="form-control url-input" id="youtube" name="youtube" value="<?php echo ($youtube); ?>" placeholder="https://" aria-describedby="basic-addon7">
																</div>
															</div>
														</div>
														<div class="col-md-12">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="userinput4">Instagram</label>
																<div class="col-md-9 mx-auto input-group">
																	<div class="input-group-prepend">
																		<span class="input-group-text bg-primary border-primary white" id="basic-addon7"><span class="la la-instagram font-medium-4"></span></span>
																	</div>
																	<input type="text" class="form-control url-input" id="instagram" name="instagram" value="<?php echo ($instagram); ?>" placeholder="https://" aria-describedby="basic-addon7">
																</div>
															</div>
														</div>
													</div>
													<div class="form-actions right">
														<a href="employee.html">
															<button type="button" class="btn btn-outline-warning btn-glow mr-1">
																<i class="ft-x"></i> Cancel
															</button>
														</a>
														<button type="submit" id="btnSave" name="btnSave" class="btn btn-outline-primary btn-glow">
															<i class="la la-check-square-o"></i>
															<?php echo ($buttonTitle); ?>
														</button>
													</div>
												</form>
											</div>

											<div class="tab-pane fade bg-gradient-y-success" id="account-vertical-connections" role="tabpanel" aria-labelledby="account-pill-connections" aria-expanded="false">
												<form id="frmUpdateScubscription" class="form-horizontal form-label-left" action="plansubscriptiondetailssubmit.html" method="post" enctype="multipart/form-data">
													<input type="hidden" name="planId" value="<?php echo ($planId); ?>">
													<input type="hidden" id="detailId" name="detailId" value="<?php echo ($businessPlanDetailId); ?>">
													<input type="hidden" name="businessId" value="<?php echo ($businessId); ?>">
													<div class="row">
														<div class="text-white card-head-inverse box-shadow-0 bg-gradient-y-success" style="width:100%">
															<div class=" row col-sm-12 card-header subscription-card-header">
																<div class="">
																	<h4 class="card-title text-white mobile-mb-10">Subscription
																		Details</h4>
																	<a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
																</div>
																<div class="wallet-amount-parent">
																	<h4 class="card-title text-white" style="margin-top: 5px;"><i class="ft-folder"></i> Wallet Amount: $ <?php echo ($walletAmount); ?></h4>

																	<a href="javascript:void(0)" id="subscribePlan" name="subscribePlan" class="btn btn-sm btn-warning text-white btn-glow pull-right">Subscribe
																		Plan</a>
																</div>
															</div>
															<div class="card-content collapse show">
																<div class="card-body">
																	<p class="card-text">
																	<div class="row form-section" style="border-bottom: none !important;">
																		<div class="col-md-6 p-0 header-border-bottom mobile-mb-10">
																			<h4 class="text-white">Your Current Plan
																			</h4>
																		</div>
																		<div class="col-md-6 p-0 header-border-bottom">
																			<div class="row">
																				<label style="line-height: 1.2; margin: 8px 0;" class="col-md-4 p-0 label-control text-white" for="title">View Plan Details:
																				</label>
																				<div class="col-md-8 col-sm-4 p-0">
																					<select id="businessPlanId" name="businessPlanId" class="form-control input-md  select2_single" required>
																						<?php
																						if ($planmasters != "") {
																							while ($row = mysqli_fetch_assoc($planmasters)) {
																								$planMasterId  = $row['planId'];
																								$name  = stripslashes($row['title']);
																						?>
																								<option value="<?php echo ($planMasterId); ?>" <?php
																																				if ($bPlanId == $planMasterId) {
																																				?> selected="true" class="bg-info" <?php } ?>>
																									<?php echo ($name); ?>
																								</option>
																						<?php
																							}
																						}
																						?>
																					</select>
																				</div>
																			</div>
																		</div>
																	</div>
																	<div class="row">
																		<div class="col-sm-12 row padding-x-0">
																			<div class="col-sm-12 form-section padding-x-0">
																				<h4 class=" text-white" style="line-height: inherit;"><i class="ft-thumbs-up text-white"></i>Basic
																					Details</h4>

																			</div>
																			<?php if ($planId) { ?>
																				<div class="col-sm-6 form-section">
																					<!-- <div class="col-lg-6 "> -->
																					<p class="text-white"><strong>Start Date : <span style="color:#00000080;"><?php echo ($subscriptionStartDate);  ?> </span></strong></p>
																				</div>
																				<div class="col-lg-6 form-section">
																					<p class="text-white"><strong>End Date : <span style="color:#00000080;"><?php echo  ($subscriptionEndDate); ?></span></strong></p>
																				</div>
																		</div>
																	<?php } ?>

																	<div class="col-lg-6">
																		<div class="form-group row">
																			<label class="control-label text-white w-half padding-x-5" for="title">Title:</label>
																			<div class="w-half padding-x-5">
																				<input id="title" name="title" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" required>
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6 transactionFileds">
																		<div class="form-group row skin">
																			<div class="control-label w-half padding-x-5 " for="price">
																				<label class="main text-white ">
																					<input class="setup-fee-checkbox mr-1 isSetupFeeClass" type="checkbox" name="isSetupFee" id="isSetupFee" value="<?php echo ($isSetupFee); ?>" <?php if ($isSetupFee == 1) { ?> checked <?php } ?>>Setup Fee:
																					<span class="geekmark"></span>
																				</label>
																			</div>

																			<div class="w-half padding-x-5">
																				<input min="0" id="price" name="price" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																			</div>


																		</div>
																	</div>
																	<div class="col-lg-6 transactionFileds">
																		<div class="form-group row skin skin-square">
																			<label class="control-label w-half padding-x-5 text-white" for="quarterlyAmount"><input type="radio" name="paymentPlan" value="1" class="custom-control-input bg-info" <?php if ($paymentPlan == 1) { ?> checked <?php } ?> duration="3">Quarterly:
																			</label>
																			<div class="w-half padding-x-5">
																				<span id="quarterlyAmount" style="color:#3a4d81;margin-left:15px;"></span>
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6 transactionFileds">
																		<div class="form-group row skin skin-square">
																			<label class="control-label w-half padding-x-5 text-white" for="semiAnnualAmount"><input type="radio" name="paymentPlan" value="2" class="custom-control-input bg-info" <?php if ($paymentPlan == 2) { ?> checked <?php } ?> duration="6">Semi
																				Annual:
																			</label>
																			<div class="w-half padding-x-5">
																				<span id="semiAnnualAmount" style="color:#3a4d81;margin-left:15px;"></span>
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6 transactionFileds">
																		<div class="form-group row skin skin-square">
																			<label class="control-label w-half padding-x-5 text-white" for="annualAmount"><input type="radio" name="paymentPlan" value="3" class="custom-control-input bg-info" <?php if ($paymentPlan == 3) { ?> checked <?php } ?> duration="12">Annually:
																			</label>
																			<div class="w-half padding-x-5">
																				<span id="annualAmount" style="color:#3a4d81;margin-left:15px;"></span>

																			</div>
																		</div>
																	</div>

																	<div class="col-lg-6">
																		<div class="form-group row">
																			<label class="control-label w-half padding-x-5 text-white" for="contactLimit">Contact
																				Limit</label>
																			<div class="w-half padding-x-5">
																				<input min="1" id="contactLimit" name="contactLimit" type="text" style="background-color:#2fbd8b00;border:none;" readonly placeholder="" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6">
																		<div class="form-group row">
																			<label class="control-label w-half padding-x-5 text-white" for="cardLimit">Card
																				Limit</label>
																			<div class="w-half padding-x-5">
																				<input min="1" id="cardLimit" name="cardLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6">
																		<div class="form-group row">
																			<label class="control-label w-half padding-x-5 text-white" for="subCardLimit">Sub Card
																				Limit</label>
																			<div class="w-half padding-x-5">
																				<input min="0" id="subCardLimit" name="subCardLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6">
																		<div class="form-group row">
																			<label class="control-label w-half padding-x-5 text-white" for="formLimit">Form
																				Limit</label>
																			<div class="w-half padding-x-5">
																				<input min="0" id="formLimit" name="formLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																			</div>
																		</div>
																	</div>
																	</div>
																	<div class="col-sm-12 padding-x-0">
																		<div class="col-sm-12 p-0">
																			<h4 class="form-section text-white"><i class="ft-plus-square text-white"></i>Gallery
																			</h4>
																		</div>
																		<div class="row">
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="imageCarouselLimit">Album
																						Limit</label>
																					<div class="w-half padding-x-5">
																						<input min="0" id="imageCarouselLimit" name="imageCarouselLimit" type="text" style="background-color:#2fbd8b00;border:none;" readonly placeholder="" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="galleryImageLimit">Gallery
																						Limit</label>
																					<div class="w-half padding-x-5">
																						<span id="galleryImageLimit" style="background-color:#2fbd8b00;border:none;color:black;"></span>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																	<div class="col-sm-12 padding-x-0">
																		<div class="col-sm-12 p-0">
																			<h4 class="form-section text-white"><i class="ft-plus-square text-white"></i>Infographic
																			</h4>
																		</div>
																		<div class="row" style="display:none;">
																			<div class="col-md-12">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="imageInfographSectionLimit">Image
																						Info. Sections Limit</label>
																					<div class="w-half padding-x-5">
																						<input min="0" id="imageInfographSectionLimit" name="imageInfographSectionLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																					</div>
																				</div>
																			</div>
																			<div class="col-md-12">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="imageInfographLimit">Image
																						Content Limit</label>
																					<div class="w-half padding-x-5">
																						<input min="0" id="imageInfographLimit" name="imageInfographLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																					</div>
																				</div>
																			</div>
																			<div class="col-md-12">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="videoInfographSectionLimit">Video
																						Info. Sections Limit</label>
																					<div class="w-half padding-x-5">
																						<input min="0" id="videoInfographSectionLimit" name="videoInfographSectionLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																					</div>
																				</div>
																			</div>
																			<div class="col-md-12">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="videoInfographLimit">Video
																						Content Limit</label>
																					<div class="w-half padding-x-5">
																						<input min="0" id="videoInfographLimit" name="videoInfographLimit" type="text" placeholder="" style="background-color:#2fbd8b00;border:none;" readonly class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
																					</div>
																				</div>
																			</div>
																		</div>
																		<div class="row">
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="imageCarouselLimit">Sections
																						Limit</label>
																					<div class="w-half padding-x-5">
																						<span name="infoSectionLiimit" style="color:black;" id="infoSectionLiimit"></span>
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="imageCarouselLimit">Content
																						Limit</label>
																					<div class="w-half padding-x-5">
																						<span name="infoContentLimit" style="color:black;" id="infoContentLimit"></span>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																	<div class="col-sm-12 padding-x-0">
																		<div class="col-sm-12 p-0">
																			<h4 class="form-section text-white"><i class="ft-plus-square text-white"></i>SMS/Email
																			</h4>
																		</div>
																		<div class="row">
																			<div class="col-lg-6" id="Dfsmsdiv">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="smsLiimit">SMS Limit</label>
																					<div class="w-half padding-x-5">
																						<span name="smsdfUsed" class="" data-toggle="tooltip" data-placement="top" title="Used SMS Count" style="color:black;" id="smsdfUsed"></span>
																						<span name="smsLiimit" class="" style="color:black;" id="smsLiimit"></span>
																					</div>
																				</div>
																			</div>
																																						<div class="col-lg-6" id="DfEmaildiv">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="emaillLimit">Email Limit</label>
																					<div class="w-half padding-x-5">
																						<span name="emaildfUsed" class="" data-toggle="tooltip" data-placement="top" title="Used Email Count" style="color:black;" id="emaildfUsed"></span>
																						<span name="emaillLimit" class="" style="color:black;" id="emaillLimit"></span>
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6" id="smsdiv">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="smsLiimit">SMS Purchased Limit</label>
																					<div class="w-half padding-x-5">
																						<span name="smsPurUsed" class="" data-toggle="tooltip" data-placement="top" title="Used SMS Count" style="color:black;" id="smsPurUsed"></span>
																						<span name="smsPurCount" class="" style="color:black;" id="smsPurCount"></span>
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6" id="emaildiv">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="emaillLimit">Email Purchased Limit</label>
																					<div class="w-half padding-x-5">
																						<span name="EmailpurUsed" class="" data-toggle="tooltip" data-placement="top" title="Used Email Count" style="color:black;" id="EmailpurUsed"></span>
																						<span name="EmailpurchasedCount" class="" style="color:black;" id="EmailpurchasedCount"></span>
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6" id="totalSmsdiv">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="Totalsms">Total</label>
																					<div class="w-half padding-x-5">
																						<span name="totalSMSUsed" class="" data-toggle="tooltip" data-placement="top" title="Total Used SMS Count" style="color:black;" id="totalSMSUsed"></span>
																						<span name="totalsms" class="" style="color:black;" id="totalsms"></span>
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6" id="totalEmaildiv">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="TotalEmail">Total</label>
																					<div class="w-half padding-x-5">
																						<span name="totalEmailUsed" class="" data-toggle="tooltip" data-placement="top" title="Total Used Email Count" style="color:black;" id="totalEmailUsed"></span>
																						<span name="totalEmail" class="" style="color:black;" id="totalEmail"></span>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="row">
																	<div class="col-lg-6 padding-x-0">
																		<h4 class="form-section text-white"><i class="ft-user-check text-white"></i>Themes/Profile
																			Details</h4>
																		<div class="row">
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="imageCarouselLimit">Dedicated
																						URL.</label>
																					<div class="w-half padding-x-5">
																						<span name="isDedicatedURL" style="color:black;" id="isDedicatedURL"></span>
																						<!---input type="text" name="isDedicatedURL" id="isDedicatedURL" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="cardLimit">Edit
																						Theme.</label>
																					<div class="w-half padding-x-5">
																						<span name="allowThemeEditing" style="color:black;" id="allowThemeEditing"></span>
																						<!---input type="text" name="allowThemeEditing" id="allowThemeEditing" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="allowLogoInclusion">Logo
																						Inclusion.</label>
																					<div class="w-half padding-x-5">
																						<span name="allowLogoInclusion" style="color:black;" id="allowLogoInclusion"></span>
																						<!---input type="text" name="allowLogoInclusion" id="allowLogoInclusion" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="contactLimit">Photo
																						Inclusion.</label>
																					<div class="w-half padding-x-5">
																						<span name="allowPhotoInclusion" style="color:black;" id="allowPhotoInclusion"></span>
																						<!---input type="text" name="allowPhotoInclusion" id="allowPhotoInclusion" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="subCardLimit">Video
																						Profile.</label>
																					<div class="w-half padding-x-5">
																						<span name="allowVideoProfile" style="color:black;" id="allowVideoProfile"></span>
																						<!---input type="text" name="allowVideoProfile" id="allowVideoProfile" style="background-color:#2fbd8b00;border:none;margin-top:4px;" readonly---->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="formLimit">White
																						Label</label>
																					<div class="w-half padding-x-5">
																						<span name="isWhiteLabel" style="color:black;" id="isWhiteLabel"></span>
																						<!---input type="text" name="isWhiteLabel" id="isWhiteLabel" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																	<div class="col-lg-6 padding-x-0">
																		<h4 class="form-section text-white"><i class="ft-plus-circle text-white"></i>Additional
																			Services</h4>
																		<div class="row">
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="isCrmIntergration">Allow
																						CRM.</label>
																					<div class="w-half padding-x-5">
																						<span name="isCrmIntergration" style="color:black;" id="isCrmIntergration"></span>
																						<!---input type="text" name="isCrmIntergration" id="isCrmIntergration" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="isPDFSubscription">Allow
																						Paid Contents</label>
																					<div class="w-half padding-x-5">
																						<span name="isPDFSubscription" style="color:black;" id="isPDFSubscription"></span>
																						<!---input type="text" name="isPDFSubscription" id="isPDFSubscription" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="isCouponServices">Coupons/Service.</label>
																					<div class="w-half padding-x-5">
																						<span name="isCouponServices" style="color:black;" id="isCouponServices"></span>
																						<!---input type="text" name="isCouponServices" id="isCouponServices" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="chatRoom">Chat
																						Room</label>
																					<div class="w-half padding-x-5">
																						<span name="chatRoom" style="color:black;" id="chatRoom"></span>
																						<!---input type="text" name="chatRoom" id="chatRoom" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="chatemail">Email</label>
																					<div class="w-half padding-x-5">
																						<span name="chatemail" style="color:black;" id="chatemail"></span>
																						<!---input type="text" style="background-color:#2fbd8b00;border:none;" id="chatemail" readonly style="margin-top:4px;"-->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="smsChk">SMS</label>
																					<div class="w-half padding-x-5">
																						<span name="smsChk" style="color:black;" id="smsChk"></span>
																						<!---input type="text" style="background-color:#2fbd8b00;border:none;" id="chatemail" readonly style="margin-top:4px;"-->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="geolocation">Geolocation</label>
																					<div class="w-half padding-x-5">
																						<span name="geolocation" style="color:black;" id="geolocation"></span>
																						<!---input type="text" name="geolocation" style="background-color:#2fbd8b00;border:none;" readonly id="geolocation" style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="invoice">Invoice</label>
																					<div class="w-half padding-x-5">
																						<span name="invoice" style="color:black;" id="invoice"></span>
																						<!---input type="text" name="invoice" id="invoice"  style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="pushNotification">Push
																						Notification</label>
																					<div class="w-half padding-x-5">
																						<span name="pushNotification" style="color:black;" id="pushNotification"></span>
																						<!---input type="text" name="pushNotification" id="pushNotification" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="probizcaplus">ProBizCa
																						+</label>
																					<div class="w-half padding-x-5">
																						<span name="probizcaplus" style="color:black;" id="probizcaplus"></span>
																						<!---input type="text" name="probizcaplus" id="probizcaplus" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="shoppingCart">Shopping
																						Cart</label>
																					<div class="w-half padding-x-5">
																						<span name="shoppingCart" style="color:black;" id="shoppingCart"></span>
																						<!---input type="text" name="shoppingCart" id="shoppingCart" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="statistics">Statistics</label>
																					<div class="w-half padding-x-5">
																						<span name="statistics" style="color:black;" id="statistics"></span>
																						<!---input type="text" name="statistics" id="statistics" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"--->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="support">Support</label>
																					<div class="w-half padding-x-5">
																						<span name="support" style="color:black;" id="support"></span>
																						<!---input type="text" name="support" id="support" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"-->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="todo">To Do</label>
																					<div class="w-half padding-x-5">
																						<span name="todo" style="color:black;" id="todo"></span>
																						<!---input type="text" name="todo" id="todo" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"-->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="survey">Survey</label>
																					<div class="w-half padding-x-5">
																						<span name="surveychk" style="color:black;" id="surveychk"></span>
																						<!---input type="text" name="todo" id="todo" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"-->
																					</div>
																				</div>
																			</div>
																			<div class="col-lg-6">
																				<div class="form-group row">
																					<label class="control-label w-half padding-x-5 text-white" for="customQrFormchk">Custom QR Form</label>
																					<div class="w-half padding-x-5">
																						<span name="customQrFormchk" style="color:black;" id="customQrFormchk"></span>
																						<!---input type="text" name="todo" id="todo" style="background-color:#2fbd8b00;border:none;" readonly style="margin-top:4px;"-->
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</form>
											</div>
											<!-- wallet --->
											<div class="tab-pane fade" id="wallet-vertical-connections" role="tabpanel" aria-labelledby="wallet-pill-connections" aria-expanded="false">
												<form action="submitwalletpayment.html" id="paymentFrm" class="col-sm-12 card-form" method="POST" enctype="multipart/form-data">
													<div class="card-content collapse show">
														<div class="card-body">
															<div class="row">
																<div class="col-xl-6 col-lg-12">
																	<h4 class="form-section"><i class="ft-plus-square"></i>Add Money To
																		Wallet</h4>
																</div>
																<div class="col-xl-6 col-lg-12">
																	<h4 class="form-section"><i class="ft-folder"></i>Wallet Amount: $
																		<?php echo ($walletAmount); ?>
																	</h4>
																</div>
															</div>
															<div class="row">
																<div class="col-xl-12 col-lg-12">
																	<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($employeeId); ?>">
																	<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
																	<input type="hidden" name="stripeKeys" id="stripeKeys" value="<?php echo ($stripeKeys); ?>">
																	<div class="row">
																		<div class="col-lg-6">
																			<fieldset class="mb-1">
																				<h5>Card Number</h5>
																				<div class="form-group">
																					<div id="card-number" class="field form-control">
																					</div>
																				</div>
																				<div id="paymentResponse" style="color:red;"></div>
																			</fieldset>
																		</div>
																		<div class="col-lg-6">
																			<fieldset class="mb-1">
																				<h5>Amount</h5>
																				<div class="form-group">
																					<!-- <input type="text" class="field form-control card-name" name="amount" id="amount" minlength="2" oninput="numberOnly(this.id);" required> -->
																					<input type="text" class="field form-control card-name" name="amount" id="amount" minlength="2" oninput="numberOnly(this.id); validateAmount(this);" required>
																					<div id="error-message" style="color: red;"></div>
																				</div>
																			</fieldset>
																		</div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">
																			<fieldset class="mb-1">
																				<h5>Expiry Date</h5>
																				<div class="form-group">
																					<div id="card_expiry" class="field form-control">
																					</div>
																				</div>
																			</fieldset>
																		</div>
																		<div class="col-lg-6">
																			<fieldset class="mb-1">
																				<h5>CVC CODE</h5>
																				<div class="form-group">
																					<div id="card_cvc" class="field form-control">
																					</div>
																				</div>
																			</fieldset>
																		</div>
																	</div>
																	<div class="row">
																		<div class="col-md-12">
																			<fieldset class="mb-1">
																				<h5>Description</h5>
																				<div class="form-group">
																					<textarea name="walletdescrition" id="walletdescrition" class="form-control" rows="2">Credit Added to Account</textarea>
																				</div>
																			</fieldset>
																		</div>
																	</div>

																	<div class="row">
																		<div class="col-md-12">
																			<div class="tacbox">
																				<input id="flexCheckChecked" type="checkbox" />
																				<label for="checkbox"> I have accepted and agreed to the <a href="terms.html" target="_blank">Terms and Conditions</a>.</label>
																			</div>
																		</div>
																	</div>
																	<div class="form-actions right">
																		<button type="submit" id="btnSaveWallet" name="btnSave" class="btn btn-outline-primary btn-glow">
																			<i class="la la-check-square-o"></i>
																			<?php echo ($buttonTitle); ?>
																		</button>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</form>
												<hr class="form-horizontal">
												</hr>
												<div class="row">
													<div class="col-xl-6 col-lg-12">
														<h4 class="form-section"><i class="ft-plus-square"></i>Wallet
														</h4>
													</div>
													<div class="col-xl-6 col-lg-12">
														<h4 class="form-section"><i class="ft-folder"></i>Wallet Amount:
															$
															<?php echo ($walletAmount); ?>
														</h4>
													</div>
													<div class="table-responsive mt-2">
														<table class="table table-striped table-bordered default-ordering" id="gallaryTable" width="100%">
															<thead>
																<tr>
																	<th>Date</th>
																	<th class="hide"></th>
																	<th>Description</th>
																	<th class="text-center">Credit</th>
																	<th class="text-center">Debit</th>
																	<th class="text-center">Balance</th>
																</tr>
															</thead>
															<tbody>
																<?php
																if ($allTransactions != '') {
																	$creditAmount = 0;
																	$debitAmount = 0;
																	$totalBalance = 0.00;
																	while ($row = mysqli_fetch_assoc($allTransactions)) {
																		$businessTransactionId = stripslashes($row['businessTransactionId']);
																		$actualAmount = stripslashes($row['actualAmount']);
																		$fees = stripslashes($row['fees']);
																		$amount = stripslashes($row['amount']);
																		$transactionType = stripslashes($row['transactionType']);
																		$walletdescrition = stripslashes($row['walletdescrition']);
																		$createdDate = stripslashes($row['createdDate']);
																		$newDate = str_replace('/', '-', $createdDate);
																		// $balanceDate = date('m/d/Y h:i A', strtotime($newDate));
																		$balanceDate = date('Y/m/d h:i A', strtotime($newDate));
																		$balanceDateShow = date('m/d/Y h:i A', strtotime($newDate));

																		if ($transactionType == 'Cr') {
																			$creditAmount = $amount;
																			$debitAmount = 0.00;
																			$totalBalance = $totalBalance + $amount;
																		}
																		if ($transactionType == 'db') {
																			$debitAmount = $amount;
																			$creditAmount = 0.00;
																			$totalBalance = round($totalBalance - $amount, 2);
																		}
																?>
																		<tr>
																			<td class="hide">
																				<?php echo ($$businessTransactionId); ?>
																			</td>
																			<td>
																				<?php echo ($balanceDateShow); ?>
																			</td>
																			<td>
																				<?php echo ($walletdescrition); ?>
																			</td>
																			<td class="text-center">
																				<?php echo (number_format($creditAmount, 2)); ?>
																			</td>
																			<td class="text-center">
																				<?php echo (number_format($debitAmount, 2)); ?>
																			</td>
																			<td class="text-center">
																				<?php echo (number_format($totalBalance, 2)); ?>
																			</td>
																		</tr>
																<?php
																	}
																}
																?>
															</tbody>
														</table>
													</div>
												</div>
											</div>
											<!-- Addon -->
											<div class="tab-pane fade" id="addon-vertical-connections" role="tabpanel" aria-labelledby="addon-pill-connections" aria-expanded="false">

												<form action="submitwalletpayment.html" id="paymentFrm" class="col-sm-12 card-form" method="POST" enctype="multipart/form-data">
													<div class="card-content collapse show">
														<div class="d-flex justify-content-between mobile-display-block" style="flex-wrap: wrap;">
															<h3 class=""> <span><i class="fa fa-light fa-puzzle-piece"></i></span>
																Subscription Addons</h3>

															<div style="" class="d-flex align-items-center justify-content-end">
																<h5 class="font-weight-500 mb-0 no-wrap">Active Addon:
																</h5>
																<h6 class="font-weight-500 mb-0 d-flex flex-wrap margin-top-2">
																	&nbsp;
																	<div class="sms-count "> SMS- <span>
																			<?php echo $purchaseSMSUsed;  ?>/
																			<?php echo $purchaseSMS; ?>
																			|
																		</span></div>
																	<div class="emails-count font-weight-500 margin-left-3">
																		EMAILS- <span class="">
																			<?php echo $purchaseEmailUsed;  ?>/
																			<?php echo $purchaseEmail; ?>
																		</span></div>
																</h6>
															</div>
														</div>
														<hr class="form-horizontal">
														</hr>

														<div class="card-body px-0">
															<div class="row">
																<div class="card col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 mb-2 px-md-1 px-0 accordion">
																	<div class="subs-card" style="padding-bottom: 0px;">
																		<div class="sms-collapse-div sms-link" type="button" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne" id="accordionExample1">
																			<div class="card-header card-heading" id="headingOne" style="position: relative">

																				<h2 class="mb-0 btn btn-link ">SMS
																				</h2>
																				<i class="fa-solid fa-angle-down smsAddonDownArrow"></i>

																			</div>
																		</div>
																		<div id="collapseOne" class="collapse " aria-labelledby="headingOne" data-parent="#accordionExample1">
																			<div class="card-body py-0 pb-1">
																				<div class="card-type d-flex justify-content-center align-items-center">
																					<h3 class="mb-0">
																						<?php echo $subscriptionType; ?>
																					</h3>
																				</div>
																				<?php
																				if ($smsaddonDtls != '') {
																					while ($row = mysqli_fetch_assoc($smsaddonDtls)) {
																						$addonId = $row['addonId'];
																						$qty = $row['qty'];
																						$amount = $row['amount'];

																				?>



																						<div class="row card-product mx-xl-1 mx-0">
																							<p class="col-5 pb-0 mb-0 px-0 subscription-quantity" style="position: relative;">
																								<?php echo $qty; ?> <span style="color:rgb(6 6 6 / 48%); font-weight: 300;
																							 float: right; position: relative; top: -2px; right: 5px;">|</span>
																							</p>
																							<p class="col-5 pb-0 mb-0 px-0 subscription-price">

																								<span class="doller-symbol">$</span>
																								<?php echo $amount; ?>
																							</p>
																							<a href="javascript:void(0)" class="col-2 btn buy-btn" onclick="subscribeAddon(<?php echo $addonId; ?>,2,<?php echo $amount; ?>,<?php echo $qty; ?>);">Buy</a>

																						</div>
																				<?php }
																				} ?>
																			</div>
																		</div>
																	</div>
																</div>

																<div class="card col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 mb-2 px-md-1 px-0 accordion ">
																	<div class="subs-card" style="padding-bottom: 0px;">
																		<div class="email-collapse-div emails-link" type="button" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="true" aria-controls="collapseTwo" id="accordionExample2">
																			<div class="card-header email-card-heading" id="headingTwo" style="position: relative">
																				<h2 class="mb-0 btn btn-link ">EMAILS</h2>
																				<i class="fa-solid fa-angle-down emailsAddonDownArrow"></i>
																			</div>

																		</div>
																		<div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionExample2">
																			<div class="card-body py-0 pb-1">
																				<div class="card-type d-flex justify-content-center align-items-center">
																					<h3 class="email-subs-pack-tyle mb-0">
																						<?php echo $subscriptionType; ?>
																					</h3>
																				</div>
																				<?php
																				if ($emailaddonDtls != '') {
																					while ($row = mysqli_fetch_assoc($emailaddonDtls)) {
																						$addonId = $row['addonId'];
																						$qty = $row['qty'];
																						$amount = $row['amount'];

																				?>
																						<div class="row card-product mx-xl-1 mx-0">
																							<p class="col-5 pb-0 mb-0 px-0 subscription-quantity" style="position: relative;">
																								<?php echo $qty; ?> <span style="color:rgb(6 6 6 / 48%); font-weight: 300;
																				float: right; position: relative; top: -2px; right: 5px;">|</span>
																							</p>
																							<!-- <div class="vertical-border"></div> -->
																							<p class="col-5 pb-0 mb-0 px-0 subscription-price">
																								<span class="doller-symbol">$</span>
																								<?php echo $amount; ?>
																							</p>
																							<a href="javascript:void(0)" class="col-2 btn buy-btn email-buy-btn" onclick="subscribeAddon(<?php echo $addonId; ?>,1,<?php echo $amount; ?>,<?php echo $qty; ?>);">Buy</a>

																						</div>
																				<?php }
																				} ?>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</form>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</section>
				<!-- account setting page end -->
			</div>
		</div>
	</div>


	<div class="container">

		<div class="modal fade" id="myModal" role="dialog">
			<div class="modal-dialog modal-dialog-centered">

				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">&times;</button>
					</div>
					<div class="modal-body">
						<p>Do you want to apply only Setup Fee? (Current subscribed Plan)</p>
						<p>Setup Fee : <b> $ <?php echo $price; ?> </b></p>
					</div>
					<div class="modal-footer pt-0">
						<button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
						<button type="button" id="btnSetupFee" class="btn btn-success">Pay</button>
					</div>
				</div>

			</div>
		</div>

	</div>



	<!-- END: Content-->

	<!-- BEGIN: Footer-->
	<?php include('includes/footer.php'); ?>
	<?php include('includes/footerJs.php'); ?>
	<?php include('includes/datatableJs.php'); ?>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/custom.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/jquery.cascadingdropdown.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/select/form-select2.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-inputmask.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/ui/jquery.sticky.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/jquery.sparkline.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/toggle/switchery.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/typeahead/typeahead.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/typeahead/bloodhound.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/typeahead/handlebars.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/formatter/formatter.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/maxlength/bootstrap-maxlength.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/card/jquery.card.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-inputmask.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-typeahead.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-formatter.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-maxlength.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/custom-file-input.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/icheck/icheck.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/custom-file-input.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/checkbox-radio.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/moment/moment.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/common.js"></script>
	<!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->


	<!--script src="<?php echo BASE_PATH; ?>/assets/js/scripts/pages/account-setting.js"></script---->
	<script src="https://js.stripe.com/v3/"></script>

	 	<script src="<?php echo BASE_PATH; ?>/assets/cropper.js" type="text/javascript"></script>
 

	 
	
	<script type="text/javascript">
		/*------onchange tab set url href path-----*/
		$(document).ready(() => {
			Test();

			let url = location.href.replace(/\/$/, "");

			if (location.hash) {
				const hash = url.split("#");
				$('#myTab a[href="#' + hash[1] + '"]').tab("show");
				url = location.href.replace(/\/#/, "#");
				history.replaceState(null, null, url);
				setTimeout(() => {
					$(window).scrollTop(0);
				}, 400);
			}

			$('a[data-toggle="tab"]').on("click", function() {
				let newUrl;
				const hash = $(this).attr("href");
				if (hash == "#home") {
					newUrl = url.split("#")[0];
				} else {
					newUrl = url.split("#")[0] + hash;
				}
				newUrl += "/";
				history.replaceState(null, null, newUrl);
			});
		});

		$(".nav-pills .nav-item .nav-link:not(.nav-pills .nav-item.dropdown .nav-link), .dropdown-item").click(function() {
			$(".fade.active").removeClass('active');
		});

		// character only validation
		function charactersOnly(id) {
			var element = document.getElementById(id);
			element.value = element.value.replace(/[^a-zA-Z]/gi, "");
		}

		// number only validation
		function numberOnly(id) {
			var element = document.getElementById(id);
			element.value = element.value.replace(/[^0-9]/gi, "");
			// element.value = element.value.replace(/[^0-9.]/g, ''); // Allow digits and a single decimal point
		}

		// Validate the input amount 
		// function validateAmount(input) {
		//     // Get the input value
		// 	var inputValue = parseFloat(input.value); // Convert input value to a floating-point number

		// 	// Check if the input is a single digit (excluding decimal values)
		// 	if (inputValue >= 0 && inputValue < 10) {
		// 		document.getElementById('error-message').innerHTML = 'Cannot accept single digit value';
		// 		input.setCustomValidity('Cannot accept single digit value');
		//     } else {
		//         document.getElementById('error-message').innerHTML = '';
		//         input.setCustomValidity('');
		//     }

		// 	 // Perform calculations
		// 	var percentage = 6;
		// 	var percentageAmt = (percentage / 100) * inputValue;
		// 	var finalAmt = parseInt(percentageAmt) + parseInt(inputValue);

		// 	// Display the results in the <span> elements
		// 	$('#actualAmt').html('$' + inputValue); // Display with 2 decimal places
		// 	$('#chargesAmt').html('$' + percentageAmt);
		// 	$('#finalAmt').html('$' + finalAmt);

		// }

		function validateAmount(input) {
			// Get the input value
			var inputValue = input.value;

			// Check for more than one decimal point
			if ((inputValue.split('.').length - 1) > 1) {
				document.getElementById('error-message').innerHTML = 'Cannot have multiple decimal points';
				input.setCustomValidity('Cannot have multiple decimal points');
			}
			// Check if the input is a single digit
			else if (/^\d(\.\d+)?$/.test(inputValue)) {
				document.getElementById('error-message').innerHTML = 'Cannot accept single digit value';
				input.setCustomValidity('Cannot accept single digit value');
			} else {
				document.getElementById('error-message').innerHTML = '';
				input.setCustomValidity('');

				// Perform calculations
				var percentage = 6;
				var percentageAmt = (percentage / 100) * parseFloat(inputValue); // Allow decimal values
				var finalAmt = parseFloat(percentageAmt) + parseFloat(inputValue); // Allow decimal values

				if (inputValue) {
					// Display the results in the <span> elements
					$('#actualAmt').html('$' + inputValue); // Display with 2 decimal places
					$('#chargesAmt').html('$' + percentageAmt.toFixed(2));
					$('#finalAmt').html('$' + finalAmt.toFixed(2));
				} else {
					// Display the results in the <span> elements
					$('#actualAmt').html('$00.00'); // Display with 2 decimal places
					$('#chargesAmt').html('$00.00');
					$('#finalAmt').html('$00.00');
				}


			}
		}



		//browse image
		// function browseProfileImage(input) {
		// 	const fileSize = input.files[0].size; // in MiB

		// 	if (input.files && input.files[0]) {
		// 		var reader = new FileReader();
		// 		reader.onload = function(e) {
		// 			$('#profileimg').attr('src', e.target.result);
		// 		};
		// 		reader.readAsDataURL(input.files[0]);
		// 	}

		// }

		// $(document).ready(function() {
		// 	Test();

		// 	$("#isSetupFee").click(function() {
		// 		var checked = $(this).is(':checked');
		// 		if (checked) {
		// 			$("#myModal").modal({
		// 				backdrop: "static"
		// 			});
		// 			$('#myModal').modal('toggle');
		// 			$('#myModal').modal('show');
		// 			$('#myModal').modal('hide');
		// 		}

		// 	});



		// 	$('#headingOne').addClass('addonHeaderBottomBorder');
		// 	$('#headingOne').removeClass('card-header');
		// 	$('#headingTwo').addClass('addonHeaderBottomBorder');
		// 	$('#headingTwo').removeClass('card-header');

		// 	$('.sms-link').click(function() {
		// 		setCSStemporary();
		// 		$('.smsAddonDownArrow').toggleClass('smsRotateArrow');
		// 	})
		// 	$('.emails-link').click(function() {
		// 		setCSStemporarytwo();
		// 		$('.emailsAddonDownArrow').toggleClass('emailRotateArrow');
		// 	})
		// });




		function setCSStemporary() {
			var hasShow = $("#collapseOne").hasClass('show');
			console.log('hey' + hasShow);
			if (hasShow == true) {
				$('#headingOne').addClass('addonHeaderBottomBorder');
				$('#headingOne').removeClass('card-header');
			} else {
				$('#headingOne').removeClass('addonHeaderBottomBorder');
			}
		}

		function setCSStemporarytwo() {
			var hasShow = $("#collapseTwo").hasClass('show');
			console.log('hey' + hasShow);
			if (hasShow == true) {
				$('#headingTwo').addClass('addonHeaderBottomBorder');
				$('#headingTwo').removeClass('card-header');
			} else {
				$('#headingTwo').removeClass('addonHeaderBottomBorder');
			}
		}


		//***** DATE-TIME PICKER WITH CAL-ICON ******//
		$(function() {
			$('.date-picker-class').datetimepicker({
				icons: {
					date: 'fa fa-calendar',
					up: 'fa fa-chevron-up',
					down: 'fa fa-chevron-down',
					previous: 'fa fa-chevron-left',
					next: 'fa fa-chevron-right',
					today: 'fa fa-check',
					clear: 'fa fa-trash'
				},
				allowInputToggle: true
			});
		});

		//datatable alignment
		var album_current_datatable = $("#gallaryTable").DataTable({
			"language": {
				// "info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			// aaSorting: [[0, 'asc']],
			"order": [
				[0, 'desc']
			], //order column in descending order.
			"columnDefs": [{
					"type": "date-time",
					"targets": 0
				} //date column formatted like "03/23/2018 10:25:13 AM".
			],
			"aoColumns": [{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "15%",
					"bSortable": false
				},
				{
					"sWidth": "15%",
					"bSortable": false
				},
				{
					"sWidth": "15%",
					"bSortable": false
				},
				{
					"sWidth": "15%",
					"bSortable": false
				}
			],
		});


		//stripe settings
		var stripeKeys = $("#stripeKeys").val();
		var stripe = Stripe(stripeKeys);
		var elements = stripe.elements();

		var style = {
			base: {
				fontWeight: 400,
				fontFamily: 'Roboto, Open Sans, Segoe UI, sans-serif',
				fontSize: '16px',
				lineHeight: '1.4',
				color: '#555',
				backgroundColor: '#fff',
				'::placeholder': {
					color: '#888',
				},
			},
			invalid: {
				color: '#eb1c26',
			}
		};

		var cardElement = elements.create('cardNumber', {
			style: style
		});

		cardElement.mount('#card-number');

		var exp = elements.create('cardExpiry', {
			'style': style
		});
		exp.mount('#card_expiry');

		var cvc = elements.create('cardCvc', {
			'style': style
		});
		cvc.mount('#card_cvc');

		// Validate input of the card elements
		var resultContainer = document.getElementById('paymentResponse');
		cardElement.addEventListener('change', function(event) {
			if (event.error) {
				resultContainer.innerHTML = '<p>' + event.error.message + '</p>';
			} else {
				resultContainer.innerHTML = '';
			}
		});

		// Get payment form element
		var form = document.getElementById('paymentFrm');

		// Create a token when the form is submitted.
		form.addEventListener('submit', function(e) {
			e.preventDefault();
			if ($("#flexCheckChecked").prop('checked') == true) {
				// alert("chkAgree");
				createToken();

			} else {
				Swal.fire({
					type: "warning",
					title: 'Kindly accept the terms and conditions.',
					confirmButtonClass: 'btn btn-warning',
				})
				// alert("Please select");
			}
		});
		// Create single-use token to charge the user
		function createToken() {
			stripe.createToken(cardElement).then(function(result) {
				if (result.error) {
					// Inform the user if there was an error
					resultContainer.innerHTML = '<p>' + result.error.message + '</p>';
				} else {
					// Send the token to your server
					stripeTokenHandler(result.token);
				}
			});
		}

		// Callback to handle the response from stripe
		function stripeTokenHandler(token) {
			// Insert the token ID into the form so it gets submitted to the server
			var hiddenInput = document.createElement('input');
			hiddenInput.setAttribute('type', 'hidden');
			hiddenInput.setAttribute('name', 'stripeToken');
			hiddenInput.setAttribute('value', token.id);
			form.appendChild(hiddenInput);
			$('#btnSaveWallet').attr('disabled', 'disabled');

			// Submit the form
			form.submit();

		}

		//--------------------------------------------------------
		$("#planId").change(function(e) {
			var curPlanId = $('#currentPlanId').val();
			var newPlanId = $(this).val();
			console.log(curPlanId);
			console.log(newPlanId);
			if (curPlanId != $(this).val()) {

				Swal.fire({
					title: 'Confirmation',
					text: "Continue with update plan?",
					type: 'warning',
					showCancelButton: true,
					confirmButtonColor: '#3085d6',
					cancelButtonColor: '#d33',
					confirmButtonText: 'Update',
					confirmButtonClass: 'btn btn-outline-primary',
					cancelButtonClass: 'btn btn-outline-danger ml-1',
					buttonsStyling: false,
				}).then(function(result) {
					if (result.value) {
						console.log('newPlanId' + newPlanId);
						$('#planId').val(newPlanId);
						$('#frmUpdateScubscription').submit();
					}
				});
				$('#planId').val(curPlanId).trigger('change');
			}
		});

		/*--------- Pay Only Setup Fee--------*/
		$("#btnSetupFee").click(function(e) {
			var setupFee = $('#currentPlanSetupFee').val();
			var businessId = $('#businessId').val();

			$.ajax({
				type: "GET",
				url: "../ajax/ajax_pay_setupFee.html",
				data: {
					businessId: businessId,
					setupFee: setupFee
				},
				dataType: "json",
				success: function(data) {
					if (data == 0) {
						Swal.fire({
							type: "warning",
							title: 'Insufficient Balance!',
							confirmButtonClass: 'btn btn-warning',
						})
					} else {
						Swal.fire({
							type: "success",
							title: 'Plan updated successfully.',
							text: 'Upgraded.',
							confirmButtonClass: 'btn btn-success',
						});

						// $('#frmUpdateScubscription').submit();
					}

				}
			});


		});

		/*----- subscribe plan ---------*/
		$("#subscribePlan").click(function(e) {

			var businessPlanId = $("#businessPlanId").val();
			var curPlanId = $('#currentPlanId').val();
			var businessIds = $("#businessIds").val();
			var detailId = $("#detailId").val();
			var paymentPlan = $('input[name="paymentPlan"]:checked').val();
			var isSetupFee = 0;
			var isChecked = $('input[name="isSetupFee"]:checkbox');
			if (isChecked.is(":checked")) {
				isSetupFee = 1;
			}

			var paymentPlanDuration = $('input[name="paymentPlan"]:checked').attr("duration");
			if (!paymentPlanDuration) {
				Swal.fire({
					type: "error",
					title: 'Please select a payment plan duration!',
					confirmButtonClass: 'btn btn-danger',
				});
				return; // Stop further execution if duration is not selected
			}

			if (curPlanId != businessPlanId && businessPlanId != 1 && businessPlanId != 11) {
				Swal.fire({
					title: 'Confirmation',
					text: "Continue with upgrade plan?",
					type: 'warning',
					showCancelButton: true,
					confirmButtonColor: '#3085d6',
					cancelButtonColor: '#d33',
					confirmButtonText: 'Update',
					confirmButtonClass: 'btn btn-outline-primary',
					cancelButtonClass: 'btn btn-outline-danger ml-1',
					buttonsStyling: false,
				}).then(function(result) {
					if (result.value) {
						//check current wallet balance with subscribe plan
						$.ajax({
							type: "GET",
							url: "../ajax/ajax_check_business_wallet_balance.html",
							data: {
								businessIds: businessIds,
								businessPlanId: businessPlanId,
								detailId: detailId,
								paymentPlan: paymentPlan,
								paymentPlanDuration: paymentPlanDuration,
								isSetupFee: isSetupFee
							},
							dataType: "json",
							success: function(data) {
								// alert(data);								
								if (data == 0) {
									Swal.fire({
										type: "warning",
										title: 'Insufficient Balance!',
										confirmButtonClass: 'btn btn-warning',
									})
								} else {
									Swal.fire({
										type: "success",
										title: 'Plan updated successfully.',
										text: 'Upgraded.',
										confirmButtonClass: 'btn btn-success',
									});

									// $('#frmUpdateScubscription').submit();
								}
							}
						});
					}
				});
				$('#planId').val(curPlanId).trigger('change');
			} else {
				Swal.fire({
					type: "warning",
					title: 'You have already subscribed to this plan!',
					confirmButtonClass: 'btn btn-warning',
				})
			}
		});
		/*---- end code subscribe plan ------*/

		/*------------ Start Addon Subscription ------------*/
		function subscribeAddon(addonId, addonNameId, amount, qty) {
			var businessId = $("#businessId").val();

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with Addon plan?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Update',
				confirmButtonClass: 'btn btn-outline-primary',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_subscribe_addon.html",
						data: {
							businessId: businessId,
							addonId: addonId,
							addonNameId: addonNameId,
							amount: amount,
							qty: qty
						},
						dataType: "json",
						success: function(data) {
							// console.log(data);								
							if (data == 0) {
								Swal.fire({
									type: "warning",
									title: 'Insufficient Balance!',
									confirmButtonClass: 'btn btn-warning',
								})
							} else {
								Swal.fire({
									type: "success",
									title: 'Addon Plan upgraded successfully.',
									text: 'Upgraded.',
									confirmButtonClass: 'btn btn-success',
								});

								// $('#frmUpdateScubscription').submit();
							}
						}
					});


				}
			});
		}

		/*------------ End Addon Subscription ------------*/

		//business plan form validation 
		$('#businessDetailsForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});

		//profile form validation
		$('#employeeForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});

		//change password form validation
		var isPrimaryUser = $('#isPrimaryUser').val();
		if (isPrimaryUser == 1) {
			$('#changePasswordForm').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					return true; // Don't submit form for this demo
				});

		}


		//country dropdown code
		$('#employeeForm').cascadingDropdown({
			selectBoxes: [{
					selector: '.step1',
					selected: '<?php echo ($dbCountryId); ?>'
				},
				{
					selector: '.step2',
					selected: '<?php echo ($stateId); ?>',
					requires: ['.step1'],
					requireAll: true,
					source: function(request, response) {

						$.getJSON('../ajax/getStates.html', request, function(data) {
							response($.map(data, function(item, index) {
								return {
									label: item['StateName'],
									value: item['StateId']
								};
							}));
						});
					}
				}
			]
		});
		<?php
		if ($dbCountryId == 0) {
		?>
			$('#cboCountry').val('224').trigger('change');
		<?php
		}
		?>


		//country dropdown code
		$('#businessDetailsForm').cascadingDropdown({
			selectBoxes: [{
					selector: '.step3',
					selected: '<?php echo ($businesscountryId); ?>'
				},
				{
					selector: '.step4',
					selected: '<?php echo ($businessdbStateId); ?>',
					requires: ['.step3'],
					requireAll: true,
					source: function(request, response) {

						$.getJSON('../ajax/getStates1.html', request, function(data) {
							response($.map(data, function(item, index) {
								return {
									label: item['StateNames'],
									value: item['StateIds']
								};
							}));
						});
					}
				}
			]
		});
		<?php
		if ($businesscountryId == 0) {
		?>
			$('#cboCountry1').val('224').trigger('change');
		<?php
		}
		?>

		// $(document).ready(function() 
		// {       

		// });		
		function Test() {
			var businessPlanId = $('#currentPlanId').val();
			var businessId = $('#businessId').val();
			var businessPlanDetailId = $('#businessPlanDetailId').val();


			// alert(businessId);
			// return false;
			$.ajax({
				type: "GET",
				url: "../ajax/ajax_get_plan_details.html",
				data: {
					businessPlanId: businessPlanId,
					businessId: businessId,
					businessPlanDetailId: businessPlanDetailId
				},
				dataType: "json",
				success: function(data) {

					var dbbusinessPlanId = data[0].planId;

					if (dbbusinessPlanId == 11 || dbbusinessPlanId == 1) {
						$('.transactionFileds').addClass('hidden');
					} else {
						$('.transactionFileds').removeClass('hidden');

					}
					// alert(businessPlanId);

					$("#title").val(data[0].title);
					$("#price").val(data[0].price);
					$("#contactLimit").val(data[0].contactLimit);
					$("#imageInfographLimit").val(data[0].imageInfographLimit);
					$("#videoInfographLimit").val(data[0].videoInfographLimit);
					$("#formLimit").val(data[0].formLimit);
					$("#cardLimit").val(data[0].cardLimit);
					$("#imageCarouselLimit").val(data[0].imageCarouselLimit);
					$("#subCardLimit").val(data[0].subCardLimit);
					$("#imageInfographSectionLimit").val(data[0].imageInfographSectionLimit);;
					$("#videoInfographSectionLimit").val(data[0].videoInfographSectionLimit);
					$("#isDedicatedURL").html(data[0].isDedicatedURL);
					$("#allowThemeEditing").html(data[0].allowThemeEditing);
					$("#allowLogoInclusion").html(data[0].allowLogoInclusion);
					$("#allowPhotoInclusion").html(data[0].allowPhotoInclusion);
					$("#allowVideoProfile").html(data[0].allowVideoProfile);
					$("#isWhiteLabel").html(data[0].isWhiteLabel);
					$("#socialfeed").val(data[0].socialfeed);
					$("#isCrmIntergration").html(data[0].isCrmIntergration);
					$("#isPDFSubscription").html(data[0].isPDFSubscription);
					$("#isCouponServices").html(data[0].isCouponServices);
					$("#chatRoom").html(data[0].chatRoom);
					$("#chatemail").html(data[0].chatemail);
					$("#geolocation").html(data[0].geolocation);
					$("#invoice").html(data[0].invoice);
					$("#pushNotification").html(data[0].pushNotification);
					$("#probizcaplus").html(data[0].probizcaplus);
					$("#shoppingCart").html(data[0].shoppingCart);
					$("#statistics").html(data[0].statistics);
					$("#support").html(data[0].support);
					$("#todo").html(data[0].todo);
					$("#infoSectionLiimit").html(data[0].infoSectionLiimit);
					$("#infoContentLimit").html(data[0].infoContentLimit);
					$("#quarterlyAmount").html(data[0].quarterlyAmount);
					$("#semiAnnualAmount").html(data[0].semiAnnualAmount);
					$("#annualAmount").html(data[0].annualAmount);
					$("#galleryImageLimit").html(data[0].galleryImageLimit);

					$('#smsChk').html(data[0].smschk);
					$('#surveychk').html(data[0].surveychk);
					$('#customQrFormchk').html(data[0].customQrFormchk);

					console.log(data[0].customQrFormchk);

					// if(data[0].smschk == 'No')
					// {
					// 	$('#Dfsmsdiv').addClass('hide');
					// 	$('#smsdiv').addClass('hide');
					// 	$('#totalSmsdiv').addClass('hide');
					// }

					// if(data[0].chatemail == 'No')
					// {
					// 	$('#DfEmaildiv').addClass('hide');
					// 	$('#emaildiv').addClass('hide');
					// 	$('#totalEmaildiv').addClass('hide');
					// }


					$("#smsdfUsed").html(data[0].dfsmsU + ' / ');
					$("#smsLiimit").html(data[0].defaultSms);
					$("#smsPurUsed").html(data[0].smsPurUsed + ' / ');
					$("#smsPurCount").html(data[0].smsPurCount);

					var totalSMSUsed = parseInt(data[0].dfsmsU) + parseInt(data[0].smsPurUsed);
					var totalSMS = parseInt(data[0].defaultSms) + parseInt(data[0].smsPurCount);
					$('#totalSMSUsed').html(totalSMSUsed + ' / ');
					$('#totalsms').html(totalSMS);

					$("#emaildfUsed").html(data[0].dfUsed + ' / ');
					$("#emaillLimit").html(data[0].defaultEmail);
					$("#EmailpurUsed").html(data[0].EmailpurUsed + ' / ');
					$("#EmailpurchasedCount").html(data[0].EmailpurchasedCount);

					var totalEmailUsed = parseInt(data[0].dfUsed) + parseInt(data[0].EmailpurUsed);
					var totalEmail = parseInt(data[0].defaultEmail) + parseInt(data[0].EmailpurchasedCount);
					$('#totalEmailUsed').html(totalEmailUsed + ' / ');
					$('#totalEmail').html(totalEmail);
				}
			});
		}


		//onchange plan get details 	
		$(document).on('change', '#businessPlanId', function() {
			var businessPlanId = $('#businessPlanId').val();
			var businessId = $('#businessId').val();
			var currentPlanId = $('#currentPlanId').val();
			var businessPlanDetailId = 0;
			if (businessPlanId == currentPlanId) {
				var businessPlanDetailId = $('#businessPlanDetailId').val();
			}
			if (businessPlanId == 1)
				$('input[name=paymentPlan]').attr("disabled", true);
			// Swal.fire({
			// title: 'Confirmation',
			// text: "Continue with upgrade plan?",
			// type: 'warning',
			// showCancelButton: true,
			// confirmButtonColor: '#3085d6',
			// cancelButtonColor: '#d33',
			// confirmButtonText: 'Upgrade',
			// confirmButtonClass: 'btn btn-outline-primary',
			// cancelButtonClass: 'btn btn-outline-danger ml-1',
			// buttonsStyling: false,
			// }).then(function (result) 
			// {
			// if (result.value) 
			// {				   
			$.ajax({
				type: "GET",
				url: "../ajax/ajax_get_plan_details.html",
				data: {
					businessPlanId: businessPlanId,
					businessId: businessId,
					businessPlanDetailId: businessPlanDetailId
				},
				dataType: "json",
				success: function(data) {
					$("#isSetupFee").prop('checked', false);

					var dbbusinessPlanId = data[0].planId;

					if (dbbusinessPlanId == 11 || dbbusinessPlanId == 1) {
						$('.transactionFileds').addClass('hidden');
					} else {
						$('.transactionFileds').removeClass('hidden');

					}

					$("#title").val(data[0].title);
					$("#price").val(data[0].price);
					$("#contactLimit").val(data[0].contactLimit);
					$("#imageInfographLimit").val(data[0].imageInfographLimit);
					$("#videoInfographLimit").val(data[0].videoInfographLimit);
					$("#formLimit").val(data[0].formLimit);
					$("#cardLimit").val(data[0].cardLimit);
					$("#imageCarouselLimit").val(data[0].imageCarouselLimit);
					$("#subCardLimit").val(data[0].subCardLimit);
					$("#imageInfographSectionLimit").val(data[0].imageInfographSectionLimit);;
					$("#videoInfographSectionLimit").val(data[0].videoInfographSectionLimit);
					$("#isDedicatedURL").html(data[0].isDedicatedURL);
					$("#allowThemeEditing").html(data[0].allowThemeEditing);
					$("#allowLogoInclusion").html(data[0].allowLogoInclusion);
					$("#allowPhotoInclusion").html(data[0].allowPhotoInclusion);
					$("#allowVideoProfile").html(data[0].allowVideoProfile);
					$("#isWhiteLabel").html(data[0].isWhiteLabel);
					$("#socialfeed").val(data[0].socialfeed);
					$("#isCrmIntergration").html(data[0].isCrmIntergration);
					$("#isPDFSubscription").html(data[0].isPDFSubscription);
					$("#isCouponServices").html(data[0].isCouponServices);
					$("#chatRoom").html(data[0].chatRoom);
					$("#chatemail").html(data[0].chatemail);
					$("#geolocation").html(data[0].geolocation);
					$("#invoice").html(data[0].invoice);
					$("#pushNotification").html(data[0].pushNotification);
					$("#probizcaplus").html(data[0].probizcaplus);
					$("#shoppingCart").html(data[0].shoppingCart);
					$("#statistics").html(data[0].statistics);
					$("#support").html(data[0].support);
					$("#todo").html(data[0].todo);
					$("#infoContentLimit").html(data[0].infoContentLimit);
					$("#infoSectionLiimit").html(data[0].infoSectionLiimit);
					$("#quarterlyAmount").html(data[0].quarterlyAmount);
					$("#semiAnnualAmount").html(data[0].semiAnnualAmount);
					$("#annualAmount").html(data[0].annualAmount);
					$("#galleryImageLimit").html(data[0].galleryImageLimit);
					$('#smsChk').html(data[0].smschk);
					$("#smsLiimit").html(data[0].defaultSms);
					$("#emaillLimit").html(data[0].defaultEmail);

					$('#surveychk').html(data[0].surveychk);
					$('#customQrFormchk').html(data[0].customQrFormchk);


					if (data[0].dfUsed != '' && data[0].dfsmsU != '') {
						// Email
						$("#emaildiv").removeClass('hide');
						$("#emaildfUsed").removeClass('hide');
						$("#emaildfUsed").html(data[0].dfUsed + ' / ');
						$("#EmailpurUsed").html(data[0].EmailpurUsed + ' / ');
						$("#EmailpurchasedCount").html(data[0].EmailpurchasedCount);
						$('#totalEmaildiv').removeClass('hide');

						var totalEmailUsed = parseInt(data[0].dfUsed) + parseInt(data[0].EmailpurUsed);
						var totalEmail = parseInt(data[0].defaultEmail) + parseInt(data[0].EmailpurchasedCount);
						$('#totalEmailUsed').html(totalEmailUsed + ' / ');
						$('#totalEmail').html(totalEmail);

						// SMS
						$("#smsdiv").removeClass('hide');
						$("#smsdfUsed").removeClass('hide');
						$("#smsdfUsed").html(data[0].dfsmsU + ' / ');
						$("#smsPurUsed").html(data[0].smsPurUsed + ' / ');
						$("#smsPurCount").html(data[0].smsPurCount);
						$("#totalSmsdiv").removeClass('hide');

						var totalSMSUsed = parseInt(data[0].dfsmsU) + parseInt(data[0].smsPurUsed);
						var totalSMS = parseInt(data[0].defaultSms) + parseInt(data[0].smsPurCount);
						$('#totalSMSUsed').html(totalSMSUsed + ' / ');
						$('#totalsms').html(totalSMS);
						$('#totalsms').removeClass('hide');
					} else {
						$("#emaildiv").addClass('hide');
						$('#totalEmaildiv').addClass('hide');
						$("#emaildfUsed").addClass('hide');

						$("#smsdiv").addClass('hide');
						$("#totalSmsdiv").addClass('hide');
						$("#smsdfUsed").addClass('hide');
					}

				}
			});
			// }
			// });
		});


		var wrapTrue = $('.flex-wrap')

		if (wrapTrue == true) {
			console.log('hey' + wrapTrue);
		}
	</script>


<script>
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
        
		  const coverCropper =	  new ImageCropper('.cropper-instance-1');
   	      const coverCropper2 = new ImageCropper('.cropper-instance-2');


    
            
            // Check if default school image exists and display it
            const defaultImagePath = '<?php echo $employeeImagePath; ?>';
            const profileImageFilePath = '<?php echo $businessLogo; ?>';

            console.log("Default image path:", defaultImagePath);
            console.log("profileImageFilePath image path:", profileImageFilePath);
            
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('.cropper-instance-1 .upload-area');
                const $uploadContent = $('.cropper-instance-1 .upload-content');
                
                console.log("Upload area element exists:", $uploadArea.length > 0);
                console.log("Upload content element exists:", $uploadContent.length > 0);
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
                
                // Create the image element first to check if it loads
                const imgElement = document.createElement('img');
                imgElement.onload = function() {
                    console.log("Image loaded successfully");
                };
                imgElement.onerror = function() {
                    console.error("Failed to load image from path:", defaultImagePath);
                };
                imgElement.src = defaultImagePath + "?randId=" + new Date().getTime();
                imgElement.alt = "School Logo Preview";
                imgElement.style = "max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #667eea; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-top: 10px !important;";
                
                // Update the content with the existing image
                $uploadContent.empty().append(
                    $('<div>').css({
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '10px'
                    }).append(
                        imgElement,
                        $('<div>').css({
                            textAlign: 'center'
                        }).append(
                            $('<p>').css({
                                color: '#666EE8',
                                fontWeight: 'bold',
                                margin: '5px 0',
                                fontSize: '14px'
                            }).text('✓ Current image'),
                            $('<span>').addClass('file-types').css({
                                color: '#666',
                                fontSize: '12px'
                            }).text('Click to select a different image if needed')
                        )
                    )
                );
            } 
			if(profileImageFilePath && profileImageFilePath !== ''){

                const $uploadArea = $('.cropper-instance-2 .upload-area');
                const $uploadContent = $('.cropper-instance-2 .upload-content');
                
                console.log("Upload area element exists:", $uploadArea.length > 0);
                console.log("Upload content element exists:", $uploadContent.length > 0);
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
                
                // Create the image element first to check if it loads
                const imgElement = document.createElement('img');
                imgElement.onload = function() {
                    console.log("Image loaded successfully");
                };
                imgElement.onerror = function() {
                    console.error("Failed to load image from path:", profileImageFilePath);
                };
                imgElement.src = profileImageFilePath + "?randId=" + new Date().getTime();
                imgElement.alt = "School Logo Preview";
                imgElement.style = "max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #667eea; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-top: 10px !important;";
                
                // Update the content with the existing image
                $uploadContent.empty().append(
                    $('<div>').css({
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '10px'
                    }).append(
                        imgElement,
                        $('<div>').css({
                            textAlign: 'center'
                        }).append(
                            $('<p>').css({
                                color: '#666EE8',
                                fontWeight: 'bold',
                                margin: '5px 0',
                                fontSize: '14px'
                            }).text('✓ Current image'),
                            $('<span>').addClass('file-types').css({
                                color: '#666',
                                fontSize: '12px'
                            }).text('Click to select a different image if needed')
                        )
                    )
                );
            
			}
        });
  

  </script>
</body>

</html>