<?php

require ROOT_PATH . '/vendor/autoload.php';

// echo ROOT_PATH;exit;

use \SendGrid\Mail\Mail;
use Mailgun\Mailgun;

class clsSendEmails
{
	var $emailHeader = '';
	var $emailFooter = '';
	var $proBizCaLogo = '';

	function  __construct()
	{
		$this->emailHeader = '<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td width="30%" align="center">
										<img src="BASE_PATH/assets/images/logo.png" alt="Funasia" title="Funasia" />
									</td>
								</tr>
							</table>';
		$path = ROOT_PATH . "/assets/images/logo/logo-white.png";
		$image_type = pathinfo($path, PATHINFO_EXTENSION);
		$imagedata = file_get_contents("$path");
		// alternatively specify an URL, if PHP settings allow
		// $this->proBizCaLogo ='data:image/' . $image_type . ';base64,' . base64_encode($imagedata);

		// $this->proBizCaLogo = 'https://rt.clinicaltrac.net/assets/images/probizca/logo-white.png';
		$this->proBizCaLogo = 'https://probizca.net/assets/images/probizca/logo-white.png';

		// echo $this->proBizCaLogo;exit;
		// $this->proBizCaLogo = BASE_PATH."/assets/images/logo/logo-white.png";	

	}

	// function SendEmail($subject, $emailContent, $toEmail, $fromEmail = "", $attatchementPath = "", $fileName = "")
	// {

	// 	$email = new Mail();
	// 	$email->setFrom("<EMAIL>", "ProBizCa");
	// 	$email->setSubject($subject);
	// 	$email->addTo($toEmail);
	// 	$email->addContent(
	// 		"text/html",
	// 		$emailContent
	// 	);

	// 	// Check if attachment content is provided
	// 	if (!empty($attatchementPath)) {
	// 		$attachment = new \SendGrid\Mail\Attachment();
	// 		$attachment->setContent($attatchementPath); // Assuming $attatchementPath contains base64-encoded content
	// 		$attachment->setType("application/octet-stream"); // Set the MIME type as needed
	// 		$attachment->setFilename($fileName);

	// 		$email->addAttachment($attachment);
	// 	}

	// 	// print_r($email);
	// 	// exit;
	// 	$sendgrid = new \SendGrid(SENDGRID_API_KEY);
	// 	try {
	// 		$response = $sendgrid->send($email);
	// 		// print $response->statusCode() . "\n";
	// 		// print_r($response->headers());
	// 		// print $response->body() . "\n";
	// 		// return $response->statusCode();
	// 	} catch (Exception $e) {
	// 		echo 'Caught exception: ' . $e->getMessage() . "\n";
	// 	}

	// 	// $mail = new PHPMailer;
	// 	// $mail->isSMTP();                                     
	// 	// $mail->Host = 'mail.probizca.net'; 
	// 	// $mail->SMTPAuth = true;                   
	// 	// $mail->Username   = "<EMAIL>"; 
	// 	// $mail->Password   = "9GgTY~.srAq,";
	// 	// $mail->SMTPSecure = 'tls';                          
	// 	// $mail->Port = 465;
	// 	// $mail->From = '<EMAIL>';
	// 	// $mail->FromName = 'ProBizCa';
	// 	// $mail->addAddress($toEmail); 
	// 	// $mail->isHTML(true);  
	// 	// $mail->Subject = $subject;
	// 	// $mail->Body = $emailContent;  		

	// 	// // print_r($mail->send());
	// 	// //check any error
	// 	// // if(!$mail->send())  
	// 	// // {
	// 	// 	// echo 'Message could not be sent.';
	// 	// 	// echo 'Mailer Error: ' . $mail->ErrorInfo;exit;
	// 	// // } 
	// 	// // else 
	// 	// // {
	// 	// 	// echo 'Message has been sent';exit;
	// 	// // }
	// 	// /*----- send single attachment files------*/
	// 	// if($attatchementPath!='')
	// 	// {
	// 	//     $mail->AddStringAttachment($attatchementPath,$fileName);
	// 	//     $mail->addAttachment($attatchementPath);		   
	// 	// }
	// 	// $mail->send();
	// 	// unset($mail);
	// }

	// MailGUn
	function SendEmail($subject, $emailContent, $to, $attatchementString = '', $attachmentName = '')
	{
		// Mailgun configuration
		$apiKey     = PRB_MAIL_GUN_API_KEY;
		$domain     = PRB_MAIL_GUN_DOMAIN;
		$fromEmail  = PRB_MAIL_GUN_FROM_EMAIL;
		$fromName   = PRB_MAIL_GUN_FROM_NAME;

		// Initialize Mailgun client
		$mg = Mailgun::create($apiKey);

		// Build message payload
		$params = [
			'from'    => "$fromName <$fromEmail>",
			'to'      => $to,
			'subject' => $subject,
			'html'    => $emailContent,
			'text'    => strip_tags($emailContent)
		];

		// 	print_r($params);

		// Attach file if path is valid
		// if (!empty($attatchementPath) && file_exists($attatchementPath)) {
		// 	$params['attachment'] = [
		// 		[
		// 			'filePath' => $attatchementPath,
		// 			'filename' => basename($attatchementPath)
		// 		]
		// 	];
		// }

		// Attach file from string
		if (!empty($attatchementString)) {
			// echo "attachment";exit;
			$params['attachment'][] = [
				'fileContent' => $attatchementString,
				'filename'    => $attachmentName
			];
		}
		try {
			$response = $mg->messages()->send($domain, $params);
			return $response->getId(); // success
		} catch (Exception $e) {
			error_log('Mailgun Exception: ' . $e->getMessage());
			return false;
		}
	}


	//Business module function start here...
	function SendBusiessForgotPassword($employeeId, $dynamicPassword = 's')
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE employeeId=" . $employeeId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Forgot Password Request";
		$ResetUrl = BASE_PATH . '/business/resetpassword.html?id=' . EncodeQueryData($employeeId);
		$path = ROOT_PATH . "/includes/emailtemplate/forgetpassword/forgetpassword.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $firstName . " " . $lastName,
			'{{URL}}' => $ResetUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
		// $emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 						<tr>
		// 							<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">Forgot Password</h1></td>
		// 						</tr>
		// 						<tr>
		// 							<td width="30%" align="center">
		// 							</td>
		// 						</tr>
		// 					</table>';

		// $emailContent .='<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 						<tr>
		// 							<td valign="top">
		// 								<p>Hello </strong> '.$firstName.',</p>
		// 								<p>We recently received a request to reset your account password. If you initiated this request, please proceed with the password reset process by clicking on<br/> the button below<br/><br>									
		// 								 <a href="'.BASE_PATH.'/business/resetpassword.html?id='.EncodeQueryData($employeeId).'"><strong>Click here to reset password</strong></a><br/>

		// 								</p>
		// 							</td>
		// 						</tr>
		// 					</table>';	
		$this->SendEmail($subject, $emailContent, $email);
	}

	//customer module function
	function SendCustomerForgotPassword($customerId, $dynamicPassword = '')
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer WHERE customerId=" . $customerId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Forgot Password Request";
		$ResetUrl = BASE_PATH . '/customer/resetpassword.html?id=' . EncodeQueryData($customerId);
		$path = ROOT_PATH . "/includes/emailtemplate/forgetpassword/forgetpassword.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $firstName . " " . $lastName,
			'{{URL}}' => $ResetUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);

		// $subject = APPLICATION_NAME." - Login Details";		
		// $emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 						<tr>
		// 							<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">Forgot Password</h1></td>
		// 						</tr>
		// 						<tr>
		// 							<td width="30%" align="center">
		// 							</td>
		// 						</tr>
		// 					</table>';

		// $emailContent .='<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 						<tr>
		// 							<td valign="top">
		// 								<p>Hello </strong> '.$firstName.',</p>
		// 								<p>Below  your  login  details.<br/><br>									
		// 								<strong>URL:</strong> '.BASE_PATH."/customer/index.html".'<br/>
		// 								<strong>Email:</strong> '.$email.'<br/>
		// 								<strong>Password:</strong> '.$dynamicPassword.'<br/>
		// 								</p>
		// 							</td>
		// 						</tr>
		// 					</table>';		
		// $this->SendEmail($subject,$emailContent,$email);					
	}

	//customer module function
	function SendAffiliateForgotPassword($affiliateId, $dynamicPassword = '')
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM affiliate WHERE affiliateId=" . $affiliateId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Forgot Password Request";
		$ResetUrl = BASE_PATH . '/affiliate/resetpassword.html?id=' . EncodeQueryData($affiliateId);
		$path = ROOT_PATH . "/includes/emailtemplate/forgetpassword/forgetpassword.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $firstName . " " . $lastName,
			'{{URL}}' => $ResetUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	//employee module function
	function SendEmployeeForgotPassword($employeeId, $dynamicPassword)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE employeeId=" . $employeeId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Login Details";
		$emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">Forgot Password</h1></td>
								</tr>
								<tr>
									<td width="30%" align="center">
									</td>
								</tr>
							</table>';

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td valign="top">
										<p>Hello </strong> ' . $firstName . ',</p>
										<p>Below  your  login  details.<br/><br>									
										<strong>URL:</strong> ' . BASE_PATH . "/employee/index.html" . '<br/>
										<strong>Email:</strong> ' . $email . '<br/>
										<strong>Password:</strong> ' . $dynamicPassword . '<br/>
										</p>
									</td>
								</tr>
							</table>';
		$this->SendEmail($subject, $emailContent, $email);
	}

	function SendSystemUserForgotPassword($systemUserId, $dynamicPassword)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM systemuser WHERE systemUserId=" . $systemUserId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Login Details";
		$emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">Forgot Password</h1></td>
								</tr>
								<tr>
									<td width="30%" align="center">
									</td>
								</tr>
							</table>';

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td valign="top">
										<p>Hello </strong> ' . $firstName . ',</p>
										<p>Below  your  login  details.<br/><br>									
										<strong>URL:</strong> ' . BASE_PATH . "/superadmin/index.html" . '<br/>
										<strong>Email:</strong> ' . $email . '<br/>
										<strong>Password:</strong> ' . $dynamicPassword . '<br/>
										</p>
									</td>
								</tr>
							</table>';
		$this->SendEmail($subject, $emailContent, $email);
	}

	function SendSystemUserLoginDetails($systemUserId, $dynamicPassword)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM systemuser WHERE systemUserId=" . $systemUserId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		$userName = stripslashes($row['userName']);
		unset($objDB);

		$loginUrl = BASE_PATH . '/superadmin/index.html';

		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		 instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		 here to assist you. Below is your ProBizCa login credentials:";

		$subject = APPLICATION_NAME . " - Your Login Information";


		$emailContent = file_get_contents('../includes/emailtemplate/senduserslogindetails/userDetailsLogin.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{USERNAME}}' => $userName,
			'{{PASSWORD}}' => $dynamicPassword,
			'{{message}}' =>  $message,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$this->SendEmail($subject, $emailContent, $email);
	}

	function SendEmployeeLoginDetails($employeeId, $dynamicPassword, $isPrimary)
	{
		// echo "Business Logo -> ".$businrssLogo;exit;

		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE employeeId=" . $employeeId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$loginUrl = $isPrimary ? BASE_PATH . '/business/index.html?isPrimary=' . $isPrimary : BASE_PATH . '/business/index.html';


		$subject = APPLICATION_NAME . " - Your Login Information";

		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		 instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		 here to assist you. Below is your ProBizCa login credentials:";

		$emailContent = file_get_contents('../includes/emailtemplate/sendbusinesslogindetails/employeeLogin.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{USERNAME}}' => $email,
			'{{PASSWORD}}' => $dynamicPassword,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl,
			'{{message}}' =>  $message
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		$this->SendEmail($subject, $emailContent, $email);
	}

	function SendEmployeeLoginDetailsForSecondaryBusines($employeeId, $dynamicPassword, $primaryBusinessId)
	{
		// echo "Business Logo -> ".$businrssLogo;exit;

		$objDB = new clsDB();
		$sql = "SELECT businessName FROM business WHERE businessId=" . $primaryBusinessId;
		$businessName = $objDB->GetSingleFieldValue($sql);

		// echo $businessName;exit;
		// $isPrimaryUser = stripslashes($row['businessName']);

		$sql1 = "SELECT * FROM employee WHERE employeeId=" . $employeeId;
		$emplyeeDtls = $objDB->GetDataRow($sql1);
		$firstName = stripslashes($emplyeeDtls['firstName']);
		$lastName = stripslashes($emplyeeDtls['lastName']);
		$email = stripslashes($emplyeeDtls['email']);

		unset($objDB);
		// echo $sql;exit;


		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		here to assist you. Below is your ProBizCa login credentials and instructions on how to
		start personalizing your ProBizCa:";

		$userNote = "Please Use your existing password associated with your " . $businessName . " business account. ";

		$loginUrl = BASE_PATH . '/business/index.html?isPrimary=1';


		$subject = APPLICATION_NAME . " - Your Login Information";

		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		 instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		 here to assist you. Below is your ProBizCa login credentials:";

		$emailContent = file_get_contents('../includes/emailtemplate/sendbusinesslogindetails/secondaryBusinessUsrLoginDtls.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{USERNAME}}' => $email,
			'{{BUSINESSNOTE}}' => $userNote,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl,
			'{{message}}' =>  $message
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		$this->SendEmail($subject, $emailContent, $email);
	}

	function SendStudentLoginDetails($studentId, $dynamicPassword)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM student WHERE studentId=" . $studentId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Login Details";
		$emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">Login Details</h1></td>
								</tr>
								<tr>
									<td width="30%" align="center">
									</td>
								</tr>
							</table>';

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td valign="top">
										<p>Hello </strong> ' . $firstName . ',</p>
										<p>Below  your  login  details.<br/><br>									
										<strong>URL:</strong> ' . BASE_PATH . "/student/index.html" . '<br/>
										<strong>Email:</strong> ' . $email . '<br/>
										<strong>Password:</strong> ' . $dynamicPassword . '<br/>
										</p>
									</td>
								</tr>
							</table>';
		$this->SendEmail($subject, $emailContent, $email);
	}

	function SendAffiliateLoginDetails($affiliateId, $dynamicPassword)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM affiliate WHERE affiliateId=" . $affiliateId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);
		$subject = APPLICATION_NAME . " - Your Login Information";

		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		 instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		 here to assist you. Below is your ProBizCa login credentials:";
		$message1 = "";

		$loginUrl = BASE_PATH . '/affiliate/index.html';
		$emailContent = file_get_contents('../includes/emailtemplate/sendcontactlogindetails/contactDetailsLogin.html');
		//$emailContent = file_get_contents('../includes/emailtemplate/businessregistrationemail/businessLoginDetails.html');


		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{USERNAME}}' => $email,
			'{{PASSWORD}}' => $dynamicPassword,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl,
			'{{message}}' =>  $message,
			'{{message1}}' =>  $message1,

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	function SendCustomerLoginDetails($customerId, $dynamicPassword)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer WHERE customerId=" . $customerId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		// $Blogo = ($businessLogo != '') ? $businessLogo : $this->proBizCaLogo;
		// echo $Blogo;exit;

		$subject = APPLICATION_NAME . " - Your Login Information";

		$message = "Your ProBizCa allows you to connect with businesses in our network to instantly engage, communicate, transact, and save time and money.";
		$message1 = "Below is your ProBizCa Contact Login Credentials, you'll need to add the Login Page to your Mobile Device Home Screen:";

		$loginUrl = BASE_PATH . '/customer/index.html';
		// $emailContent = file_get_contents('../includes/emailtemplate/sendcontactlogindetails/contactDetailsLogin.html');
		$path = ROOT_PATH . "/includes/emailtemplate/sendcontactlogindetails/contactDetailsLogin.html";
		$emailContent = file_get_contents("$path");
		//$emailContent = file_get_contents('../includes/emailtemplate/businessregistrationemail/businessLoginDetails.html');


		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{USERNAME}}' => $email,
			'{{PASSWORD}}' => $dynamicPassword,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl,
			'{{message}}' =>  $message,
			'{{message1}}' =>  $message1
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
		// print_r($emailContent);
		// exit;
	}

	// Send Code for login from Probizca 
	function SendCustomerLoginCode($customerId, $dynamicLoginCode)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer WHERE customerId=" . $customerId;
		$row = $objDB->GetDataRow($sql);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$email = stripslashes($row['email']);
		unset($objDB);

		$subject = APPLICATION_NAME . " - Login Code";

		$path = ROOT_PATH . "/includes/emailtemplate/logincode/logincode.html";
		$emailContent = file_get_contents("$path");

		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{VERICODE}}' => $dynamicLoginCode,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}


	function SendFormPdfToFormRecipients($formTitle, $email, $fromEmail, $attatchementPath, $fileName, $imageFileArray)
	{
		//Read details	
		$subject = APPLICATION_NAME . " -" . $formTitle;
		$emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">' . $formTitle . '</h1></td>
								</tr>
								<tr>
									<td width="30%" align="center">
									</td>
								</tr>
							</table>';

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td valign="top">
										Hello,<br/>
										You received form information from your ProBizCa account.<br/>
										Please view the attached pdf.
									</td>
								</tr>
							</table>';


		//send email
		$mail = new PHPMailer;
		$mail->isSMTP();
		$mail->Host = 'mail.probizca.net';
		$mail->SMTPAuth = true;
		$mail->Username   = "<EMAIL>";
		$mail->Password   = "9GgTY~.srAq,";
		$mail->SMTPSecure = 'tls';
		$mail->Port = 465;
		$mail->From = '<EMAIL>';
		$mail->FromName = 'ProBizCa';
		$mail->addAddress($toEmail);
		$mail->isHTML(true);
		$mail->Body = $emailContent;
		$mail->Subject = $subject;

		/*----- send single attachment files------*/
		if ($attatchementPath != '') {
			$mail->AddStringAttachment($attatchementPath, $fileName);
			$mail->addAttachment($attatchementPath);
		}


		/*----- send multiple attachment files------*/
		if ($imageFileArray != '') {
			foreach ($imageFileArray as $attachpath) {
				$mail->addAttachment($attachpath);
			}
		}


		$mail->send();
		//check any error
		// if(!$mail->send())  
		// {
		// echo 'Message could not be sent.';
		// echo 'Mailer Error: ' . $mail->ErrorInfo;exit;
		// } 
		// else 
		// {
		// echo 'Message has been sent';exit;
		// }
		unset($mail);
	}

	function SendShareProBizCaMessage($previewURL, $fullName, $email, $contactmessage, $dynamicPassword)
	{
		// echo "Share URl -> ".$previewURL;exit;

		$welcomeMessage = "Your ProBizCa allows you to connect with businesses in our network to instantly engage, communicate, transact, and save time and money.";
		$welcomeMessage2 = "Below is your ProBizCa Contact Login Credentials, you'll need to add the Login Page to your Mobile Device Home Screen:";

		$note = "Kindly note that logging in as contact allows you an automatic login of 30-day , but please be aware that abrupt logouts will require re-entry.";

		$loginUrl = BASE_PATH . '/customer/index.html';

		// $subject = APPLICATION_NAME." - ProBizCa Share Message Details";	
		$subject = APPLICATION_NAME . " - Your Login Information";
		$path = ROOT_PATH . "/includes/emailtemplate/sharemessageviaemail/shareMessageViaEmail.html";
		$emailContent = file_get_contents("$path");

		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{USERNAME}}' => $email,
			'{{PASSWORD}}' => $dynamicPassword,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{previewURL}}' => $previewURL,
			'{{contactmessage}}' => strip_tags($contactmessage),
			'{{URL}}' => $loginUrl,
			'{{welcomemessage}}' =>  $welcomeMessage,
			'{{welcomeMessage2}}' =>  $welcomeMessage2,
			'{{NOTE}}' =>  $note
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// echo $emailContent; exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	//send business subscription plan details
	function SendBusiessPlanDetails($businessId, $ownername, $email, $dynamicPassword)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE email=" . $email;
		$row = $objDB->GetDataRow($sql);
		$isPrimaryUser = stripslashes($row['isPrimaryUser']);

		unset($objDB);

		$loginUrl = BASE_PATH . '/business/index.html?isPrimary=1';

		$subject = APPLICATION_NAME . " - Welcome to ProBizCa";

		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		here to assist you. Below is your ProBizCa login credentials and instructions on how to
		start personalizing your ProBizCa:";

		$emailContent = file_get_contents('../includes/emailtemplate/businessregistrationemail/businessLoginDetails.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $ownername,
			'{{MESSAGE}}' => $message,
			'{{USERNAME}}' => $email,
			'{{PASSWORD}}' => $dynamicPassword,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}


	// send business Registration notification to Super Admin 
	function SendBusiessRegistrationDetailsToSuperAdmin($businessName, $ownername, $email, $phoneNo)
	{

		$subject = "New business notification";

		$message = "We are pleased to inform you that a new business has been successfully registered on " . APPLICATION_NAME . " through your marketing website.";

		$emailContent = file_get_contents('../includes/emailtemplate/businessregistrationemail/businessRegistration.html');
		// SUPERADMIN_EMAIL // SUPERADMIN_EMAILTEST 		
		$SystemUseremails = array(SUPERADMIN_EMAILTEST);
		foreach ($SystemUseremails as $key => $SystemUseremail) {
			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $ownername,
				'{{BUSINESSNAME}}' => $businessName,
				'{{MESSAGE}}' => $message,
				'{{USERNAME}}' => $email,
				'{{PHONE}}' => $phoneNo,
				'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
				'{{BUSINESS_LOGO}}' => $this->proBizCaLogo
			);

			// Perform the replacements in one go using str_replace with arrays
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

			$sendEmail = $this->SendEmail($subject, $emailContent, $SystemUseremail);
		}
	}


	//send business subscription plan details
	function SendBusiessPlanDetailsForSecondary($businessId, $ownername, $email, $dynamicPassword)
	{
		// echo $businessId;exit;
		//Read admin Details
		$objDB = new clsDB();
		$sql = "SELECT businessName FROM business WHERE businessId=" . $businessId;
		$businessName = $objDB->GetSingleFieldValue($sql);
		// $isPrimaryUser = stripslashes($row['businessName']);
		unset($objDB);
		// echo $sql;exit;

		$loginUrl = BASE_PATH . '/business/index.html?isPrimary=1';
		$subject = APPLICATION_NAME . " - Welcome to ProBizCa";

		$message = "Your ProBizCa allows you to place your Business & Brand in the palm of your hand to
		instantly Engage, Communicate, Transact, and Save Time & Money. We are always
		here to assist you. Below is your ProBizCa login credentials and instructions on how to
		start personalizing your ProBizCa:";

		$userNote = "Please Use your existing password associated with your " . "$businessName" . " business account. ";

		$emailContent = file_get_contents('../includes/emailtemplate/businessregistrationemail/businessLoginDetailsForSecondary.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $ownername,
			'{{BUSINESSNOTE}}' => $userNote,
			'{{USERNAME}}' => $email,
			'{{MESSAGE}}' => $message,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}




	function SendFormSubmissionToFormReceipent($formTitle, $toemail, $name, $email, $phoneNumber, $submittedDate)
	{

		$subject =   $formTitle . " form Submitted from ProBizca";
		$path = ROOT_PATH . "/includes/emailtemplate/formsubmission/formsubmission.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $name,
			'{{FORMTITLE}}' => $formTitle,
			'{{STARTDATE}}' => $submittedDate,
			'{{EMAIL}}' => $email,
			'{{PHONE}}' => $phoneNumber,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $toemail);
	}

	function SendReminderToBusinessContact($fullName, $id, $email, $businessName)
	{
		//Read details
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM calendarevents WHERE id=" . $id;
		$row = $objDB->GetDataRow($sql);
		$url = stripslashes($row['url']);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);
		$startDate  = (date("m/d/Y h:i A", strtotime($row['start'])));
		unset($objDB);

		$subject = APPLICATION_NAME . " -  Calendar Event Notification";

		$path = ROOT_PATH . "/includes/emailtemplate/calendarevent/calenderEvent.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{TITLE}}' => $title,
			'{{BUSINESSNAME}}' => $businessName,
			'{{STARTDATE}}' => $startDate,
			'{{DESCRIPTION}}' => $description,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	function SendPushNotification($email, $notificationId, $businessName, $fullName)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM notification WHERE notificationId=" . $notificationId;
		$row = $objDB->GetDataRow($sql);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);
		$notificationDate = (date("m/d/Y h:i A", strtotime($row['notificationDate'])));
		unset($objDB);

		$subject = APPLICATION_NAME . " - New Notification";
		$path = ROOT_PATH . "/includes/emailtemplate/pushnotificationfrombo/pushnotificationfrombo.html";

		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{TITLE}}' => $title,
			'{{BUSINESSNAME}}' => $businessName,
			'{{STARTDATE}}' => $notificationDate,
			'{{DESCRIPTION}}' => $description,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}


	function SendPushNotificationsuperadmin($email, $notificationId, $systemUserFullName, $fullName)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM notification WHERE notificationId=" . $notificationId;
		$row = $objDB->GetDataRow($sql);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);
		$notificationDate = (date("m/d/Y h:i A", strtotime($row['notificationDate'])));
		unset($objDB);

		$subject = APPLICATION_NAME . " - New Notification";
		$path = ROOT_PATH . "/includes/emailtemplate/pushnotificationfrombo/pushnotificationfrombo.html";

		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{TITLE}}' => $title,
			'{{BUSINESSNAME}}' => $systemUserFullName,
			'{{STARTDATE}}' => $notificationDate,
			'{{DESCRIPTION}}' => $description,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	function SendInvoicePdf($email, $attatchementPath, $fileName, $invoiceNumber, $billingDate, $firstName)
	{
		//Read details	
		$subject = APPLICATION_NAME . " - ProBizCa Invoice Details";

		$subject = APPLICATION_NAME . " -  Invoice Details " . $invoiceNumber;
		$path = ROOT_PATH . "/includes/emailtemplate/sendinvoicedetails/sendinvoicedetails.html";

		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $firstName,
			'{{INVOICENUMBER}}' => $invoiceNumber,
			'{{INVOICEDATE}}' => $billingDate,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// echo $emailContent;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email,$attatchementPath, $fileName);
	}
	function SendReminderMailOfEventToBusinessContact($fullName, $id, $email, $businessName)
	{
		//Read details
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM calendarevents WHERE id=" . $id;
		$row = $objDB->GetDataRow($sql);
		$url = stripslashes($row['url']);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);
		$startDate  = (date("m/d/Y h:i A", strtotime($row['start'])));
		unset($objDB);

		$subject = APPLICATION_NAME . " - ProBizCa Calendar Event";
		$emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">' . $businessName . ' ProBizCa Calendar Event</h1></td>
								</tr>
								<tr>
									<td width="30%" align="center">
									</td>
								</tr>
							</table>';

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
								<tr>
									<td valign="top">
										<p>Hello </strong> ' . $fullName . ',</p>
										<p>There is reminder for you. <br>Below  is ' . $businessName . ' ProBizCa Calendar Event:<br/><br>																			
										<strong>Name:</strong> ' . $title . '<br/>';
		$emailContent .= '<strong>Date:</strong> ' . $startDate . '<br/>
										</p>
									</td>
								</tr>
							</table>';
		$this->SendEmail($subject, $emailContent, $email);
	}
	function SendMailOfEventToBusinessContact($fullName, $id, $email, $businessName, $startDate = '')
	{
		//Read details
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM calendarevents WHERE id=" . $id;
		$row = $objDB->GetDataRow($sql);
		$url = stripslashes($row['url']);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);

		$startDate  = (date("m/d/Y h:i A", strtotime($startDate)));
		unset($objDB);

		$subject = APPLICATION_NAME . " - ProBizCa Calendar Event - REMINDER ";

		$path = ROOT_PATH . "/includes/emailtemplate/calendarevent/calenderEvent.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{TITLE}}' => $title,
			'{{BUSINESSNAME}}' => $businessName,
			'{{STARTDATE}}' => $startDate,
			'{{DESCRIPTION}}' => $description,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);


		// $subject = APPLICATION_NAME." - ProBizCa Calendar Event";	 	
		// $emailContent =	'<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 						<tr>
		// 							<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif">'.$businessName.' ProBizCa Calendar Event</h1></td>
		// 						</tr>
		// 						<tr>
		// 							<td width="30%" align="center">
		// 							</td>
		// 						</tr>
		// 					</table>';

		// $emailContent .='<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 						<tr>
		// 							<td valign="top">
		// 								<p>Hello </strong> '.$fullName.',</p>
		// 								<p>Your '.$title.'.  is scheduled at '.$startDate.'<br><strong>'.$businessName.' ProBizCa </strong> wishing you <strong>Happy '.$title.'</strong> with joy and year full of Success and Glory.<br/><br>																			
		// 								'.$description.'<br/>
		// 								</p>
		// 							</td>
		// 						</tr>
		// 					</table>';       						
		// $this->SendEmail($subject,$emailContent,$email);					
	}


	function SendMailOfEventToBusinessContactReminder($fullName, $id, $email, $businessName, $startDate = '')
	{
		//Read details
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT * FROM calendarevents WHERE id=" . $id;
		$row = $objDB->GetDataRow($sql);
		$url = stripslashes($row['url']);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);

		$startDate  = (date("m/d/Y h:i A", strtotime($startDate)));
		unset($objDB);


		$subject = APPLICATION_NAME . " - ProBizCa Calendar Event - REMINDER ";

		$path = ROOT_PATH . "/includes/emailtemplate/calendarevent/calenderEvent.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{TITLE}}' => $title,
			'{{BUSINESSNAME}}' => $businessName,
			'{{STARTDATE}}' => $startDate,
			'{{DESCRIPTION}}' => $description,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	//send business subscription Expiry details TO Business Admin
	function SendBusiessPlanExpiryDetails($ownername, $email, $title, $NotificationMsg)
	{
		$subject = APPLICATION_NAME . " - Subscription Notification";

		$path = ROOT_PATH . "/includes/emailtemplate/subscriptionNotification/subscriptionNotification.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $ownername,
			'{{TITLE}}' => $title,
			'{{DESCRIPTION}}' => $NotificationMsg,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent); // exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	//send business subscription Expiry details TO Business SuperAdmin
	function SendBusiessPlanExpiryDetailsToSuperAdmin($ownername, $email, $title, $NotificationMsg)
	{

		$subject = APPLICATION_NAME . " - Subscription Notification";

		$path = ROOT_PATH . "/includes/emailtemplate/subscriptionNotification/subscriptionNotification.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $ownername,
			'{{TITLE}}' => $title,
			'{{DESCRIPTION}}' => $NotificationMsg,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent); // exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	//send Addon subscription  details TO Business SuperAdmin
	function SendAddonSubscriptionDetailsToSuperAdmin($ownername, $email, $NotificationMsg)
	{
		$subject = "Confirmation of " . $NotificationMsg["addonName"] . " Addon Purchase";
		$path = ROOT_PATH . "/includes/emailtemplate/smsaddonpurchase/smsaddonpurchase.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $ownername,
			'{{ADDONNAME}}' => $NotificationMsg["addonName"],
			'{{SUBSCRIPTION}}' => $NotificationMsg["subscriptionType"],
			'{{QUANTITY}}' => $NotificationMsg["qty"],
			'{{PRICE}}' => $NotificationMsg["amount"],
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	//send Addon subscription  details TO Business 
	function SendAddonSubscriptionDetailsToBusiness($ownername, $email, $NotificationMsg)
	{

		$subject = "Confirmation of " . $NotificationMsg["addonName"] . " Addon Purchase";
		$path = ROOT_PATH . "/includes/emailtemplate/smsaddonpurchase/smsaddonpurchase.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $ownername,
			'{{ADDONNAME}}' => $NotificationMsg["addonName"],
			'{{SUBSCRIPTION}}' => $NotificationMsg["subscriptionType"],
			'{{QUANTITY}}' => $NotificationMsg["qty"],
			'{{PRICE}}' => $NotificationMsg["amount"],
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	// Send Incentive Notification 
	function SendIncentiveNotification($fullName, $serviceId, $email, $businessName)
	{
		//Read details
		$objDB = new clsDB();
		$sql = "SELECT service.*,servicecategory.title AS servicecategoryTitle FROM service  
		Inner Join servicecategory on servicecategory.serviceCategoryId = service.serviceCategoryId
		WHERE service.serviceId =" . $serviceId;
		$row = $objDB->GetDataRow($sql);
		$price = stripslashes($row['price']);
		$title = stripslashes($row['title']);
		$description = stripslashes($row['description']);
		$servicecategoryTitle = stripslashes($row['servicecategoryTitle']);
		$startDate  = (date("m/d/Y", strtotime($row['startDate'])));
		$endDate  = (date("m/d/Y", strtotime($row['endDate'])));
		unset($objDB);

		$subject = APPLICATION_NAME . " -  Incentives Announcement ";
		$path = ROOT_PATH . "/includes/emailtemplate/sendincentivestocontact/sendincentivestocontact.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{TITLE}}' => $title,
			'{{BUSINESSNAME}}' => $businessName,
			'{{SERVICENAME}}' => $servicecategoryTitle,
			'{{STARTDATE}}' => $startDate,
			'{{ENDDATE}}' => $endDate,
			'{{DESCRIPTION}}' => $description,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	// Send survey Mails
	function SendSurvey($fullName, $businessName, $email, $redirectUrl, $surveyTitle)
	{
		$subject = APPLICATION_NAME . " -   Your Feedback Requested: Participate in the " . $businessName . " Survey";
		$path = ROOT_PATH . "/includes/emailtemplate/sendsurveyrequest/sendsurveyrequest.html";
		$emailContent = file_get_contents("$path");
		$replaceValues = array(
			'{{NAME}}' => $fullName,
			'{{SURVETITLE}}' => $surveyTitle,
			'{{BUSINESSNAME}}' => $businessName,
			'{{URL}}' => $redirectUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}
	//send Addon subscription  details TO Business When 80% used and surpassed
	function SendAddonSubscriptionSurpassed($businessName, $businessAdminEmail, $message, $addonName)
	{

		$subject = "Your " . $addonName . " Addon Usage is Reaching 80% for the Month";
		$title = "Your " . $addonName . " Addon Usage is Reaching 80% for the Month";

		$path = ROOT_PATH . "/includes/emailtemplate/smsaddonpurchase80/smsaddonpurchasereach80.html";
		$emailContent = file_get_contents("$path");
		$loginUrl = BASE_PATH . '/business/index.html';

		$replaceValues = array(
			'{{NAME}}' => $businessName,
			'{{MESSAGE}}' => $message,
			'{{TITLE}}' => $title,
			'{{URL}}' => $loginUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $businessAdminEmail);
	}
	//send Addon subscription  details TO Business SuperAdmin When 80% used and surpassed
	function SendAddonSubscriptionDetailsToSuperAdminSurpassed($businessName, $superAdminEmail, $message, $addonName)
	{
		$loginUrl = BASE_PATH . '/business/index.html';
		$subject = "ProBizCa -" . $addonName . " Addon limit exceeded";
		$title = $addonName . " Limit Surpassed";


		$path = ROOT_PATH . "/includes/emailtemplate/smsaddonpurchase80/smsaddonpurchasereach80.html";
		$emailContent = file_get_contents("$path");

		$replaceValues = array(
			'{{NAME}}' => $businessName,
			'{{MESSAGE}}' => $message,
			'{{TITLE}}' => $title,
			'{{URL}}' => $loginUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$sendEmail = $this->SendEmail($subject, $emailContent, $superAdminEmail);
	}


	function SendWalletTransactionReceipt($businessId, $ownerName, $amount, $email, $transactionId, $TransactionDateTime, $paymentMethodBrand, $paymentMethodLast4)
	{


		//Read admin Details
		$objDB = new clsDB();
		$sql = "SELECT businessName FROM business WHERE businessId=" . $businessId;
		$businessName = $objDB->GetSingleFieldValue($sql);

		unset($objDB);

		$subject = "ProBizCa - Transaction Confirmation Receipt for " . $businessName;

		$message = "This email is to confirm the successful completion of a transaction in your " . $businessName . " business wallet. Below are the specific details:";

		$path = ROOT_PATH . "/includes/emailtemplate/transactioncomplete/transactioncomplete.html";
		$emailContent = file_get_contents("$path");

		$replaceValues = array(
			'{{NAME}}' => $ownerName,
			'{{BUSINESSNAME}}' => $businessName,
			'{{MESSAGE}}' => $message,
			'{{TXNID}}' => $transactionId,
			'{{DATETIME}}' => $TransactionDateTime,
			'{{AMOUNT}}' => $amount,
			'{{CARDTYPE}}' => $paymentMethodBrand,
			'{{CARDNUMBER}}' => $paymentMethodLast4,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	function SendFreeTrailSubscriptionNotification($businessOwnerName, $businessName, $numberOfDays, $subject, $title, $description, $message, $message1, $email)
	{
		$loginUrl = BASE_PATH . '/business/index.html';

		$subject = APPLICATION_NAME . " - " . $subject;

		$path = ROOT_PATH . "/includes/emailtemplate/FreeTrail/freeTrail.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $businessOwnerName,
			'{{BUSINESSNAME}}' => $businessName,
			'{{DAYS}}' => $numberOfDays,
			'{{TITLE}}' => $title,
			'{{DESCRIPTION}}' => $description,
			'{{MESSAGE}}' => $message,
			'{{MESSAGE1}}' => $message1,
			'{{URL}}' => $loginUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);
		// exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}

	function SendChatAppNotification($userName, $businessName, $message, $email, $UserType)
	{
		if ($UserType == 1) {
			$loginUrl = BASE_PATH . '/business/index.html';
		} else if ($UserType == 2) {
			$loginUrl = BASE_PATH . '/customer/index.html';
		} elseif ($UserType == 3) {
			$loginUrl = BASE_PATH . '/affiliate/index.html';
		}

		$subject = APPLICATION_NAME . " - Notification";

		$title = "Chat App Notification";

		$path = ROOT_PATH . "/includes/emailtemplate/notification/notification.html";

		$emailContent = file_get_contents("$path");
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $userName,
			'{{BUSINESSNAME}}' => $businessName,
			'{{TITLE}}' => $title,
			'{{MESSAGE}}' => $message,
			'{{URL}}' => $loginUrl,
			'{{PROBIZCA_LOGO}}' => $this->proBizCaLogo,
			'{{BUSINESS_LOGO}}' => $this->proBizCaLogo,
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);
		// exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $email);
	}
}
