<?php
    //include classes and config file here	
	include('./includes/validatebusinesslogin.php');
	include('../includes/config.php');
    include('../includes/commonfun.php');
    include('../class/clsDB.php');
    include('../class/clsToDo.php');
	include('../class/clsBusinessContactActivity.php');	
	include('../class/clsBusiness.php');

	
	
    //***** TODO VARIABLES ******//
	    // $taskname = '';		
		$assignTags = '';
		$description = '';
		$comments = '';
	    $assignDate  = date("m/d/Y h:i A") ;
	    $businessId = $_SESSION["loggedBusinessId"];
	    $employeeId = $_SESSION["loggedEmployeeId"];
	    $assignEmployeeId = 0;
	    $todoId = 0;
	    $customerId = 0;
	    $isCompleted = '';
		$pageTitle = 'Add';
		$buttonTitle = 'Save';
		$employeeList = '';
		$dbtaskActivityId = 0;	
		$todoType =0;
		$viewSelectedAssignee = array();
		$dbcustomerId =0;
		$isCRM = 0;



		//***** ACTIVITIES *****//
		$objBusinessContactActivity = new clsBusinessContactActivity();
		$taskActivities = $objBusinessContactActivity->GetAllTaskActivity($businessId);
		unset($objBusinessContactActivity);	
		
		//***** GET ALL BUSINESS EMPLOYEE LIST *****//
		$objBusiness = new clsBusiness();
		$BusinessOwner = $objBusiness->CheckIsBusinessOwner($businessId,$employeeId);

		$contactList = $objBusiness->GetAllBusinessContactList($businessId);

		if($BusinessOwner)
		{
			$employeeId = 0;
		}

		$objToDo = new clsToDo();
		$employeeList = $objToDo->GetAllBusinessEmployeeLst($businessId);
		unset($objToDo);

		

		
	
    //**** EDIT MODE TODO *****//
		if(isset($_GET['id']))
		{		
			$todoId = DecodeQueryData($_GET['id']);
			$pageTitle = 'Edit';
		    $buttonTitle = 'Update';

			$empId = isset($_GET['empId']) ? DecodeQueryData($_GET['empId']) :  0;
		
			$objToDo = new clsToDo();
			$todoRow = $objToDo->GetToDoDetails($todoId);
			
			if($todoRow!='')
			{
				$dbtaskActivityId = stripslashes($todoRow['taskActivityId']);
				$todoType = stripslashes($todoRow['todoType']);
				$dbcustomerId = stripslashes($todoRow['customerId']);
				$assignDate =(date("m/d/Y h:i A",strtotime($todoRow['assignDate'])));					
				$assignTags = stripslashes($todoRow['assignTags']);					
				$description = stripslashes($todoRow['description']);
				$comments = stripslashes($todoRow['comments']);
				// $assignEmployeeId = stripslashes($todoRow['assignEmployeeId']);
			

				$selectedemployeeList = $objToDo->GetTodoAssigneeList($todoId,$empId);
					if ($selectedemployeeList != "") {
						while ($cos = mysqli_fetch_assoc($selectedemployeeList)) {
							$employeeId  = $cos['employeeId'];

							$assignDtls = $objToDo->GetToDoAssignDetails($todoId,$employeeId);
							$isCompleted = stripslashes($assignDtls['isCompleted']);
							array_push($viewSelectedAssignee, $employeeId);
						}
					}
			}
			unset($objToDo);

		}

		if(isset($_GET['customerId']))
		{
			$customerId = DecodeQueryData($_GET['customerId']);
			$dbcustomerId = $customerId;
		}

		if(isset($_GET['isCRM']))
		{
			$isCRM = DecodeQueryData($_GET['isCRM']);
			$todoType = 1;
		}
?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">
<head>
    <title><?php echo ($pageTitle);?> Task</title>
    <?php include('includes/headerCss.php'); ?> 
	<link href="<?php echo BASE_PATH;?>/assets/vendors/js/summernote/dist/summernote-bs4.css" rel="stylesheet">	
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/vendors/css/forms/selects/select2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/css/plugins/forms/extended/form-extended.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/fonts/simple-line-icons/style.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<style>
		/* Ensure error messages are visible */
		#error-contactList {
			color: #e74c3c !important;
			font-size: 12px !important;
			margin-top: 5px !important;
			display: block !important;
		}

		/* Parsley error styling */
		.parsley-errors-list {
			color: #e74c3c !important;
			font-size: 12px !important;
			margin-top: 5px !important;
			list-style: none !important;
			padding: 0 !important;
		}

		.parsley-error {
			border-color: #e74c3c !important;
		}

		/* Select2 error styling */
		.select2-container--default .select2-selection--single.parsley-error {
			border-color: #e74c3c !important;
		}
	</style>
</head>
<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

    <!-- include header-->
    <?php include('includes/header.php'); ?>
	
    <!-- BEGIN: Content-->
    <div class="app-content content">
        <div class="content-overlay"></div>
        <div class="content-wrapper">
            <div class="content-header row">
                <div class="content-header-left col-md-6 col-12 mb-2">
                    <h3 class="content-header-title">Task</h3>
                    <div class="row breadcrumbs-top">
                        <div class="breadcrumb-wrapper col-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.html">Dashboard</a>
                                </li>

								<li class="breadcrumb-item"><?php if($isCRM == 1) { ?> <a href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>">Contact</a> <?php } else { ?> <a href="todo.html">ToDo</a> <?php } ?>
                                </li>
                                <li class="breadcrumb-item active"><a href="javascript:void(0);"><?php echo ($pageTitle);?> Task</a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="content-header-right col-md-6 col-12">
                    <a href="todo.html">
						<div class="text-right">
						   <button type="button" class="btn btn-outline-primary btn-min-width mr-1 mb-1">Back</button>
						</div>
					</a>
                </div>
            </div>
            <div class="content-body">
                
                <!-- Basic form layout section start -->
                <section id="horizontal-form-layouts">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title" id="horz-layout-colored-controls">Task</h4>
                                    <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                    <div class="heading-elements">
                                        <ul class="list-inline mb-0">                                          
                                            <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                            <li><a data-action="expand"><i class="ft-maximize"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-content collpase show">
                                    <div class="card-body">                                       
                                        <form class="form form-horizontal" id="todoForm" method="post" action="addtasksubmit.html" enctype="multipart/form-data">
                                            <input type="hidden" name="todoId" id="todoId" value="<?php echo ($todoId);?>">
										    <input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId);?>">
										    <input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId);?>">
										    <input type="hidden" name="dbtaskActivityId" id="dbtaskActivityId" value="<?php echo ($dbtaskActivityId);?>">
										    <input type="hidden" name="isPrimary" id="isPrimary" value="<?php echo $_SESSION["isPrimary"];?>">
										    <input type="hidden" name="isCRM" id="isCRM" value="<?php echo $isCRM;?>">
										    
                                            <div class="form-body">
                                                <h4 class="form-section"><i class="la la-money"></i>Task</h4>
												<div class="row">
													 <div class="col-md-6">
														<input type="hidden" class="form-control" name="todoType" id="todoType" value="<?php echo $todoType; ?>">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="type">To Do Type<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<fieldset>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input todoType" name="todoType" id="todoTypeRadio1" value="0" onchange="toggelDiv(0);" <?php if ($todoType == 0) { ?> checked <?php } ?> <?php if($isCRM == 1) {?> disabled <?php } ?>>
																		<label class="custom-control-label" for="todoTypeRadio1" ></label>To Do</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input todoType" name="todoType" id="todoTypeRadio2" value="1" onchange="toggelDiv(1);" <?php if ($todoType == 1) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="todoTypeRadio2">Contact CRM</label>
																	</div>
																	
																</fieldset>
															</div>
														</div>
                                                    </div>
												</div> 
												<div class="row">                                                 
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="taskActivityId">Task Activity<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<select id="taskActivityId" name="taskActivityId" class="select2 form-control" <?php if($_SESSION["isPrimary"]==0) {?> disabled <?php }?> data-parsley-errors-container="#error-taskActivity" required>	
																	<option value="">Select Task Activity</option>																
																	<?php						  
																		if($taskActivities!='')
																		{	
																		
																			while($row = mysqli_fetch_assoc($taskActivities)) 
																			{
																				$activityId = $row['activityId']; 															  
																				$activityName = $row['activityName']; 															  
																				
																	?>
																			<option value="<?php echo ($activityId);?>"  <?php if($dbtaskActivityId == $activityId){?> selected <?php }?>><?php echo ($activityName);?></option>
																	<?php
																			} 
																		}
																	?>	
																</select>
																<div id="error-taskActivity"></div>
															</div>
														</div>
													</div>
													
													<div class="col-md-6" id="contactListDiv" class="hide">
														<div class="form-group row assign-employee">
															<label class="col-md-3 label-control" for="contactList">Contact List<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto" style="display:flex;flex-direction:column-reverse;">
																<select id="customerId" name="customerId" class="select2 form-control" <?php if($_SESSION["isPrimary"]==0 || $isCRM == 1) {?> disabled <?php }?> data-parsley-errors-container="#error-contactList" >	
																	<option value="">Select Contact</option>																
																	<?php

																		if ($contactList != "") {
																			while ($row = mysqli_fetch_assoc($contactList)) {
																				$customerId  = $row['customerId'];
																				$firstName  = stripslashes($row['firstName']);
																				$lastName  = stripslashes($row['lastName']);

																		?>
																				<option class="form-control" value="<?php echo ($customerId); ?>"   <?php if($dbcustomerId == $customerId){?> selected <?php }?>><?php echo ($firstName . ' ' . $lastName); ?></option>
																		<?php

																			}
																		}
																		?>
																</select>
																<div id="error-contactList"></div>
															</div>
														</div>
													</div>
													
													<div class="col-md-6">
                                                        <div class="form-group row assign-employee">
                                                            <label class="col-md-3 label-control" for="assignEmployeeId">Assign To<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
															    <?php if($_SESSION["isPrimary"]==0) {?>
																    <!-- <input type="hidden" name="assignEmployeeId" value="<?php echo ($assignEmployeeId);?>"> -->
																	<input type="hidden" name="employeeId[]" id="employeeId" value="<?php echo ($employeeId);?>">
																<?php }?>
																<select id="assignEmployeeId" name="assignEmployeeId[]" class="form-control" <?php if($_SESSION["isPrimary"]==0) {?> disabled <?php } else {?> multiple multiselect-search="true" multiselect-select-all="true"   <?php }?>  required >																	

																	<?php						  
																		if($employeeList!='')
																		{	
																			$empFullName = '';
																			while($row = mysqli_fetch_assoc($employeeList)) 
																			{
																				$employeeId = $row['employeeId']; 															  
																				$firstName = $row['firstName']; 															  
																				$lastName = $row['lastName']; 
																				$isPrimary = $row['isPrimary'];
																				if($isPrimary == 1)
																					$empFullName = $firstName.' '.$lastName.'- Business Owner';
																				else
																					$empFullName = $firstName.' '.$lastName;
																				
																	?>
																			<option value="<?php echo ($employeeId);?>" <?php if (in_array($employeeId, $viewSelectedAssignee)) { ?> selected="true" <?php } ?>><?php echo ($empFullName);?></option>
																	<?php
																			} 
																		}
																	?>	
																</select>
																<div id="error-assignto"></div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>													
												<div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="assignDate">Task Date<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">                                                                
																<div class="input-group date-picker-class">
																	<?php if($_SESSION["isPrimary"]==0) {?>
																		<input type="hidden" name="assignDate" value="<?php echo ($assignDate);?>">
																	<?php }?>
																	<input type="text" class="form-control" id="assignDate" <?php if($_SESSION["isPrimary"]==0) {?> readonly <?php }?> name="assignDate" value="<?php echo ($assignDate);?>" required>
																	<div class="input-group-append">
																		<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																	</div>
																</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- <div class="col-md-6">
													    <div class="form-group row">
															<label class="col-md-3 label-control" for="isCompleted">Task Status<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<select id="isCompleted" name="isCompleted" class="select2 form-control" required>																	
																	<option value="">Select</option>
																	<option value="0" <?php if($isCompleted ==0){?> selected <?php }?>>Incomplete</option>
																	<option value="1" <?php if($isCompleted ==1){?> selected <?php }?>>Completed</option>																					
																</select>
															</div>
														</div>
                                                    </div> -->
													<div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="description">Description</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Description"><?php echo ($description);?></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
													<div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="comments">Comments</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <textarea class="form-control" id="comments" name="comments" rows="2" placeholder="Comments"><?php echo ($comments);?></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
													
                                                </div>
												
												<div class="form-actions text-right">
													<a <?php if($isCRM == 1) {?> href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>" <?php } else {?> href="todo.html" <?php }?>>
														<button type="button" class="btn btn-outline-warning btn-glow mr-1">
															<i class="ft-x"></i> Cancel
														</button>
													</a>
													<button type="submit" id="btnSave" class="btn btn-outline-primary btn-glow">
														<i class="la la-check-square-o"></i> <?php echo ($buttonTitle);?>
													</button>
												</div>
                                        </form>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
    <!-- END: Content-->
   
    <!-- BEGIN: Footer-->
    <?php include('includes/footer.php'); ?> 
    <?php include('includes/footerJs.php'); ?> 
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/custom.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/parsleyjs/parsley.js"></script>	    
	<script src="<?php echo BASE_PATH; ?>/assets/js/multiselect-dropdown.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/js/scripts/forms/select/form-select2.js"></script>	
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/summernote/dist/summernote-bs4.min.js"></script>	
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/editorDemo.js"></script>	
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/forms/tags/form-field.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/js/scripts/forms/custom-file-input.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/js/datetimepicker/moment/moment.min.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/js/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>

    <script>
		// Ensure jQuery is loaded before executing
		if (typeof jQuery === 'undefined') {
			console.error('jQuery is not loaded!');
		}

		$(document).ready(function() {
			// Wait for Select2 to be fully initialized before adding classes
			setTimeout(function() {
				try {
					// Check if Select2 is initialized
					if (typeof $.fn.select2 !== 'undefined') {
						var taskActivityContainer = $('#select2-taskActivityId-container');
						var customerContainer = $('#select2-customerId-container');

						if (taskActivityContainer && taskActivityContainer.length > 0) {
							taskActivityContainer.addClass('required-select2');
						} else {
							// Fallback: try to find the select element and add class to its parent
							var taskSelect = $('#taskActivityId');
							if (taskSelect.length > 0) {
								taskSelect.next('.select2-container').find('.select2-selection').addClass('required-select2');
							}
						}

						if (customerContainer && customerContainer.length > 0) {
							customerContainer.addClass('required-select2');
						} else {
							// Fallback: try to find the select element and add class to its parent
							var customerSelect = $('#customerId');
							if (customerSelect.length > 0) {
								customerSelect.next('.select2-container').find('.select2-selection').addClass('required-select2');
							}
						}
					} else {
						console.warn('Select2 is not available');
					}
				} catch (error) {
					console.error('Error initializing Select2 containers:', error);
				}
			}, 1000); // Increased wait time to 1000ms for Select2 to initialize

			// Handle todo type visibility
			var todoType = $('#todoType').val();
			if(todoType == 1)
			{
				$('#contactListDiv').removeClass('hide');
				$('#customerId').prop("required", true);
				$('#customerId').attr("required", "required");

				// Refresh Parsley after making field required
				setTimeout(function() {
					if (typeof $('#todoForm').parsley === 'function') {
						$('#todoForm').parsley().refresh();
					}
				}, 100);
			}else{
				$('#contactListDiv').addClass('hide');
				$('#customerId').prop("required", false);
				$('#customerId').removeAttr("required");
				$('#customerId').val(''); // Clear value when hiding

				// Clear any validation errors and refresh Parsley
				$('#error-contactList').html('').hide();
				setTimeout(function() {
					if (typeof $('#todoForm').parsley === 'function') {
						$('#todoForm').parsley().refresh();
					}
				}, 100);
			}

			// Initialize date-time picker
			$('.date-picker-class').datetimepicker({
				icons: {
					time: 'fa fa-clock-o',
					date: 'fa fa-calendar',
					up: 'fa fa-chevron-up',
					down: 'fa fa-chevron-down',
					previous: 'fa fa-chevron-left',
					next: 'fa fa-chevron-right',
					today: 'fa fa-check',
					clear: 'fa fa-trash',
					close: 'fa fa-times'
				},
				allowInputToggle: true
			});

			// Form validation
			$('#todoForm').parsley().on('field:validated', function()
			{
				var ok = $('.parsley-error').length === 0;
				console.log('Field validated, errors:', $('.parsley-error').length);

				// Log which fields have errors
				$('.parsley-error').each(function() {
					console.log('Field with error:', $(this).attr('name') || $(this).attr('id'));
				});
			})
			.on('form:submit', function()
			{
				console.log('Form submit triggered');

				// Check if contact list is required and validate
				var todoType = $('input[name="todoType"]:checked').val();
				if (todoType == 1) {
					var customerValue = $('#customerId').val();
					console.log('Contact CRM selected, customer value:', customerValue);

					if (!customerValue || customerValue === '') {
						console.log('Customer not selected, showing error');
						$('#error-contactList').html('Please select a contact.').show();
						return false; // Prevent form submission
					} else {
						$('#error-contactList').html('').hide();
					}
				}

				return true; // Allow form submission
			});
		});

			function toggelDiv(type) {
				console.log('toggelDiv called with type:', type);

				if(type == 1)
				{
					console.log('Showing contact list and making it required');
					$('#contactListDiv').removeClass('hide');

					// Set required attribute multiple ways to ensure it works
					$('#customerId').prop("required", true);
					$('#customerId').attr("required", "required");

					// Also add required class for styling if needed
					$('#customerId').addClass('required');

					// If using Select2, we need to handle it specially
					if ($('#customerId').hasClass('select2-hidden-accessible')) {
						$('#customerId').select2('destroy');
						$('#customerId').prop("required", true).attr("required", "required");
						$('#customerId').select2(); // Reinitialize Select2
					}

					// Refresh Parsley validation to recognize the new required field
					if (typeof $('#todoForm').parsley === 'function') {
						$('#todoForm').parsley().refresh();
						console.log('Parsley validation refreshed');

						// Force validation of the customer field to show error if empty
						setTimeout(function() {
							if ($('#customerId').val() === '' || $('#customerId').val() === null) {
								$('#customerId').parsley().validate();
								console.log('Forced validation of customer field');
							}
						}, 200);
					}

					// Verify the required attribute was added
					console.log('Required attribute set:', $('#customerId').prop('required'));
					console.log('Required attribute in HTML:', $('#customerId').attr('required'));
				}else{
					console.log('Hiding contact list and removing required');
					$('#contactListDiv').addClass('hide');

					// Remove required attribute
					$('#customerId').prop("required", false);
					$('#customerId').removeAttr("required");
					$('#customerId').removeClass('required');
					$('#customerId').val('').trigger('change'); // Clear selection when hiding

					// Clear any existing validation errors
					$('#error-contactList').html('').hide();

					// If using Select2, handle it specially
					if ($('#customerId').hasClass('select2-hidden-accessible')) {
						$('#customerId').select2('destroy');
						$('#customerId').prop("required", false).removeAttr("required");
						$('#customerId').select2(); // Reinitialize Select2
					}

					// Refresh Parsley validation to remove the field from validation
					if (typeof $('#todoForm').parsley === 'function') {
						$('#todoForm').parsley().refresh();
						console.log('Parsley validation refreshed - field removed from validation');
					}

					// Verify the required attribute was removed
					console.log('Required attribute removed:', $('#customerId').prop('required'));
					console.log('Required attribute in HTML:', $('#customerId').attr('required'));
				}
			}

			// Test function to manually validate the contact field
			function testContactValidation() {
				console.log('=== Testing Contact Validation ===');

				var todoType = $('input[name="todoType"]:checked').val();
				console.log('Current todo type:', todoType);

				var isContactListVisible = !$('#contactListDiv').hasClass('hide');
				console.log('Contact list visible:', isContactListVisible);

				var isRequired = $('#customerId').prop('required');
				console.log('Customer field required:', isRequired);

				var customerValue = $('#customerId').val();
				console.log('Customer value:', customerValue);

				// Check error container
				var errorContainer = $('#error-contactList');
				console.log('Error container exists:', errorContainer.length > 0);
				console.log('Error container content:', errorContainer.html());
				console.log('Error container visible:', errorContainer.is(':visible'));

				// Check for Parsley errors
				var parsleyErrors = $('.parsley-errors-list');
				console.log('Parsley error elements found:', parsleyErrors.length);
				parsleyErrors.each(function(index) {
					console.log('Parsley error ' + index + ':', $(this).html());
				});

				// Test Parsley validation
				if (typeof $('#todoForm').parsley === 'function') {
					var isValid = $('#customerId').parsley().isValid();
					console.log('Parsley validation result:', isValid);

					// Force validation
					$('#customerId').parsley().validate();
					console.log('Forced validation completed');

					// Check again after validation
					setTimeout(function() {
						console.log('After validation - Error container content:', $('#error-contactList').html());
						console.log('After validation - Parsley errors:', $('.parsley-errors-list').length);
					}, 100);
				}

				console.log('=== End Test ===');
			}

			// Test function to manually trigger form validation
			function testFormValidation() {
				console.log('=== Testing Form Validation ===');

				if (typeof $('#todoForm').parsley === 'function') {
					var formValid = $('#todoForm').parsley().isValid();
					console.log('Form is valid:', formValid);

					// Force validation of all fields
					$('#todoForm').parsley().validate();
					console.log('Form validation completed');
				}

				console.log('=== End Form Test ===');
			}

			// Test function to manually show error message
			function testErrorDisplay() {
				console.log('=== Testing Error Display ===');

				// Show manual error message
				$('#error-contactList').html('Test error message').show();
				console.log('Manual error message set');

				// Check if it's visible
				setTimeout(function() {
					console.log('Error container visible:', $('#error-contactList').is(':visible'));
					console.log('Error container content:', $('#error-contactList').html());
				}, 100);

				console.log('=== End Error Display Test ===');
			}

			// Function to force show contact validation error
			function forceShowContactError() {
				// Make sure Contact CRM is selected
				$('#todoTypeRadio2').prop('checked', true);
				toggelDiv(1);

				// Clear the customer selection
				$('#customerId').val('').trigger('change');

				// Force validation
				setTimeout(function() {
					$('#customerId').parsley().validate();

					// Also show manual error
					$('#error-contactList').html('Please select a contact.').show();
					console.log('Forced contact error display');
				}, 200);
			}


    </script>
	
</script>

</body>
<!-- END: Body-->

</html>