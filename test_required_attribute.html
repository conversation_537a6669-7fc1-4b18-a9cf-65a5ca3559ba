<!DOCTYPE html>
<html>
<head>
    <title>Test Required Attribute Function</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .container { max-width: 600px; margin: 20px auto; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { width: 100%; padding: 8px; border: 1px solid #ccc; }
        .hide { display: none; }
        .required { border-color: red; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        .console-output { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: scroll; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Required Attribute Function</h1>
        
        <div class="test-section">
            <h3>Radio Button Test</h3>
            <div class="form-group">
                <label>To Do Type:</label>
                <input type="radio" name="todoType" value="0" onchange="toggelDiv(0);" checked> To Do
                <input type="radio" name="todoType" value="1" onchange="toggelDiv(1);"> Contact CRM
            </div>
            
            <div id="contactListDiv" class="form-group hide">
                <label for="customerId">Contact List (Required when Contact CRM is selected):</label>
                <select id="customerId" name="customerId">
                    <option value="">Select Contact</option>
                    <option value="1">John Doe</option>
                    <option value="2">Jane Smith</option>
                    <option value="3">Bob Johnson</option>
                </select>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Manual Tests</h3>
            <button onclick="testRequiredAttribute()">Test Required Attribute</button>
            <button onclick="checkCurrentState()">Check Current State</button>
            <button onclick="clearConsole()">Clear Console</button>
        </div>
        
        <div class="test-section">
            <h3>Console Output</h3>
            <div id="console-output" class="console-output"></div>
        </div>
    </div>

    <script>
        // Override console.log to show in our custom console
        var originalLog = console.log;
        console.log = function() {
            originalLog.apply(console, arguments);
            var output = document.getElementById('console-output');
            var message = Array.prototype.slice.call(arguments).join(' ');
            output.innerHTML += message + '\n';
            output.scrollTop = output.scrollHeight;
        };

        function toggelDiv(type) {
            console.log('toggelDiv called with type: ' + type);
            
            if(type == 1)
            {
                console.log('Showing contact list and making it required');
                $('#contactListDiv').removeClass('hide');
                
                // Set required attribute multiple ways to ensure it works
                $('#customerId').prop("required", true);
                $('#customerId').attr("required", "required");
                
                // Also add required class for styling if needed
                $('#customerId').addClass('required');
                
                // Verify the required attribute was added
                console.log('Required attribute set: ' + $('#customerId').prop('required'));
                console.log('Required attribute in HTML: ' + $('#customerId').attr('required'));
            }else{
                console.log('Hiding contact list and removing required');
                $('#contactListDiv').addClass('hide');
                
                // Remove required attribute
                $('#customerId').prop("required", false);
                $('#customerId').removeAttr("required");
                $('#customerId').removeClass('required');
                $('#customerId').val('').trigger('change'); // Clear selection when hiding
                
                // Verify the required attribute was removed
                console.log('Required attribute removed: ' + $('#customerId').prop('required'));
                console.log('Required attribute in HTML: ' + $('#customerId').attr('required'));
            }
        }

        function testRequiredAttribute() {
            console.log('=== Manual Test Started ===');
            
            // Test setting required
            console.log('Setting required attribute...');
            $('#customerId').prop("required", true);
            $('#customerId').attr("required", "required");
            console.log('Required prop: ' + $('#customerId').prop('required'));
            console.log('Required attr: ' + $('#customerId').attr('required'));
            
            // Test removing required
            console.log('Removing required attribute...');
            $('#customerId').prop("required", false);
            $('#customerId').removeAttr("required");
            console.log('Required prop: ' + $('#customerId').prop('required'));
            console.log('Required attr: ' + $('#customerId').attr('required'));
            
            console.log('=== Manual Test Completed ===');
        }

        function checkCurrentState() {
            console.log('=== Current State Check ===');
            console.log('Contact div visible: ' + !$('#contactListDiv').hasClass('hide'));
            console.log('Select required prop: ' + $('#customerId').prop('required'));
            console.log('Select required attr: ' + $('#customerId').attr('required'));
            console.log('Select value: ' + $('#customerId').val());
            console.log('Select has required class: ' + $('#customerId').hasClass('required'));
            console.log('=== End State Check ===');
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }

        $(document).ready(function() {
            console.log('Page loaded and jQuery ready');
            console.log('jQuery version: ' + $.fn.jquery);
        });
    </script>
</body>
</html>
