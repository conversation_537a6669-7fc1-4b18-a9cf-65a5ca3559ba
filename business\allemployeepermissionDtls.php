<?php
//include classes and config file here
include('./includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsBusiness.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsBusinessPlanMaster.php');
include('../class/clsEmployee.php');
include('../class/clsBusinessModulePermissions.php');


//variables
$employeeData = '';
$subCardLimitr = 0;
$moduleId = 3;
$subModuleId = 10;
$empArray = [];
$businessId = $_SESSION["loggedBusinessId"];
$empId = 0;
$employeeIds = '';
$loggedEmployeeId = $_SESSION["loggedEmployeeId"];

$viewSelectedAssignee = array();

//object
$objBusiness = new clsBusiness();
$employeeList = $objBusiness->GetAllBusinessEmployees($status = 1, $businessId);
unset($objBusiness);
?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
    <title>Employee-Permissions</title>
    <?php include('includes/headerCss.php'); ?>
    <?php include('includes/datatableCss.php'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/animate/animate.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/extensions/sweetalert2.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/selects/select2.min.css">
	<link rel="stylesheet" href="<?php echo BASE_PATH;?>/superadmin/vendors/alertifyjs/css/alertify.css">


</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

    <!-- include header-->
    <?php include('includes/header.php'); ?>
    <!-- BEGIN: Content-->
    <div class="app-content content">
        <div class="content-overlay"></div>
        <div class="content-wrapper">
            <div class="content-header row">
                <div class="content-header-left col-md-6 col-12 mb-2">
                    <h3 class="content-header-title">Permissions</h3>
                    <div class="row breadcrumbs-top">
                        <div class="breadcrumb-wrapper col-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.html">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item"><a href="#">Employee</a>
                                </li>
                                <li class="breadcrumb-item active"><a href="#">Permissions</a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="content-header-right col-md-6 col-12">
                    <div class="text-right">
                        <a href="employee.html">
                            <button type="button" class="btn btn-outline-primary btn-min-width btn-glow mb-1">Back</button>
                        </a>
                    </div>
                </div>
            </div>

            <div class="content-body">
                <!-- Default ordering table -->
                <section id="ordering">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <input type="hidden" name="businessId" id="businessId" value="<?php echo $businessId; ?>">
                                    <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                    <div class="heading-elements">
                                        <ul class="list-inline mb-0">
                                            <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                            <li><a data-action="expand"><i class="ft-maximize"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-content collapse show">
                                    <div class="card-body card-dashboard">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered default-ordering" id="permissionTable">
                                                <thead>
                                                    <tr>
                                                        <th class="text-center">Employee/Independent</th>
                                                        <?php
                                                        $objBusinessModulePermissions = new clsBusinessModulePermissions();
                                                        $permissionArray = $objBusinessModulePermissions->GetBusinessModules($moduleId, $subModuleId);
                                                        ?>
                                                        <?php foreach ($permissionArray as $permission) :

                                                            // Skip module with moduleId equal to 10
                                                            if ($permission['moduleId'] == 10) {
                                                                continue;
                                                            }
                                                        ?>

                                                            <th class="text-center"><?php echo $permission['moduleName']; ?></th>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    // Fetch permissions outside the loop
                                                    $objBusinessModulePermissions = new clsBusinessModulePermissions();
                                                    $permissionArray = $objBusinessModulePermissions->GetBusinessModules($moduleId, $subModuleId);

                                                    if ($employeeList) {
                                                        while ($row = mysqli_fetch_assoc($employeeList)) {
                                                            $employeeId = $row['employeeId'];
                                                            $businessId = $row['businessId'];
                                                            $firstName = $row['firstName'];
                                                            $lastName = $row['lastName'];

                                                            // Fetch permissions details for the current employee
                                                            $permissionDtls = $objBusinessModulePermissions->GetBusinessModulesByUserId($businessId, $employeeId,3);
                                                            $userPermissions = array(); // Initialize an array to hold user permissions for each module

                                                            if ($permissionDtls != '') {
                                                                while ($permissionRow = mysqli_fetch_assoc($permissionDtls)) {
                                                                    // Store the permission details for each moduleId
                                                                    $moduleId = $permissionRow['moduleId'];
                                                                    $userPermissions[$moduleId]['businessModulePermissionId'] = $permissionRow['businessModulePermissionId'];
                                                                    $userPermissions[$moduleId]['isAll'] = $permissionRow['isAll'];
                                                                }
                                                            }
                                                    ?>
                                                            <tr>
                                                                <td><?php echo ($firstName . ' ' . $lastName); ?></td>

                                                                <?php foreach ($permissionArray as $permission) :
                                                                    // Skip module with moduleId equal to 10
                                                                    if ($permission['moduleId'] == 10) {
                                                                        continue;
                                                                    }

                                                                    // Check if the module has isAll set to 1 or if the user has permission for this module
                                                                    $isChecked = isset($userPermissions[$permission['moduleId']]) && $userPermissions[$permission['moduleId']]['isAll'] == 1;
                                                                ?>
                                                                    <td class="text-center">
                                                                        <input class="clsSetPermission" type="checkbox" id="<?php echo $permission['moduleId']; ?>" name="moduleIdArray[]" value="<?php echo $permission['moduleId']; ?>" moduleId="<?php echo $permission['moduleId']; ?>" parent_id="<?php echo $permission['parent_id']; ?>" businessModulePermissionId="<?php echo isset($userPermissions[$permission['moduleId']]) ? $userPermissions[$permission['moduleId']]['businessModulePermissionId'] : 0; ?>" employeeId="<?php echo $employeeId; ?>" <?php echo $isChecked ? 'checked' : ''; ?>>
                                                                    </td>
                                                                <?php endforeach; ?>
                                                            </tr>
                                                    <?php
                                                        }
                                                    }
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!--/ Default ordering table -->


            </div>
        </div>
    </div>
    <!-- END: Content-->

    <!-- BEGIN: Footer-->
    <?php include('includes/footer.php'); ?>
    <?php include('includes/footerJs.php'); ?>
    <?php include('includes/datatableJs.php'); ?>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/polyfill.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/select/form-select2.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/multiselect-dropdown.js"></script>
    <script src="<?php echo BASE_PATH;?>/superadmin/vendors/alertifyjs/alertify.js"></script>

    <script>
        $(document).ready(function() {
        });
        //datatable alignment
        var current_datatable = $("#permissionTable").DataTable({
            "order": [
                [1, "ASC"]
            ],
            "language": {
                // "info": "Showing _PAGE_ of _PAGES_ Entries",
                "lengthMenu": "Show _MENU_ Entries"
            },
            "aoColumns": [{
                    "sWidth": "50%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                }
            ],
        });
  
        //update status
        $('.clsSetPermission').on('click', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var businessModulePermissionId = $(this).attr('businessModulePermissionId');
            var moduleId = $(this).attr('moduleId');
            var isChecked = $(this).is(":checked") ? 1 : 0;
            var parent_id = $(this).attr('parent_id');
            var roleId = 3;
            var UserId = $(this).attr('employeeId');
            var businessId = $('#businessId').val();

            $.ajax({
                type: "GET",
                url: "../ajax/ajax_set_businessModule_permissions.html",
                data: {
                    businessId: businessId,
                    UserId: UserId,
                    roleId: roleId,
                    businessModulePermissionId: businessModulePermissionId,
                    moduleId: moduleId,
                    parent_id: parent_id,
                    isChecked: isChecked,

                },
                success: function() {

					// alertify.success("Status updated!");

                    Swal.fire({
                        type: "success",
                        title: 'Status updated!',
                        confirmButtonClass: 'btn btn-outline-success btn-glow',
                    });
                    // $('#permissionTable').data('datatable').ajax.reload();ss
                    location.reload();
                }
            });
        });
    </script>
</body>
<!-- END: Body-->
</html>