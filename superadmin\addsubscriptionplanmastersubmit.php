<?php
	//exit;
	include('includes/validatesystemuserlogin.php');
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
    include('../class/clsPlanMaster.php');

	//Check Its Add or Edit	
	if($_SERVER['REQUEST_METHOD']=='POST')
	{
	    $planId  = isset($_POST['planId']) ? trim($_POST['planId']) : "";
		$objPlanMaster = new clsPlanMaster();

			
			// exit;

		//Get All Inputs
		$objPlanMaster->price = $_POST['price'];
		$objPlanMaster->title = $_POST['title'];
		$objPlanMaster->contactLimit = $_POST['contactLimit'];
		$objPlanMaster->cardLimit = $_POST['cardLimit'];
		$objPlanMaster->subCardLimit = $_POST['subCardLimit'];
		$objPlanMaster->allowThemeEditing = isset($_POST['allowThemeEditing'])?1:0;

		$objPlanMaster->imageInfographSectionLimit = $_POST['imageInfographSectionLimit'];
		$objPlanMaster->videoInfographSectionLimit = $_POST['videoInfographSectionLimit'];
		$objPlanMaster->imageCarouselLimit = $_POST['imageCarouselLimit'];
		$objPlanMaster->imageInfographLimit = $_POST['imageInfographLimit'];

		$objPlanMaster->videoInfographLimit = $_POST['videoInfographLimit'];
		$objPlanMaster->formLimit = $_POST['formLimit'];

		$objPlanMaster->allowLogoInclusion = isset($_POST['allowLogoInclusion'])?1:0;
		$objPlanMaster->allowPhotoInclusion = isset($_POST['allowPhotoInclusion'])?1:0;
		$objPlanMaster->allowVideoProfile = isset($_POST['allowVideoProfile'])?1:0;
		$objPlanMaster->isDedicatedURL = isset($_POST['isDedicatedURL'])?1:0;
		$objPlanMaster->isCrmIntergration = isset($_POST['isCrmIntergration'])?1:0;
		$objPlanMaster->isCouponServices = isset($_POST['isCouponServices'])?1:0;
		$objPlanMaster->isWhiteLabel = isset($_POST['isWhiteLabel'])?1:0;
		$objPlanMaster->isPDFSubscription = isset($_POST['isPDFSubscription'])?1:0;
		$objPlanMaster->chatRoom = isset($_POST['chatRoom'])?1:0;
		$objPlanMaster->shoppingCart = isset($_POST['shoppingCart'])?1:0;
		$objPlanMaster->pushNotification = isset($_POST['pushNotification'])?1:0;
		
		$objPlanMaster->email = isset($_POST['email'])?1:0;
		$objPlanMaster->defaultEmail = isset($_POST['defaultEmail'])?$_POST['defaultEmail']:0;
		$objPlanMaster->smschk = isset($_POST['smschk'])?1:0;
		$objPlanMaster->defaultSms = isset($_POST['defaultSms'])?$_POST['defaultSms']:0;
		$objPlanMaster->surveychk = isset($_POST['surveychk'])?1:0;

		$objPlanMaster->customQrFormchk = isset($_POST['customQrFormchk'])?1:0;
		
		$objPlanMaster->geolocation = isset($_POST['geolocation'])?1:0;
		$objPlanMaster->socialfeed = isset($_POST['socialfeed'])?1:0;
		$objPlanMaster->statistics = isset($_POST['statistics'])?1:0;
		$objPlanMaster->support = isset($_POST['support'])?1:0;
		$objPlanMaster->todo = isset($_POST['todo'])?1:0;
		$objPlanMaster->probizcaplus = isset($_POST['probizcaplus'])?1:0;
		$objPlanMaster->invoice = isset($_POST['invoice'])?1:0;
		
		//new code
		$objPlanMaster->infoContentLimit = $_POST['infoContentLimit'];
		$objPlanMaster->infoSectionLiimit = $_POST['infoSectionLiimit'];
		$objPlanMaster->galleryImageLimit = $_POST['galleryImageLimit'];
		
		$objPlanMaster->annualAmount = $_POST['annualAmount'];
		$objPlanMaster->semiAnnualAmount = $_POST['semiAnnualAmount'];
		$objPlanMaster->quarterlyAmount = $_POST['quarterlyAmount'];
		$objPlanMaster->durationDays = isset($_POST['durationDays']) ? $_POST['durationDays'] : 0;
		$type = $_POST['type'];
		$retplanId = $objPlanMaster->SavePlanMaster($planId);
		
		if($retplanId > 0)
		{
			//Update Type
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('planmaster','type',$type,'planId',$retplanId);
			unset($objDB);
			$objPlanMaster->UpdatePlanForAllBusiness($retplanId);

			// $GetBusiness=$objPlanMaster->GetBusinessPlanDetails($planId,0);
			// while($row = mysqli_fetch_assoc($GetBusiness))
			// {
			// 	// echo "<pre>";
			// 	$businessIds = $row['businessId'];
			// 	$objPlanMaster->UpdateDefaultCount($businessIds,1,$_POST['defaultEmail']);
			// 	$objPlanMaster->UpdateDefaultCount($businessIds,2,$_POST['defaultSms']);

			// }	
			// exit;
		}

		$messageText = $planId ? 'updated' : 'added';
		header('location:viewsubscriptionplan.html?status='.$messageText);
		exit();	
	}
	else
	{
		header('location:viewsubscriptionplan.html?status=Error');
		exit;
	}
	
?>