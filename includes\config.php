<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->safeLoad();

// Get the current environment
$app_env = $_ENV['APP_ENV'] ?? 'local';

// Configure based on environment
switch ($app_env) {
    case 'local':
        // Local environment settings
        define("DB_HOST", $_ENV['LOCAL_DUBLIN_HOST'] ?? "localhost");
        define("DB_USER", $_ENV['LOCAL_DUBLIN_CLOSE'] ?? "root");
        define("DB_PASSWORD", $_ENV['LOCAL_DUBLIN_OPEN'] ?? "");
        define("DB_NAME", $_ENV['LOCAL_DUBLIN_NAME'] ?? "pprobizc_live");
        define("BASE_PATH", $_ENV['LOCAL_BASE_PATH'] ?? "http://localhost/probizca");
        define("SCAN_PATH", $_ENV['LOCAL_SCAN_PATH'] ?? "localhost/probizca");
        define("ROOT_PATH", $_ENV['LOCAL_ROOT_PATH'] ?? "C:/xampp/htdocs/probizca");
        
        // Stripe keys local
        define("STRIPE_PUBLISHABLE_KEY", $_ENV['LOCAL_SAN_FRANCISCO'] );
        define("STRIPE_API_KEY", $_ENV['LOCAL_SAN_FRANCISCO_K']);

        // Chat App Local
        define("CHAT_CLIENT_ID", $_ENV['LOCAL_COLORADO_ID']);
        define("CHAT_CLIENT_SECRET_KEY", $_ENV['LOCAL_COLORADO_HONG']);
        define("CHAT_APIKEY", $_ENV['LOCAL_COLORADO_LONG']);
        define("CHAT_DOMAIN_BACKEND", $_ENV['LOCAL_COLORADO_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['LOCAL_COLORADO_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['LOCAL_COLORADO_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['LOCAL_COLORADO_NOTI']);
        
        // Debug settings
        define("DISPLAY_ERRORS", true);
        define("DEBUG_MODE", true);
        break;
        
    case 'development':
        // Development environment settings
        define("DB_HOST", $_ENV['DEV_DUBLIN_HOST']);
        define("DB_USER", $_ENV['DEV_DUBLIN_CLOSE']);
        define("DB_PASSWORD", $_ENV['DEV_DUBLIN_OPEN']);
        define("DB_NAME", $_ENV['DEV_DUBLIN_NAME']);
        define("BASE_PATH", $_ENV['DEV_BASE_PATH']);
        define("SCAN_PATH", $_ENV['DEV_SCAN_PATH']);
        define("ROOT_PATH", $_ENV['DEV_ROOT_PATH']);

        // Stripe keys development
        define("STRIPE_PUBLISHABLE_KEY", $_ENV['DEV_SAN_FRANCISCO'] );
        define("STRIPE_API_KEY", $_ENV['DEV_SAN_FRANCISCO_K']);
        
        // Chat App development
        define("CHAT_CLIENT_ID", $_ENV['DEV_COLORADO_ID']);
        define("CHAT_CLIENT_SECRET_KEY", $_ENV['DEV_COLORADO_HONG']);
        define("CHAT_APIKEY", $_ENV['DEV_COLORADO_LONG']);
        define("CHAT_DOMAIN_BACKEND", $_ENV['DEV_COLORADO_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['DEV_COLORADO_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['DEV_COLORADO_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['DEV_COLORADO_NOTI']);
        
        // Debug settings
        define("DISPLAY_ERRORS", true);
        define("DEBUG_MODE", true);
        break;
        
    case 'production':
    default:
        // Production environment settings
        define("DB_HOST", $_ENV['PROD_DUBLIN_HOST']);
        define("DB_USER", $_ENV['PROD_DUBLIN_CLOSE']);
        define("DB_PASSWORD", $_ENV['PROD_DUBLIN_OPEN']);
        define("DB_NAME", $_ENV['PROD_DUBLIN_NAME']);
        define("BASE_PATH", $_ENV['PROD_BASE_PATH']);
        define("SCAN_PATH", $_ENV['PROD_SCAN_PATH']);
        define("ROOT_PATH", $_ENV['PROD_ROOT_PATH']);

        // Stripe keys production
        define("STRIPE_PUBLISHABLE_KEY", $_ENV['PROD_SAN_FRANCISCO'] );
        define("STRIPE_API_KEY", $_ENV['PROD_SAN_FRANCISCO_K']);

        
        // Chat App production
        define("CHAT_CLIENT_ID", $_ENV['PROD_COLORADO_ID']);
        define("CHAT_CLIENT_SECRET_KEY", $_ENV['PROD_COLORADO_HONG']);
        define("CHAT_APIKEY", $_ENV['PROD_COLORADO_LONG']);
        define("CHAT_DOMAIN_BACKEND", $_ENV['PROD_COLORADO_BACKEND']);
		define("CHAT_DOMAIN_FRONTEND", $_ENV['PROD_COLORADO_FRONTEND']);
		define("CHAT_USER_SERVICE", $_ENV['PROD_COLORADO_SERVICE']);
		define("CHAT_NOTIFICATION_SERVICE", $_ENV['PROD_COLORADO_NOTI']);
        
        // Debug settings
        define("DISPLAY_ERRORS", false);
        define("DEBUG_MODE", false);
        break;
}

// Common settings for all environments
define("DEVELOPMENT", $_ENV['DEVELOPMENT'] ?? "1");

// Encryption Key
define("ENCRYPTION_KEY", $_ENV['ENCRYPTION_KEY']);

// SENDGRID_API_KEY  
// define("SENDGRID_API_KEY", $_ENV['SENDGRID_API_KEY']);

// // SMS key
// define("ACCOUNT_SID", $_ENV['ACCOUNT_SID']);
// define("AUTH_TOKEN_ID", $_ENV['AUTH_TOKEN_ID']);
// define("REGISTER_NUMBER", $_ENV['REGISTER_NUMBER']);

// SuperAdmin Contact numbers
define("SUPERADMIN1_NUMBER", $_ENV['SUPERADMIN1_NUMBER']);
define("SUPERADMIN2_NUMBER", $_ENV['SUPERADMIN2_NUMBER']);

define("SUPERADMIN1_NUMBERTEST", $_ENV['SUPERADMIN1_NUMBERTEST']);
define("SUPERADMIN2_NUMBERTEST", $_ENV['SUPERADMIN2_NUMBERTEST']);

// SuperAdmin Email
define("SUPERADMIN_EMAIL", $_ENV['SUPERADMIN_EMAIL']);  
define("SUPERADMIN_EMAILTEST", $_ENV['SUPERADMIN_EMAILTEST']);


// PRBMGUN
define("PRB_MAIL_GUN_API_KEY", $_ENV['PRB_MINNESOTA']);
define("PRB_MAIL_GUN_DOMAIN", $_ENV['PRB_MINNESOTA_MAIN']);
define("PRB_MAIL_GUN_FROM_EMAIL", $_ENV['PRB_MINNESOTA_MAIL']);
define("PRB_MAIL_GUN_FROM_NAME", $_ENV['PRB_MINNESOTA_NAME']);


// ClickSend SMS configuration
define("COLUMBIA_USERNAME", isset($_ENV['COLUMBIA_USERNAME']) ? $_ENV['COLUMBIA_USERNAME'] : '');
define("COLUMBIA_LONG", isset($_ENV['COLUMBIA_LONG']) ? $_ENV['COLUMBIA_LONG'] : '');
define("COLUMBIA_NUMBER", isset($_ENV['COLUMBIA_NUMBER']) ? $_ENV['COLUMBIA_NUMBER'] : '');

// Common settings for all environments
define("COOKIE_EXPIRY", $_ENV['COOKIE_EXPIRY'] ?? "1728000");
define("APPLICATION_NAME", $_ENV['APPLICATION_NAME'] ?? "ProBizCa");
define("APPLICANTION_EMAIL", $_ENV['APPLICANTION_EMAIL'] ?? "");
define("APPLICANTION_ADMIN_EMAIL", $_ENV['APPLICANTION_ADMIN_EMAIL'] ?? "");
date_default_timezone_set($_ENV['DEFAULT_SERVER_TIMEZONE']);
define("DEFAULT_SERVER_TIMEZONE", $_ENV['DEFAULT_SERVER_TIMEZONE']);
define("DEFAULT_COUNTRY_ID", $_ENV['DEFAULT_COUNTRY_ID']);
define("APIKEY", $_ENV['ARIZONA_K']);
define("SECRETKEY", $_ENV['SANDIEGO_K']);
