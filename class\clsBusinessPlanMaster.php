<?php
class clsBusinessPlanMaster
{	

    var $quarterlyAmount = 0;
	var $semiAnnualAmount = 0;
	var $annualAmount = 0;
	var $businessId = 0;
	var $businessPlanDetailId = 0;
	var $planId = 0;
	var $price = 0;
	var $contactLimit = 0;
	var $cardLimit = '0';
	var $subCardLimit = '0';
	var $imageInfographSectionLimit = 0;
	var $videoInfographSectionLimit = 0;
	var $allowThemeEditing = 0;
	var $imageCarouselLimit = 0;
	var $imageInfographLimit = 0;
	var $videoInfographLimit = 0;
	var $formLimit = 0;
	var $allowLogoInclusion = 0;
	var $allowPhotoInclusion = 0;
	var $allowVideoProfile = 0;
	var $isDedicatedURL = 0;
	var $isCrmIntergration = 0;
	var $isCouponServices = 0;
	var $isWhiteLabel = 0;
	var $isPDFSubscription = 0;
	var $is24x7Support = 0;	
	var $subscriptionStartDate = 0;	
	var $subscriptionEndDate = 0;	
	var $paymentPlan = 0;	
	var $createdDate = 0;
	var $email = 0;
	var $smschk = 0;
	var $surveychk = 0;
	var $customQrFormchk = 0;	
	var $geolocation = 0;
	var $socialfeed = 0;
	var $statistics = 0;
	var $support = 0;
	var $todo = 0;
	var $probizcaplus = 0;
	var $invoice = 0;
	var $infoContentLimit = 0;
	var $infoSectionLiimit = 0;
	var $galleryImageLimit = 0;
	var $isSetupFee = 0;
	var $pushNotification = 0;
	var $shoppingCart = 0;
	var $appliedPlanAmt = 0;
	var $chatRoom =0;
	
	function SaveBusinessPlanMaster($businessPlanDetailId)
	{
		$objDB = new clsDB();
		$sql = '';
		if($businessPlanDetailId >0)
		{
				$sql = "UPDATE businessplansubscriptiondetails SET
							title='".$this->title."',
							price=".$this->price.",
							contactLimit='".($this->contactLimit)."',
							cardLimit='".($this->cardLimit)."',
							subCardLimit='".($this->subCardLimit)."',
							imageInfographSectionLimit='".($this->imageInfographSectionLimit)."',
							videoInfographSectionLimit='".($this->videoInfographSectionLimit)."',
							allowThemeEditing=".($this->allowThemeEditing).",
							imageCarouselLimit='".($this->imageCarouselLimit)."',
							imageInfographLimit='".($this->imageInfographLimit)."',
							videoInfographLimit='".($this->videoInfographLimit)."',
							formLimit='".($this->formLimit)."',
							allowLogoInclusion=".($this->allowLogoInclusion).",
							allowPhotoInclusion=".($this->allowPhotoInclusion).",
							allowVideoProfile=".($this->allowVideoProfile).",
							isDedicatedURL=".($this->isDedicatedURL).",
							isCrmIntergration=".($this->isCrmIntergration).",
							isCouponServices=".($this->isCouponServices).",
							isWhiteLabel=".($this->isWhiteLabel).",
							isPDFSubscription=".($this->isPDFSubscription).",
							pushNotification=".($this->pushNotification).",
							shoppingCart=".($this->shoppingCart).",
							chatRoom=".($this->chatRoom).",							
							businessId=".($this->businessId).",
							email=".($this->email).",
							smschk=".($this->smschk).",
							surveychk=".($this->surveychk).",
							customQrFormchk=".($this->customQrFormchk).",
							geolocation=".($this->geolocation).",
							socialfeed=".($this->socialfeed).",
							statistics=".($this->statistics).",
							support=".($this->support).",
							todo=".($this->todo).",
							probizcaplus=".($this->probizcaplus).",
							invoice=".($this->invoice).",
							planId=".($this->planId).",
							infoSectionLiimit=".($this->infoSectionLiimit).",
							infoContentLimit=".($this->infoContentLimit).",
							annualAmount=".($this->annualAmount).",
							semiAnnualAmount=".($this->semiAnnualAmount).",
							quarterlyAmount=".($this->quarterlyAmount).",
							galleryImageLimit=".($this->galleryImageLimit).",
							subscriptionStartDate='".addslashes($this->subscriptionStartDate)."',
							subscriptionEndDate='".addslashes($this->subscriptionEndDate)."',
							paymentPlan=".($this->paymentPlan).",
							appliedPlanAmt=".($this->appliedPlanAmt).",
							isSetupFee=".($this->isSetupFee)."
							WHERE businessPlanDetailId=".$businessPlanDetailId;
							// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}
		else
		{
		    $sql = "INSERT INTO businessplansubscriptiondetails(price,title,contactLimit,cardLimit,subCardLimit,imageInfographSectionLimit,videoInfographSectionLimit,allowThemeEditing,
										imageCarouselLimit,imageInfographLimit,videoInfographLimit,formLimit,allowLogoInclusion,allowPhotoInclusion,
										allowVideoProfile,isDedicatedURL,isCrmIntergration,isCouponServices,isWhiteLabel,isPDFSubscription,subscriptionStartDate,pushNotification,
										shoppingCart,chatRoom,businessId,email,smschk,surveychk,customQrFormchk,geolocation,socialfeed,statistics,support,todo,probizcaplus,invoice,planId
										,infoContentLimit,infoSectionLiimit,quarterlyAmount,semiAnnualAmount,annualAmount,galleryImageLimit) 
								VALUES(								  				
									  '".addslashes($this->price)."',								
									  '".addslashes($this->title)."',								
									  '".addslashes($this->contactLimit)."',								
									  '".addslashes($this->cardLimit)."',								
									  '".addslashes($this->subCardLimit)."',									
									  '".addslashes($this->imageInfographSectionLimit)."',	
									  '".addslashes($this->videoInfographSectionLimit)."',								
									  '".addslashes($this->allowThemeEditing)."',								
									  '".addslashes($this->imageCarouselLimit)."',								
									  '".addslashes($this->imageInfographLimit)."',	
									  '".addslashes($this->videoInfographLimit)."',	
									  '".addslashes($this->formLimit)."',	
									  '".addslashes($this->allowLogoInclusion)."',										 	
									  '".addslashes($this->allowPhotoInclusion)."',										 	
									  '".addslashes($this->allowVideoProfile)."',	
									  '".addslashes($this->isDedicatedURL)."',	
									  '".addslashes($this->isCrmIntergration)."',	
									  '".addslashes($this->isCouponServices)."',	
									  '".addslashes($this->isWhiteLabel)."',	
									  '".addslashes($this->isPDFSubscription)."', 									  
									  '".addslashes($this->subscriptionStartDate)."', 
									  '".addslashes($this->pushNotification)."',										
									  '".addslashes($this->shoppingCart)."',										
									  '".addslashes($this->chatRoom)."',										
									  '".addslashes($this->businessId)."',										
									  '".addslashes($this->email)."',										
									  '".addslashes($this->smschk)."',										
									  '".addslashes($this->surveychk)."',										
                                      '".addslashes($this->customQrFormchk)."',										
									  '".addslashes($this->geolocation)."',										
									  '".addslashes($this->socialfeed)."',										
									  '".addslashes($this->statistics)."',										
									  '".addslashes($this->support)."',										
									  '".addslashes($this->todo)."',										
									  '".addslashes($this->probizcaplus)."',		 								
									  '".addslashes($this->invoice)."',										
									  '".addslashes($this->planId)."',
                                      '".addslashes($this->infoContentLimit)."',  										
									  '".addslashes($this->infoSectionLiimit)."',
                                      '".addslashes($this->quarterlyAmount)."',										
									  '".addslashes($this->semiAnnualAmount)."',										
									  '".addslashes($this->annualAmount)."', 									  
									  '".addslashes($this->galleryImageLimit)."'									  
									  )";
            // echo $sql;exit;									  
			$businessPlanDetailId = $objDB->ExecuteInsertQuery($sql);
		   
		}
		
		unset($objDB);
		return $businessPlanDetailId;
	}

	function HistoryBusinessPlanMaster($businessPlanDetailId)
	{
		$objDB = new clsDB();
		$sql = '';
		if($businessPlanDetailId >0)
		{
				$sql = "insert into businessplansubscriptiondetails_h (businessPlanDetailId,planId,appliedPlanAmt,notify30,notify15,businessId,title,price,quarterlyAmount,annualAmount,semiAnnualAmount,contactLimit,cardLimit,subCardLimit,imageInfographSectionLimit,videoInfographSectionLimit,allowThemeEditing,imageCarouselLimit,imageInfographLimit,videoInfographLimit,formLimit,allowLogoInclusion,allowPhotoInclusion,allowVideoProfile,isDedicatedURL,isCrmIntergration,isCouponServices,isWhiteLabel,isPDFSubscription,is24x7Support,subscriptionStartDate,subscriptionEndDate,paymentPlan,createdDate,x_invoice_num,shoppingCart,chatRoom,pushNotification,email,smschk,surveychk,customQrFormchk,geolocation,socialfeed,statistics,support,todo,probizcaplus,invoice,status,infoContentLimit,infoSectionLiimit,paymentType,isSetupFee,galleryImageLimit,type) select * from businessplansubscriptiondetails where `businessPlanDetailId`=$businessPlanDetailId";
							// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
            // echo $sql;exit;									  
			$businessPlanDetailId = $objDB->ExecuteInsertQuery($sql);
		   
		}
		
		unset($objDB);
		return $businessPlanDetailId;
	}
	
	function GetBusinessPlanDetails($businessId,$planId)
	{
		$rows = "";
		$objDB = new clsDB();
		// $sql = "SELECT * FROM businessplansubscriptiondetails WHERE businessId='".$businessId."'";
		$sql = "SELECT bsp.*,adtl1.*,adtl2.defaultCount as dfSMS, adtl2.dfUsed AS dfsmsU FROM businessplansubscriptiondetails AS bsp
		LEFT JOIN addondetails as adtl1 ON bsp.businessId = adtl1.businessId AND adtl1.addonNmId=1 
		LEFT JOIN addondetails as adtl2 ON bsp.businessId = adtl2.businessId AND adtl2.addonNmId=2 
		WHERE bsp.businessId='".$businessId."'";
        if($planId >0)
		{
		    $sql .=" AND planId='".$planId."'";
		}		
        // echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetBusinessPlanDetails1($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM businessplansubscriptiondetails INNER JOIN business ON business.businessId=businessplansubscriptiondetails.businessId WHERE businessplansubscriptiondetails.businessId='".$businessId."'";
        // if($planId >0)
		// {
		//     $sql .=" AND planId='".$planId."'";
		// }		,$planId
        // echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetAllActivePlans()
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM planmaster WHERE status=1 ORDER BY title ASC ";	 
		// echo $sql;exit; 		 
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	function SetSubscriptionPlanStatus($id, $status)
	{
		if($id > 0)
		{
			$objDB = new clsDB();
			$sql = "UPDATE planmaster SET status =".$status." WHERE planId = ".$id; 
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function GetAllBusinessContactList($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.customerId,customer.firstName,customer.lastName FROM businesscustomer
		        INNER JOIN business ON businesscustomer.businessId = business.businessId		        
		        RIGHT JOIN customer ON businesscustomer.customerId = customer.customerId		        
				WHERE business.businessId='".$businessId."' GROUP BY businesscustomer.customerId ORDER BY  CONCAT(customer.firstName , ' ', customer.lastName) ASC";
        // echo $sql;exit;				
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	// For Cronjob 
	function SendSubscriptionExpiryNotification($noOfDays)
	{ 
		$currentDate = date("Y-m-d H:i:s");

		// 1) 30 Days Before Expiration 2) 15 Days Before Expiration.
		//  latest updated : 13 feb 25
		$rows = "";
		$objDB = new clsDB();

		$sql = 'SELECT bpsd.*, 
						(SELECT SUM(amount) FROM `businesstransaction` 
						WHERE businessId = bpsd.businessId AND transactionType = \'Cr\') AS cramt,
						(SELECT SUM(amount) FROM `businesstransaction` 
						WHERE businessId = bpsd.businessId AND transactionType = \'Db\') AS dramt
				FROM `businessplansubscriptiondetails` AS bpsd
				WHERE (COALESCE((SELECT SUM(amount) FROM `businesstransaction` 
								WHERE businessId = bpsd.businessId AND transactionType = \'Cr\'), 0) - 
						COALESCE((SELECT SUM(amount) FROM `businesstransaction` 
								WHERE businessId = bpsd.businessId AND transactionType = \'Db\'), 0)) < bpsd.appliedPlanAmt
				AND bpsd.paymentPlan > 1 
				AND bpsd.subscriptionEndDate < DATE_ADD(\''.date("Y-m-d", strtotime($currentDate)).'\', INTERVAL '.$noOfDays.' DAY)';
		
				if ($noOfDays == 30) {
					$sql .= ' AND bpsd.notify30 = 0';
				} else {
					$sql .= ' AND bpsd.notify15 = 0';
				}
 
        // echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}


	function CheckBussinessSubscriptionIsExpired()
	{
		$currentDate = date("Y-m-d H:i:s");

		$rows = "";
		$objDB = new clsDB();
		// $sql = 'SELECT * FROM `businessplansubscriptiondetails` 
		// 		WHERE subscriptionEndDate < Now() and (SELECT sum(amount) FROM `businesstransaction` 
		// 		WHERE businessId=businessplansubscriptiondetails.businessId and transactionType="Cr")-(SELECT sum(amount) 
		// 		FROM `businesstransaction` 
		// 		WHERE businessId=businessplansubscriptiondetails.businessId 
		// 		and transactionType="db") < appliedPlanAmt';

		// $sql = 'SELECT *,
		// (SELECT sum(amount) FROM `businesstransaction` WHERE businessId=businessplansubscriptiondetails.businessId and transactionType="Cr") as cramt,
		// (SELECT sum(amount) FROM `businesstransaction` WHERE businessId=businessplansubscriptiondetails.businessId and transactionType="db") as dramt
		// FROM `businessplansubscriptiondetails` HAVING (cramt-dramt) < appliedPlanAmt AND businessplansubscriptiondetails.paymentPlan > 1 AND date(businessplansubscriptiondetails.subscriptionEndDate) < "'.date("Y-m-d",strtotime($currentDate)).'"';

		$sql = "SELECT bpsd.*, COALESCE(SUM(CASE WHEN bt.transactionType = 'Cr' THEN bt.amount END), 0) AS cramt, COALESCE(SUM(CASE WHEN bt.transactionType = 'Db' THEN bt.amount END), 0) AS dramt
        		FROM businessplansubscriptiondetails bpsd
        		LEFT JOIN businesstransaction bt ON bpsd.businessId = bt.businessId
        		GROUP BY bpsd.businessId
				HAVING (cramt - dramt) < bpsd.appliedPlanAmt 
           		AND bpsd.paymentPlan >= 1 
           		AND bpsd.subscriptionEndDate < '".date("Y-m-d", strtotime($currentDate))."'";

        // echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;

	}

	function CheckBussinessWalletAmountForRenew()
	{
		$currentDate = date("Y-m-d H:i:s");

		$rows = "";
		$objDB = new clsDB();
		// $sql = 'SELECT * 
		// FROM `businessplansubscriptiondetails` 
		// WHERE DATE(subscriptionEndDate) = Date("'.date("Y-m-d",strtotime($currentDate)).'") and IFNULL((SELECT sum(amount) FROM `businesstransaction` 
		// WHERE businessId=businessplansubscriptiondetails.businessId and transactionType="Cr"),0)-IFNULL((SELECT sum(amount) 
		// FROM `businesstransaction` WHERE businessId=businessplansubscriptiondetails.businessId and transactionType="db"),0)>=appliedPlanAmt AND appliedPlanAmt>0';
		$sql = 'SELECT bpsd.*, 
               COALESCE((SELECT SUM(amount) FROM `businesstransaction` 
                         WHERE businessId = bpsd.businessId AND transactionType = \'Cr\'), 0) AS cramt, 
               COALESCE((SELECT SUM(amount) FROM `businesstransaction` 
                         WHERE businessId = bpsd.businessId AND transactionType = \'Db\'), 0) AS dramt 
        FROM `businessplansubscriptiondetails` AS bpsd 
        WHERE DATE(bpsd.subscriptionEndDate) = \''.date("Y-m-d", strtotime($currentDate)).'\'
          AND (COALESCE((SELECT SUM(amount) FROM `businesstransaction` 
                         WHERE businessId = bpsd.businessId AND transactionType = \'Cr\'), 0) 
               - 
               COALESCE((SELECT SUM(amount) FROM `businesstransaction` 
                         WHERE businessId = bpsd.businessId AND transactionType = \'Db\'), 0)) 
              >= bpsd.appliedPlanAmt 
          AND bpsd.appliedPlanAmt > 0';

        // echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
		
	}

	// Free Trail Subsciption  
	function CheckBussinessFreeSubscriptionIsExpired()
	{
		$currentDate = date("Y-m-d H:i:s");

		$rows = "";
		$objDB = new clsDB();
		// $sql = 'SELECT 
		// 	    bsp.businessPlanDetailId, bsp.planId, bsp.businessId, bsp.subscriptionStartDate, bsp.subscriptionEndDate,
    	// 		business.businessName, business.planId AS BplanId, adtl1.*, adtl2.addondtlId AS addondtlSmsId, adtl2.defaultCount as dfSMS, adtl2.dfUsed AS dfsmsU,employee.employeeId,employee.firstName,employee.lastName,employee.email,employee.phoneNo
		// 		FROM businessplansubscriptiondetails AS bsp 
		// 		INNER JOIN business ON bsp.businessId = business.businessId
		// 		LEFT JOIN addondetails as adtl1 ON bsp.businessId = adtl1.businessId AND adtl1.addonNmId=1 
		// 		LEFT JOIN addondetails as adtl2 ON bsp.businessId = adtl2.businessId AND adtl2.addonNmId=2 
		// 		INNER JOIN employee ON business.businessId=employee.businessId
				
		// 		WHERE bsp.planId=11 AND date(bsp.subscriptionEndDate) < "'.date("Y-m-d",strtotime($currentDate)).'"';

		$sql = 'SELECT DISTINCT 
				bsp.businessPlanDetailId, 
				bsp.planId, 
				bsp.businessId, 
				bsp.subscriptionStartDate, 
				bsp.subscriptionEndDate,
				business.businessName, 
				business.planId AS BplanId, 
				adtl1.addondtlId AS addondtl1Id, 
				adtl1.defaultCount AS adtl1DefaultCount, 
				adtl1.dfUsed AS adtl1DfUsed,
				adtl2.addondtlId AS addondtlSmsId, 
				adtl2.defaultCount AS dfSMS, 
				adtl2.dfUsed AS dfsmsU,
				employee.employeeId,
				employee.firstName,
				employee.lastName,
				employee.email,
				employee.phoneNo
			FROM businessplansubscriptiondetails AS bsp 
			INNER JOIN business ON bsp.businessId = business.businessId
			LEFT JOIN addondetails AS adtl1 
				ON bsp.businessId = adtl1.businessId AND adtl1.addonNmId = 1 
			LEFT JOIN addondetails AS adtl2 
				ON bsp.businessId = adtl2.businessId AND adtl2.addonNmId = 2 
			INNER JOIN employee 
				ON business.businessId = employee.businessId
			WHERE bsp.planId = 11 
			AND bsp.subscriptionEndDate < "' . date("Y-m-d", strtotime($currentDate)) . '"
			GROUP BY bsp.businessPlanDetailId, bsp.businessId';  // Prevent duplicates

        // echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	// Notify Free Trail user 2 & 1 Day befor subscription expire.
	function CheckBussinessFreeSubscriptionNotify()
	{
		$currentDate = date("Y-m-d H:i:s");

		$rows = "";
		$objDB = new clsDB();
		$sql = 'SELECT 
				bsp.businessPlanDetailId, bsp.planId, bsp.businessId, bsp.subscriptionStartDate, bsp.subscriptionEndDate,
				business.businessName, business.planId AS BplanId, adtl1.*, adtl2.defaultCount as dfSMS, adtl2.dfUsed AS dfsmsU,employee.employeeId,employee.firstName,employee.lastName,employee.email,employee.phoneNo
			FROM 
				businessplansubscriptiondetails AS bsp 
			INNER JOIN 
				business ON bsp.businessId = business.businessId
			LEFT JOIN 
				addondetails as adtl1 ON bsp.businessId = adtl1.businessId AND adtl1.addonNmId = 1 
			LEFT JOIN 
				addondetails as adtl2 ON bsp.businessId = adtl2.businessId AND adtl2.addonNmId = 2 
			INNER JOIN 
				employee ON business.businessId=employee.businessId				
			WHERE 
				bsp.planId = 11 AND 

				(date(bsp.subscriptionEndDate) BETWEEN "'.date("Y-m-d",strtotime($currentDate)).'" AND DATE_ADD("'.date("Y-m-d",strtotime($currentDate)).'", INTERVAL 2 DAY))';
        // echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;

	}

	
	
	
}
?>