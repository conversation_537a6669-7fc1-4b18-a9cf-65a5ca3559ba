<IfModule mod_rewrite.c>

RewriteEngine On

RewriteCond %{THE_REQUEST} (.*)\.php  
RewriteRule ^(.*)\.php $1.html [R=301,L]  

RewriteCond %{THE_REQUEST} (.*)\.html  
RewriteRule ^(.*)\.html $1.php [L]  

#Probizca Template
RewriteRule ^mb\/([^/]*)$ index.php?orgslug=$1 [L,NC]
RewriteRule ^mb\/([^/]*)/$ index.php?orgslug=$1 [L,NC]

RewriteRule ^mb\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&status=$2 [L,NC]
RewriteRule ^mb\/([^/]*)\/([^/]*)/$ index.php?orgslug=$1&status=$2 [L,NC]

RewriteRule ^mb\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2 [L,NC]
RewriteRule ^mb\/([^/]*)\/([^/]*)/$ index.php?orgslug=$1&shareType=$2 [L,NC]
 
RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3 [L,NC]
RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3 [L,NC] 

RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4 [L,NC]
RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4 [L,NC] 

RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4&isPrimary=$5 [L,NC]
RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4&isPrimary=$5 [L,NC]  

RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4&isPrimary=$5&isAddToHome=$6 [L,NC]
RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4&isPrimary=$5&isAddToHome=$6 [L,NC] 

RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4&isPrimary=$5&isAddToHome=$6&isSaveVcard=$7 [L,NC] 
RewriteRule ^mb\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ index.php?orgslug=$1&shareType=$2&qrcode=$3&refType=$4&isPrimary=$5&isAddToHome=$6&isSaveVcard=$7 [L,NC] 


#Survey Forms
RewriteRule ^survey\/([^/]*)$ survey.php?orgslug=$1 [L,NC]
RewriteRule ^survey\/([^/]*)/$ survey.php?orgslug=$1 [L,NC] 

RewriteRule ^survey\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2 [L,NC]
RewriteRule ^survey\/([^/]*)\/([^/]*)/$ survey.php?orgslug=$1&isPreview=$2 [L,NC]

RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3 [L,NC]
RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3 [L,NC] 


RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3&businessId=$4 [L,NC]
RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3&businessId=$4 [L,NC] 

RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3&businessId=$4&assignId=$5 [L,NC]
RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3&businessId=$4&assignId=$5 [L,NC]  


RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3&businessId=$4&assignId=$5&assignUserId=$6 [L,NC]
RewriteRule ^survey\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ survey.php?orgslug=$1&isPreview=$2&surveyId=$3&businessId=$4&assignId=$5&assignUserId=$6 [L,NC] 


RewriteRule ^calendarEvent\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ calendarEvent.php?orgslug=$1&businessId=$2&eventId=$3&customerId=$4 [L,NC]
RewriteRule ^calendarEvent\/([^/]*)\/([^/]*)\/([^/]*)\/([^/]*)$ calendarEvent.php?orgslug=$1&businessId=$2&eventId=$3&customerId=$4 [L,NC] 

RewriteRule ^redirect\/([^/]*)$ redirect.php?redirectUrlId=$1 [L,NC]
RewriteRule ^redirect\/([^/]*)/$ redirect.php?redirectUrlId=$1 [L,NC] 

</IfModule>


# BEGIN Expires
<ifModule mod_expires.c>
ExpiresActive On
ExpiresDefault "access plus 1 seconds"
ExpiresByType text/html "access plus 1 seconds"
ExpiresByType image/gif "access plus 2592000 seconds"
ExpiresByType image/jpeg "access plus 2592000 seconds"
ExpiresByType image/png "access plus 2592000 seconds"
ExpiresByType text/css "access plus 604800 seconds"
ExpiresByType text/javascript "access plus 216000 seconds"
ExpiresByType application/x-javascript "access plus 216000 seconds"
</ifModule>
# END Expires

<IfModule mod_headers.c>
  # <FilesMatch "\.(ttf|ttc|otf|eot|woff|font.css|css|woff2)$">
  #   Header set Access-Control-Allow-Origin "*"
  # </FilesMatch>
  <FilesMatch ".*">
    Require all granted
  </FilesMatch>
</IfModule>

# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca/.*$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca/.*$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca/.*$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca/.*$      [NC]
# RewriteCond %{HTTP_REFERER} !^http://localhost/probizca$      [NC]
# RewriteRule .*\.(jpg|jpeg|gif|png|bmp)$ - [F,NC]

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php70” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php70 .php .php7 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
<FilesMatch "^\.env">
  Order allow,deny
  Deny from all
</FilesMatch>