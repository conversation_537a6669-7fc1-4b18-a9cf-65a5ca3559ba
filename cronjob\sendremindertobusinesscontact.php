<?php 
    include('../includes/config.php');	
	include('../includes/commonfun.php');
	include_once('../class/clsDB.php');
	include('../class/clsCronJob.php');	
	include('../class/clsCustomer.php');
    include('../class/class.phpmailer.php');
	include('../class/class.smtp.php');
    include('../class/clsSendEmails.php');
	include('../class/clsSendEventNotification.php');
	include('../class/clsBusiness.php');	
    include('../class/clsNotification.php');
	include('../class/clsAddonMaster.php');
	include('../class/clsPushNotification.php');
    require  '../class/Twilio/autoload.php';
	include('../class/clsCalendarEvents.php');
	use Twilio\Rest\Client;	

	$errorMsg = '';
	$businessId = 0;
    $objAddonMaster = new clsAddonMaster();
    
    $objBusiness = new clsBusiness();
    $employeeId=$objBusiness->CheckBusinesAsAnEmployee($businessId);

	$objCalendarEvents = new clsCalendarEvents();
	$allEvents = $objCalendarEvents->GetCronEvents(); 
	// unset($objCalendarEvents);

    // echo date('Y-m-d H:i:s');
// 	exit;
    // echo '<pre>';
	// print_r($allEvents);exit;
	$alldata = array();
	if($allEvents!='')
	{
        while($row = mysqli_fetch_assoc($allEvents))
		{
				$recurrenceEndDate = strtotime($row['recurrenceEndDate']);

				if(date('Y-m-d H:i') <= date('Y-m-d H:i', $recurrenceEndDate) )
				{
				$start = strtotime($row['start']);
 
               $recurrenceType  = stripslashes($row['recurrenceType']);  

				$datediff = $recurrenceEndDate-$start;
				$dayDiff = round($datediff / (60 * 60 * 24));
				
				if($recurrenceType == 1)
				{
					$recurrenceDay = '1 day';
					$dayDiff = round($datediff / (60 * 60 * 24));
					
				}
				else if($recurrenceType == 2)
				{
					$recurrenceDay = '7 day';
					$dayDiff = round($datediff / (7 * 60 * 60 * 24));
					
				}
				else if($recurrenceType == 3)
				{
					$recurrenceDay = '14 day';
					$dayDiff = round($datediff / (14 * 60 * 60 * 24));
					
				}
				else if($recurrenceType == 4)
				{
					$recurrenceDay = '1 month';
					$dayDiff = getMonthCount($recurrenceEndDate,$start);
					
				}
				else if($recurrenceType == 5)
				{
					$recurrenceDay = '3 month';
					$dayDiff = getMonthCount($recurrenceEndDate,$start);
					
				}
				else if($recurrenceType == 6)
				{
					$recurrenceDay = '1 year';
					$dayDiff = round($datediff / (7 * 60 * 60 * 24));
				}
				else
				{
                    $recurrenceDay = '0 day';
				    
                }	
				array_push($alldata, $row);
				
				$startDate = $row['start'];
                $lastStartDate = $row['start'];
                
                $customerId = stripslashes($row['customerId']);
                $firstName = stripslashes($row['firstName']);
                $lastName = stripslashes($row['lastName']);
                $email = stripslashes($row['email']);
                $phoneNo = stripslashes($row['phoneNo']);
                $eventId = stripslashes($row['id']);
                $businessId = stripslashes($row['businessId']);
                $description = stripslashes($row['description']);
                $title =  stripslashes($row['title']);
                // $startDate =  stripslashes($row['start']);
                $url = stripslashes($row['url']);
                $reminderType = stripslashes($row['reminderType']);
                // echo "default 152 lne -> ".$defaultEmailCnt;
                $fullName = $firstName.' '.$lastName;
         
            $objAddonMaster = new clsAddonMaster();
    		$emailsmsCount = $objAddonMaster->GetPurchaseAddonDetailsInRow($businessId);
    		if($emailsmsCount !='')
    		{	
                    // pks for Email / SMS
                    $addondtlId_Email = $emailsmsCount['addondtlId_Email'];
                    $addondtlId_Sms = $emailsmsCount['addondtlId_Sms']; 

    				// EMAIL
    				$defaultEmailCnt = $emailsmsCount['defaultCount'];
    				$defaultEmailUsedCnt = $emailsmsCount['dfUsed'];
    				$purchasedEmailCnt = $emailsmsCount['purchasedCount'];
    				$purchaseUsedEmailCnt = $emailsmsCount['purUsed'];
    
    				$remainEmailCount = ($defaultEmailCnt - $defaultEmailUsedCnt) + ($purchasedEmailCnt - $purchaseUsedEmailCnt);
    				
    				// SMS
    				$defaultSMSCnt = $emailsmsCount['dfSMS'];
    			// echo "defaulSMSUsedCnt ".
                	$defaulSMSUsedCnt = $emailsmsCount['dfsmsU'];
    				$purchasedSMSCnt = $emailsmsCount['SmsPurchaseCnt'];
    				$purchaseUsedSMSCnt = $emailsmsCount['SmsPurUsed'];
    
    				$remainSMSCount = ($defaultSMSCnt - $defaulSMSUsedCnt) + ($purchasedSMSCnt - $purchaseUsedSMSCnt);
    		}
                

				     $obj = new clsDB();
                     $businessName = $obj->GetSingleColumnValueFromTable('business','businessName','businessId',$businessId);
                     $displayURLTag = $obj->GetSingleColumnValueFromTable('business','displayURLTag','businessId',$businessId);
                     unset($obj);
				     $UpcomingRemiderDate = date('Y-m-d H:i:s', strtotime($startDate.' + '.$recurrenceDay.''));

                    //  echo "<br>";
                    //  echo "<br>";
                    //  echo "recurrenceType ".$recurrenceType;
                    //  echo "<br>";
                    //  echo "<br>";
				    
				    if($recurrenceType > 0)
				    {
	
        			    if($row['reminderalertdate'] == '' || $row['reminderalertdate'] == '0000-00-00 00:00:00')
        			    {
        			        $UpcomingRemiderDate = $startDate;
        			        $lastStartDate = $UpcomingRemiderDate;
        			    }
        			    else
        			    {
        			        $lastStartDate = $row['reminderalertdate'];
                        
                            $UpcomingRemiderDate = date('Y-m-d H:i:s', strtotime($lastStartDate.' + '.$recurrenceDay.''));
        			    }
        			
        			        $reminderDateTime = date('Y-m-d H:i', strtotime($UpcomingRemiderDate.' - '.$row['reminderalertId'].' minute'));
        			        // echo "inside reminderDateTime ".$reminderDateTime;
        			   
        			        if($reminderDateTime == date('Y-m-d H:i'))
        			        {
                                // echo "inside recurrenceType ".$recurrenceType;
                                // echo "inside defaultSMSCnt ".$defaultSMSCnt;

        			            //send email
        			            if($reminderType == 1 && $email!='')
                                {
                                     if($defaultEmailCnt > $defaultEmailUsedCnt)
                                    {
                                        sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                        
                                        $defaultEmailUsedCnt = $defaultEmailUsedCnt + 1;
                                        $objAddonMaster->UpdateDefaultUsedCount($businessId,1,$defaultEmailUsedCnt,$addondtlId_Email);

                                        if(($defaultEmailCnt * 80)/100 == $defaultEmailUsedCnt) 
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,1);
                                        }
                                    }
                                    else if($purchasedEmailCnt >  $purchaseUsedEmailCnt)
                                    {
                                        sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                        
                                        $purchaseUsedEmailCnt = $purchaseUsedEmailCnt + 1;
                                        $objAddonMaster->UpdatePurchaseUsedCount($businessId,1,$purchaseUsedEmailCnt,$addondtlId_Email);

                                        if(($purchasedEmailCnt * 80)/100 == $purchaseUsedEmailCnt)
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,1);
                                        }
                                    }
                                    else
                                    {
                                        addonLimitiReached($businessId,$employeeId,1);
                                    }
                                }
                                else if($reminderType == 2 && $phoneNo!='')
        				         {
        				             if($defaultSMSCnt > $defaulSMSUsedCnt)
                                    {

                                        sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);
                                        
                                        $defaulSMSUsedCnt = $defaulSMSUsedCnt + 1;
    								    $objAddonMaster->UpdateDefaultUsedCount($businessId,2,$defaulSMSUsedCnt,$addondtlId_Sms);
    
                                        if(($defaultSMSCnt * 80)/100 == $defaulSMSUsedCnt) 
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,2);
                                        }
                                    }
                                    else if($purchasedSMSCnt >  $purchaseUsedSMSCnt)
                                    {
                                        sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);

                                        
                                        $purchaseUsedSMSCnt = $purchaseUsedSMSCnt + 1;
                                        $objAddonMaster->UpdatePurchaseUsedCount($businessId,2,$purchaseUsedSMSCnt,$addondtlId_Sms);
    
                                        if(($purchasedSMSCnt * 80)/100 == $purchaseUsedSMSCnt)
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,2);
                                        }
                                    }
                                    else
                                    {
                                        addonLimitiReached($businessId,$employeeId,2);
                                    }
        				         }
        				         else if($reminderType == 3)
    				            {
        				             if($email!='')
        				             {
        				                 if($defaultEmailCnt > $defaultEmailUsedCnt)
                                        {
                                            sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                            
                                            $defaultEmailUsedCnt = $defaultEmailUsedCnt + 1;
                                            $objAddonMaster->UpdateDefaultUsedCount($businessId,1,$defaultEmailUsedCnt,$addondtlId_Email);
        
                                            if(($defaultEmailCnt * 80)/100 == $defaultEmailUsedCnt) 
                                            {
                                                AddonNotify80Percent($businessId,$employeeId,1);
                                            }
                                        }
                                        else if($purchasedEmailCnt >  $purchaseUsedEmailCnt)
                                        {
                                            sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                            
                                            $purchaseUsedEmailCnt = $purchaseUsedEmailCnt + 1;
                                            $objAddonMaster->UpdatePurchaseUsedCount($businessId,1,$purchaseUsedEmailCnt,$addondtlId_Email);
        
                                            if(($purchasedEmailCnt * 80)/100 == $purchaseUsedEmailCnt)
                                            {
                                                AddonNotify80Percent($businessId,$employeeId,1);
                                            }
                                        }
                                        else
                                        {
                                            addonLimitiReached($businessId,$employeeId,1);
                                        }
        				             }
    				             
        				             if($phoneNo!=''  )
        				             {
        				                 if($defaultSMSCnt > $defaulSMSUsedCnt)
                                        {
                                            sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);

                                            
                                            $defaulSMSUsedCnt = $defaulSMSUsedCnt + 1;
    									    $objAddonMaster->UpdateDefaultUsedCount($businessId,2,$defaulSMSUsedCnt,$addondtlId_Sms);
    
                                            if(($defaultSMSCnt * 80)/100 == $defaulSMSUsedCnt) 
                                            {
                                                AddonNotify80Percent($businessId,$employeeId,2);
                                            }
                                        }
                                        else if($purchasedSMSCnt >  $purchaseUsedSMSCnt)
                                        {
                                            sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);
                                            
                                            $purchaseUsedSMSCnt = $purchaseUsedSMSCnt + 1;
                                            $objAddonMaster->UpdatePurchaseUsedCount($businessId,2,$purchaseUsedSMSCnt,$addondtlId_Sms);
    
                                            if(($purchasedSMSCnt * 80)/100 == $purchaseUsedSMSCnt)
                                            {
                                                AddonNotify80Percent($businessId,$employeeId,2);
                                            }
                                        }
                                        else
                                        {
                                            addonLimitiReached($businessId,$employeeId,2);
                                        }
        				             }
    				            }
        				            $objDB = new clsDB();
                                    $objDB->UpdateSingleColumnValueToTable('calendarevents','reminderalertdate',$lastStartDate,'id',$row['id']);
                                    unset($objDB);
        			        }
        			    
				    }
				    else
				    {
        			    $reminderDateTime = date('Y-m-d H:i', strtotime($UpcomingRemiderDate.' - '.$row['reminderalertId'].' minute'));
        			 
				        if($reminderDateTime == date('Y-m-d H:i'))
				        {
				            if($reminderType == 1 && $email!='')
    				         {
    				              if($defaultEmailCnt > $defaultEmailUsedCnt)
                                    {
                                        sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                        
                                        $defaultEmailUsedCnt = $defaultEmailUsedCnt + 1;
                                        $objAddonMaster->UpdateDefaultUsedCount($businessId,1,$defaultEmailUsedCnt,$addondtlId_Email);
                                        
                                        if(($defaultEmailCnt * 80)/100 == $defaultEmailUsedCnt) 
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,1);
                                        }
                                    }
                                    else if($purchasedEmailCnt > $purchaseUsedEmailCnt)
                                    {
                                        sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                        
                                        $purchaseUsedEmailCnt = $purchaseUsedEmailCnt + 1;
                                        $objAddonMaster->UpdatePurchaseUsedCount($businessId,1,$purchaseUsedEmailCnt,$addondtlId_Email);
                                        
                                        if(($purchasedEmailCnt * 80)/100 == $purchaseUsedEmailCnt)
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,1);
                                        }
                                    }
                                    else
                                    {
                                        addonLimitiReached($businessId,$employeeId,1);
                                    }
    				             
    				         }
    				         else if($reminderType == 2 && $phoneNo!='')
    				         {
    				             if($defaultSMSCnt > $defaulSMSUsedCnt)
                                    {
                                        sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);

                                        
                                        $defaulSMSUsedCnt = $defaulSMSUsedCnt + 1;
									    $objAddonMaster->UpdateDefaultUsedCount($businessId,2,$defaulSMSUsedCnt,$addondtlId_Sms);

                                        if(($defaultSMSCnt * 80)/100 == $defaulSMSUsedCnt) 
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,2);
                                        }
                                    }
                                    else if($purchasedSMSCnt >  $purchaseUsedSMSCnt)
                                    {
                                        sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);

                                        
                                        $purchaseUsedSMSCnt = $purchaseUsedSMSCnt + 1;
                                        $objAddonMaster->UpdatePurchaseUsedCount($businessId,2,$purchaseUsedSMSCnt,$addondtlId_Sms);

                                        if(($purchasedSMSCnt * 80)/100 == $purchaseUsedSMSCnt)
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,2);
                                        }
                                    }
                                    else
                                    {
                                        addonLimitiReached($businessId,$employeeId,2);
                                    }
    				             
    				         }
    				         else if($reminderType == 3)
    				         {
    				             if($email!='')
    				             {
    				                 if($defaultEmailCnt > $defaultEmailUsedCnt)
                                    {
                                        sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                        
                                        $defaultEmailUsedCnt = $defaultEmailUsedCnt + 1;
                                        $objAddonMaster->UpdateDefaultUsedCount($businessId,1,$defaultEmailUsedCnt,$addondtlId_Email);
                                       

                                        if(($defaultEmailCnt * 80)/100 == $defaultEmailUsedCnt) 
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,1);
                                        }
                                    }
                                    else if($purchasedEmailCnt >  $purchaseUsedEmailCnt)
                                    {
                                        sendEmail($fullName,$eventId,$email,$businessName,$startDate);
                                        
                                        $purchaseUsedEmailCnt = $purchaseUsedEmailCnt + 1;
                                        $objAddonMaster->UpdatePurchaseUsedCount($businessId,1,$purchaseUsedEmailCnt,$addondtlId_Email);

                                        if(($purchasedEmailCnt * 80)/100 == $purchaseUsedEmailCnt)
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,1);
                                        }
                                    }
                                    else
                                    {
                                        addonLimitiReached($businessId,$employeeId,1);
                                    }
    				             }
    				             
    				             if($phoneNo!=''  )
    				             {
    				                 if($defaultSMSCnt > $defaulSMSUsedCnt)
                                    {
                                        sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);

                                        
                                        $defaulSMSUsedCnt = $defaulSMSUsedCnt + 1;
									    $objAddonMaster->UpdateDefaultUsedCount($businessId,2,$defaulSMSUsedCnt,$addondtlId_Sms);

                                        if(($defaultSMSCnt * 80)/100 == $defaulSMSUsedCnt) 
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,2);
                                        }
                                    }
                                    else if($purchasedSMSCnt >  $purchaseUsedSMSCnt)
                                    {
                                        sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url,$customerId);

                                        
                                        $purchaseUsedSMSCnt = $purchaseUsedSMSCnt + 1;
                                        $objAddonMaster->UpdatePurchaseUsedCount($businessId,2,$purchaseUsedSMSCnt,$addondtlId_Sms);

                                        if(($purchasedSMSCnt * 80)/100 == $purchaseUsedSMSCnt)
                                        {
                                            AddonNotify80Percent($businessId,$employeeId,2);
                                        }
                                    }
                                    else
                                    {
                                        addonLimitiReached($businessId,$employeeId,2);
                                    }
    				             }
    				         }
                                $objDB = new clsDB();
                                $objDB->UpdateSingleColumnValueToTable('calendarevents','reminderalertdate',$UpcomingRemiderDate,'id',$row['id']);
                                unset($objDB);
				        }    				         
				    }
		}
		}
	}
	function getMonthCount($recurrenceEndDate,$start)
	{
		$year1 = date('Y', $recurrenceEndDate);
		$year2 = date('Y', $start);
			
		$month1 = date('m', $recurrenceEndDate);
		$month2 = date('m', $start);

		return $diffMonths = (($year1 - $year2) * 12) + ($month1 - $month2);
	}

    function sendEmail($fullName,$eventId,$email,$businessName,$startDate)
    {
        // echo 'mail sent To -> '.$email;
        // echo "<br>";
        // echo "<br>";
        $objSendEmails = new clsSendEmails();
        $objSendEmails->SendMailOfEventToBusinessContactReminder($fullName,$eventId,$email,$businessName,$startDate);
        unset($objSendEmails);
    }
    function sendSmsReminder($phoneNo,$businessId,$businessName,$displayURLTag,$eventId,$title,$startDate,$description,$url='',$customerId)
    {
        //send SMS to the customer
        // echo "<br>";
        // echo 'sms to customer -> '.$phoneNo;
        // echo "<br>";
        // exit;
        $toNumber = '+1'.$phoneNo;
        $messageBody ='';
        $messageBody = ' REMINDER - '.$businessName.' ProBizCa Calendar Event: '.$title.".";
        
        if($startDate!='')
        {
        $messageBody .= " on Date: ".(date("m/d/Y h:i A",strtotime($startDate))); 
        }

        if(strlen($description) > 550) {
            // Set a specific error code for message too long
            // Generate guest Pass QrCode Link 
            $link = BASE_PATH . '/calendarEvent/' . $displayURLTag;
            $URL = $link . '/' . EncodeQueryData($businessId) . '/' . EncodeQueryData($eventId) . '/' . EncodeQueryData($customerId);
            $randomUrl = getTinyUrl($URL, 'calendarEvent');
            $eventlink = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);

            $messageBody .= " Please click the following link to view your message: ".$eventlink;
        } else {
            $messageBody .= ".\n Description: ".$description; 
        }
        
        if($url!='')
        {
            // $messageBody .= " URL: ".$url; 
            $messageBody .= " URL: ".$url."\n\n"." Reply STOP to unsubscribe"; 

        }
        
        sendSMS($phoneNo,$messageBody);
        // $txtCustomerPhone = '+1'.$phoneNo;  
        // $client = new Client(ACCOUNT_SID, AUTH_TOKEN_ID);  
        // $message = $client->messages->create(			
        //     $txtCustomerPhone,
        //         array(
        //             'from' => REGISTER_NUMBER,							
        //             'Body' => $messageBody
        //         )
        // );
        // echo "<br>";
        // echo "message sent";
        // echo "<br>";
    }

    function AddonNotify80Percent($businessId,$employeeId,$addonNameId)
    {
        $objBusiness = new clsBusiness();
        $businessDtls=$objBusiness->GetBusinessDetails($businessId);
        $businessEmail = $businessDtls['email'];
        $businessName = $businessDtls['businessName'];

        $addonName = ($addonNameId == 1) ? "Email" : "SMS";
      
        // 80% EMAIL usage Notification
        $objPushNotification= new clsPushNotification(); 
        $objPushNotification->businessId = $businessId;		
        $objPushNotification->title = 'Your '.$addonName.' usage is at 80%';		
        $objPushNotification->description = 'You are reaching your '.$addonName.' limit for the month. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';		
        $objPushNotification->isSystemNotification = 1;				
        $objPushNotification->systemUserId = 0;				
        $objPushNotification->notificationDate = date("Y-m-d H:i:s");
        $retNotificationId=$objPushNotification->SaveNotification(0);
        if($retNotificationId != '')
        {
            $objPushNotification= new clsPushNotification();
            $objPushNotification->businessId = $businessId;	
            $objPushNotification->customerId = 0;	
            $objPushNotification->affiliateId = 0;	
            $objPushNotification->notificationId = $retNotificationId;	
            $objPushNotification->employeeId = $employeeId;	
            $retnotificationDetailId=$objPushNotification->SaveNotificationDetails(0);
        }
        // super <NAME_EMAIL>
        $superAdminEmail = '<EMAIL>';					
        $businessAdminEmail = '<EMAIL>,'.$businessEmail;					
        $superAdmindescription = "The business ".$businessName." has reached it’s limit of ".$addonName." Addon upto 80%.";				
        $businessdescription = 'You are reaching your '.$addonName.' limit for the month. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';				
        $objSendEmails = new clsSendEmails();
        $objSendEmails->SendAddonSubscriptionDetailsToBusiness($businessName,$businessAdminEmail,$businessdescription);
        $objSendEmails->SendAddonSubscriptionDetailsToSuperAdmin($businessName,$superAdminEmail,$superAdmindescription);
        unset($objSendEmails);	
    }

    function addonLimitiReached($businessId,$employeeId,$addonNameId)
    {
        $objBusiness = new clsBusiness();
        $businessDtls=$objBusiness->GetBusinessDetails($businessId);
        $businessEmail = $businessDtls['email'];
        $businessName = $businessDtls['businessName'];

        $addonName = ($addonNameId == 1) ? "Email" : "SMS";

        // EMAIL Limit Reached Notification
        $objPushNotification= new clsPushNotification();
        $objPushNotification->businessId = $businessId;		
        $objPushNotification->title = 'Your '.$addonName.' usage has reached it’s limit';		
        $objPushNotification->description = 'Your '.$addonName.' limit for the month has been surpassed. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';		
        $objPushNotification->isSystemNotification = 1;				
        $objPushNotification->systemUserId = 0;				
        $objPushNotification->notificationDate = date("Y-m-d H:i:s");
        $retNotificationId=$objPushNotification->SaveNotification(0);
        if($retNotificationId != '')
            {
                $objPushNotification= new clsPushNotification();
                $objPushNotification->businessId = $businessId;	
                $objPushNotification->customerId = 0;	
                $objPushNotification->affiliateId = 0;	
                $objPushNotification->notificationId = $retNotificationId;	
                $objPushNotification->employeeId = $employeeId;	
                $retnotificationDetailId=$objPushNotification->SaveNotificationDetails(0);
            }
    // super <NAME_EMAIL>,<EMAIL>
    $superAdminEmail = '<EMAIL>';					
    $businessAdminEmail = '<EMAIL>,'.$businessEmail;									
    $superAdmindescription = "The business ".$businessName." has reached it’s limit of ".$addonName."";				
    $businessdescription = 'Your '.$addonName.' limit for the month has been surpassed. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';
    $objSendEmails = new clsSendEmails();
    $objSendEmails->SendAddonSubscriptionDetailsToBusiness($businessName,$businessAdminEmail,$businessdescription);
    $objSendEmails->SendAddonSubscriptionDetailsToSuperAdmin($businessName,$superAdminEmail,$superAdmindescription);
    unset($objSendEmails);
    }
    //send email to the customer					
				// 			$objSendEmails = new clsSendEmails();
				// // 			$objSendEmails->SendMailOfEventToBusinessContact('test Sourabh',123,'<EMAIL>',$errorMsg,'');
				// 			unset($objSendEmails);
?>	