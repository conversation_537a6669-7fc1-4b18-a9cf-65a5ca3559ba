<?php
//include classes and config file here
include('./includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsCalendarEvents.php');
include('../class/clsAddonMaster.php');

//variables
$systemUserRoleId  = 0;
$pageTitle = 'Add';
$buttonTitle = 'Add';
$contactList = '';
$color = '';
$title = '';
$url = '';
$description = '';
$id = 0;
$viewSelectedCustomer = array();
$viewEventReminderAlert = array();
$customerId = '';
$reminderType = 2;
$eventType = 0;
$reminderalertId = '';
$startDate  = date("m/d/Y h:i A");
$endDate  = date("m/d/Y h:i A");
$recurrenceEndDate  = date("m/d/Y h:i A");
$recurrenceType = 0;
$businessId = $_SESSION["loggedBusinessId"];
$objCalendarEvents = new clsCalendarEvents();
$calendarEventId = 0;
//get business contact list
$contactList = $objCalendarEvents->GetAllBusinessContactList($businessId);

$objAddonMaster = new clsAddonMaster();
$emailsmsCount = $objAddonMaster->GetPurchaseAddonDetailsInRow($businessId);
$emailremCnt = ($emailsmsCount['defaultCount'] - $emailsmsCount['dfUsed']) + ($emailsmsCount['purchasedCount'] - $emailsmsCount['purUsed']);
$smsremCnt = ($emailsmsCount['dfSMS'] - $emailsmsCount['dfsmsU']) + ($emailsmsCount['SmsPurchaseCnt'] - $emailsmsCount['SmsPurUsed']);






$reminderalert = $objCalendarEvents->GetCalenderEventALert();

if (isset($_GET['id'])) {
	$id = DecodeQueryData($_GET['id']);
	$calendarEventId = $id;
	$pageTitle = 'Edit';
	$buttonTitle = 'Update';

	//object
	$objCalendarEvents = new clsCalendarEvents();

	//get affiliate details
	$row = $objCalendarEvents->GetCalendarEventDetails($id);


	$reminderType  = stripslashes($row['reminderType']);
	$color  = stripslashes($row['color']);
	$title  = stripslashes($row['title']);
	$url  = stripslashes($row['url']);
	$description  = stripslashes($row['description']);
	// $customerId  = stripslashes($row['customerId']);
	// $viewSelectedCustomer = explode(',', $customerId);
	$startDate  = (date("m/d/Y h:i A", strtotime($row['start'])));
	$endDate  = (date("m/d/Y h:i A", strtotime($row['end'])));
	$eventType  = stripslashes($row['eventType']);
	$reminderalertId  = stripslashes($row['reminderalertId']);
	$recurrenceEndDate  = $row['recurrenceEndDate'];
	if ($recurrenceEndDate != '')
		$recurrenceEndDate  = (date("m/d/Y h:i A", strtotime($row['recurrenceEndDate'])));
	$recurrenceType  = stripslashes($row['recurrenceType']);
	//Get selected Customers
	$selectedCustomerList = $objCalendarEvents->GetEventContactList($id);
	if ($selectedCustomerList != "") {
		while ($cos = mysqli_fetch_assoc($selectedCustomerList)) {
			$customerId  = $cos['customerId'];
			array_push($viewSelectedCustomer, $customerId);
		}
	}
	//print_r($viewSelectedCustomer); exit;
	unset($objCalendarEvents);
}
?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
	<title><?php echo ($pageTitle); ?> Events</title>
	<?php include('includes/headerCss.php'); ?>
	<link href="<?php echo BASE_PATH; ?>/assets/vendors/js/summernote/dist/summernote-bs4.css" rel="stylesheet">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/selects/select2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/forms/extended/form-extended.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/fonts/simple-line-icons/style.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

	<!-- include header-->
	<?php include('includes/header.php'); ?>

	<!-- BEGIN: Content-->
	<div class="app-content content">
		<div class="content-overlay"></div>
		<div class="content-wrapper">
			<div class="content-header row">
				<div class="content-header-left col-md-6 col-12 mb-2">
					<h3 class="content-header-title">Events</h3>
					<div class="row breadcrumbs-top">
						<div class="breadcrumb-wrapper col-12">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="index.html">Dashboard</a>
								</li>
								<li class="breadcrumb-item"><a href="viewcalendarevents.html">Events</a>
								</li>
								<li class="breadcrumb-item active"><a href="#"><?php echo ($pageTitle); ?> Event</a>
								</li>
							</ol>
						</div>
					</div>
				</div>
				<div class="content-header-right col-md-6 col-12">
					<div class="text-right">
						<!---button type="button" class="btn btn-outline-primary btn-min-width mr-1 mb-1">Add</button--->
					</div>
				</div>
			</div>
			<div class="content-body">

				<!-- Basic form layout section start -->
				<section id="horizontal-form-layouts">
					<div class="row">
						<div class="col-md-12">
							<div class="card">
								<div class="card-header">
									<h4 class="card-title" id="horz-layout-colored-controls">Event</h4>
									<a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
									<div class="heading-elements">
										<ul class="list-inline mb-0">
											<li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
											<li><a data-action="expand"><i class="ft-maximize"></i></a></li>
										</ul>
									</div>
								</div>
								<div class="card-content collpase show">
									<div class="card-body">
										<form class="form form-horizontal" id="serviceForm" method="post" action="addcalendareventsubmit.html" enctype="multipart/form-data">
											<input type="hidden" name="id" id="id" value="<?php echo ($id); ?>">
											<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
											<input type="hidden" name="eventType" id="eventType" value="<?php echo ($eventType); ?>">

											<input type="hidden" name="emailremCnt" id="emailremCnt" value="<?php echo ($emailremCnt); ?>">
											<input type="hidden" name="smsremCnt" id="smsremCnt" value="<?php echo ($smsremCnt); ?>">

											<div class="form-body">
												<h4 class="form-section"><i class="la la-money"></i>Event</h4>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="title">Title<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<input type="text" id="title" class="form-control" placeholder="Title" name="title" value="<?php echo ($title); ?>" required <?php if ($eventType == 1) {
																																																echo "readonly";
																																															} ?>>
															</div>
														</div>
													</div>

													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="color">Color<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<input type="color" class="form-control" id="color" name="color" value="<?php echo ($color); ?>" required>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="url">URL</label>
															<div class="col-md-9 mx-auto">
																<input type="url" class="form-control url-input" id="url" name="url" value="<?php echo ($url); ?>">
															</div>
														</div>
													</div>
													<?php if ($eventType != 1) { ?>
														<div class="col-md-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="customerId">Contact List<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto" style="display: flex;flex-direction: column-reverse;">
																	<select id="customerId" name="customerId[]" class="form-control" multiple multiselect-search="true" multiselect-select-all="true" required>
																		<?php

																		if ($contactList != "") {
																			while ($row = mysqli_fetch_assoc($contactList)) {
																				$customerId  = $row['customerId'];
																				$firstName  = stripslashes($row['firstName']);
																				$lastName  = stripslashes($row['lastName']);

																		?>
																				<option class="form-control" value="<?php echo ($customerId); ?>" <?php if (in_array($customerId, $viewSelectedCustomer)) { ?> selected="true" <?php } ?>><?php echo ($firstName . ' ' . $lastName); ?></option>
																		<?php

																			}
																		}
																		?>
																	</select>
																</div>
															</div>
														</div>
													<?php } elseif ($eventType == 1 && count($viewSelectedCustomer) > 0) { ?>
														<div class="col-md-6">
															<div class="form-group row">
																<label class="col-md-3 label-control" for="customerId">Contact List<span class="validate-field">*</span></label>
																<div class="col-md-9 mx-auto">
																	<?php
																	$obj = new clsDB();
																	$cFirstName = $obj->GetSingleColumnValueFromTable('customer', 'firstName', 'customerId', $viewSelectedCustomer[0]);
																	$cLastName = $obj->GetSingleColumnValueFromTable('customer', 'lastName', 'customerId', $viewSelectedCustomer[0]);
																	unset($obj);
																	?>
																	<input type="hidden" class="form-control" id="customerId" name="customerId[]" value="<?php echo ($viewSelectedCustomer[0]); ?>" readonly>
																	<input type="text" class="form-control" value="<?php echo $cFirstName . " " . $cLastName; ?>" readonly>
																</div>
															</div>
														</div>
													<?php } ?>
												</div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="startDate"><?php if ($eventType == 1) {
																														echo $title;
																													} else {
																														echo "Start";
																													} ?> Date<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<div class="input-group date-picker-class">
																	<input type="text" class="form-control" id="startDate" name="startDate" value="<?php echo ($startDate); ?>">
																	<div class="input-group-append">
																		<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																	</div>
																</div>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="endDate">End Date<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<div class="input-group date-picker-class">
																	<input type="text" class="form-control" id="endDate" name="endDate" value="<?php echo ($endDate); ?>">
																	<div class="input-group-append">
																		<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																	</div>
																</div>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="description">Description</label>
															<div class="col-md-9 mx-auto">
																<textarea class="form-control" id="description" name="description" rows="6"  placeholder="Message"><?php echo ($description); ?></textarea>
																<div id="error-description"></div>
                                                                <div id="the-count" style="float: right; <?php if ($reminderType != 2 && $reminderType != 3) echo 'display: none;'; ?>">
                                                                    <span id="current"><?php echo strlen($description); ?></span>
                                                                    <span id="maximum">/ 550</span>
                                                                </div>
															</div>
														</div>
													</div>

													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="type">Reminder Type<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<fieldset>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input bg-primary" name="reminderType" id="colorRadio1" value="1" <?php if ($reminderType == 1) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="colorRadio1">Email</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input bg-success" name="reminderType" id="colorRadio2" value="2" <?php if ($reminderType == 2) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="colorRadio2">SMS</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input bg-warning" name="reminderType" id="colorRadio3" value="3" <?php if ($reminderType == 3) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="colorRadio3">Both</label>
																	</div>
																</fieldset>
															</div>
														</div>
														<div class="form-group row">
															<label class="col-md-3 label-control" for="type">Recurrence<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<fieldset>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio1" value="0" onchange="GetRecurrenceEndDate(0);" <?php if ($recurrenceType == 0) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio1">None</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio2" value="1" onchange="GetRecurrenceEndDate(1);" <?php if ($recurrenceType == 1) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio2">Daily</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio3" value="2" onchange="GetRecurrenceEndDate(2);" <?php if ($recurrenceType == 2) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio3">Weekly</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio4" value="3" onchange="GetRecurrenceEndDate(3);" <?php if ($recurrenceType == 3) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio4">Bi-Weekly</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio5" value="4" onchange="GetRecurrenceEndDate(4);" <?php if ($recurrenceType == 4) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio5">Monthly</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio6" value="5" onchange="GetRecurrenceEndDate(5);" <?php if ($recurrenceType == 5) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio6">Quarterly</label>
																	</div>
																	<div class="d-inline-block custom-control custom-radio mr-1">
																		<input type="radio" class="custom-control-input recurrenceType" name="recurrenceType" id="recurrenceRadio7" value="6" onchange="GetRecurrenceEndDate(6);" <?php if ($recurrenceType == 6) { ?> checked <?php } ?>>
																		<label class="custom-control-label" for="recurrenceRadio7">Anually</label>
																	</div>
																</fieldset>
															</div>
														</div>
														<div class="form-group row">
															<label class="col-md-3 label-control" for="recurrenceEndDate">Recurrence End Date<span class="validate-field">*</span></label>
															<div class="col-md-9 mx-auto">
																<div class="input-group date-picker-class">
																	<input type="text" class="form-control" id="recurrenceEndDate" name="recurrenceEndDate" value="<?php echo ($recurrenceEndDate); ?>">
																	<div class="input-group-append">
																		<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																	</div>
																</div>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">

														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="col-md-3 label-control" for="alert">Reminder Alert</label>
															<div class="col-md-9 mx-auto">
																<select name="reminderalertId" id="reminderalertId" class="form-control" readonly="false" style="background: #fff;">
																	<?php
																	while ($row = mysqli_fetch_assoc($reminderalert)) {
																		$id  = $row['id'];
																		$name  = $row['name'];
																		$reminderMinutes  = $row['reminderMinutes'];
																		// if ($id == 8)
																		// 	break;

																	?>
																		<option value="<?php echo ($reminderMinutes); ?>" <?php if ($reminderMinutes == $reminderalertId) {
																																echo ("selected");
																															} ?>><?php echo $name; ?></option>
																	<?php
																	}
																	?>
																</select><br>
																<span class="validate-field">NOTE: Email and SMS sent from Calendar are considered under total count. </span>
															</div>

														</div>

													</div>
												</div>
												<div class="form-actions text-right">
													<a href="viewcalendarevents.html">
														<button type="button" class="btn btn-outline-warning btn-glow mr-1">
															<i class="ft-x"></i> Cancel
														</button>
													</a>
													<button type="submit" id="btnSave" class="btn btn-outline-primary btn-glow">
														<i class="la la-check-square-o"></i> <?php echo ($buttonTitle); ?>
													</button>
												</div>
											</div>
										</form>

									</div>
								</div>
							</div>
						</div>
					</div>
				</section>
			</div>
		</div>
	</div>
	<!-- END: Content-->

	<!-- BEGIN: Footer-->
	<?php include('includes/footer.php'); ?>
	<?php include('includes/footerJs.php'); ?>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/custom.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/summernote/dist/summernote-bs4.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/editorDemo.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/tags/form-field.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/custom-file-input.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/moment/moment.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/multiselect-dropdown.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/common.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/parsleyjs@2.9.2/dist/parsley.min.js"></script>

	<script>
		//form validation
		$('#serviceForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				$("#btnSave").prop("disabled", true);
				return true; // Don't submit form for this demo
			});

		$(function() {
			$('#startDate').datetimepicker({

				icons: {
					time: 'fa fa-clock-o',
					date: 'fa fa-calendar',
					up: 'fa fa-chevron-up',
					down: 'fa fa-chevron-down',
					previous: 'fa fa-chevron-left',
					next: 'fa fa-chevron-right',
					today: 'fa fa-check',
					clear: 'fa fa-trash',
					close: 'fa fa-times'
				},

				allowInputToggle: true
			});

			$("#startDate").on("dp.change", function() {
				$('#endDate').val($(this).val())
			});

			$('#endDate,#recurrenceEndDate').datetimepicker({

				icons: {
					time: 'fa fa-clock-o',
					date: 'fa fa-calendar',
					up: 'fa fa-chevron-up',
					down: 'fa fa-chevron-down',
					previous: 'fa fa-chevron-left',
					next: 'fa fa-chevron-right',
					today: 'fa fa-check',
					clear: 'fa fa-trash',
					close: 'fa fa-times'
				},

				allowInputToggle: true
			});

		});

		$("#endDate").on("dp.change", function() {
			var startDate = $('#startDate').val()
			var endDate = $('#endDate').val()
			var start = moment(startDate);
			var daysInMnth = start.daysInMonth();
			var end = moment(endDate);

			var diff = end.diff(start, "days");
			var diffMnth = end.diff(start, "month");
			var diffYr = end.diff(start, "year");
			// if ($("#recurrenceRadio1").prop("checked")) {
			// 	// do something recurrenceEndDate
			// 	$('#recurrenceEndDate').val(endDate);
			// var yeardate = moment(endDate).add(1, 'years').calendar();
			var yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(1, 'years').format('MM/DD/YYYY hh:mm A')
			// console.log(yeardate);
			var id = '<?php echo $calendarEventId; ?>';
			// console.log('id'+id);
			if (id == 0) {
				$('#recurrenceEndDate').val(yeardate);
				$('#recurrenceEndDate').prop('readonly', true);
			}
			// }
			// else
			// 	$('#recurrenceEndDate').prop('readonly',false);

			if (diff > 0)
				$('#recurrenceRadio2').prop('disabled', true);
			else
				$('#recurrenceRadio2').prop('disabled', false);

			if (diff >= 7)
				$('#recurrenceRadio3').prop('disabled', true);
			else
				$('#recurrenceRadio3').prop('disabled', false);
			if (diff >= 14)
				$('#recurrenceRadio4').prop('disabled', true);
			else
				$('#recurrenceRadio4').prop('disabled', false);
			if (diff >= daysInMnth)
				$('#recurrenceRadio5').prop('disabled', true);
			else
				$('#recurrenceRadio5').prop('disabled', false);
			if (diffMnth >= 3)
				$('#recurrenceRadio6').prop('disabled', true);
			else
				$('#recurrenceRadio6').prop('disabled', false);
			if (diffYr > 0)
				$('#recurrenceRadio7').prop('disabled', true);
			else
				$('#recurrenceRadio7').prop('disabled', false);
		});

		// 	Get recurrence end date by recurrence type
		function GetRecurrenceEndDate(rtype) {
			var endDate = $('#endDate').val();

			if (rtype == 2) {
				// Weekly
				var yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(1, 'weeks').format('MM/DD/YYYY hh:mm A')
				// console.log(yeardate);
				$('#recurrenceEndDate').val(yeardate);

			} else if (rtype == 3) {
				// Bi-Weekly
				var yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(2, 'weeks').format('MM/DD/YYYY hh:mm A')
				// console.log(yeardate);
				$('#recurrenceEndDate').val(yeardate);


			} else if (rtype == 4) {
				// monthly
				var yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(1, 'months').format('MM/DD/YYYY hh:mm A')
				// console.log(yeardate);
				$('#recurrenceEndDate').val(yeardate);


			} else if (rtype == 5) {
				// Quarterly
				var yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(3, 'months').format('MM/DD/YYYY hh:mm A')
				// console.log(yeardate);
				$('#recurrenceEndDate').val(yeardate);


			} else if (rtype == 6) {
				// Anually
				var yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(1, 'years').format('MM/DD/YYYY hh:mm A')
				// console.log(yeardate);
				$('#recurrenceEndDate').val(yeardate);


			}

		}



		//input only number validation
		$('.inputValid').keypress(function(event) {
			var keycode = event.which;
			if (!(event.shiftKey == false && (keycode == 46 || keycode == 8 || keycode == 37 || keycode == 39 || (keycode >= 48 && keycode <= 57)))) {
				event.preventDefault();
			}
		});
		$(document).ready(function() {
			var id = '<?php echo $calendarEventId; ?>';
			// console.log('id'+id);
			if (id == 0)
				$(".recurrenceType").trigger('change');
		});
		$(".recurrenceType").change(function() {
			var endDate = $('#endDate').val()
			yeardate = moment(endDate, "MM/DD/YYYY hh:mm A").add(1, 'hour').format('MM/DD/YYYY hh:mm A');
			if ($("#recurrenceRadio1").prop("checked")) {
				// do something recurrenceEndDate
				$('#recurrenceEndDate').val(yeardate);
				$('#recurrenceEndDate').prop('readonly', true);
			} else
				$('#recurrenceEndDate').prop('readonly', false);
		});
		var serviceCategoryIds = $("#serviceCategoryId").val();
		if (serviceCategoryIds == '') {
			$("#couponDiv").hide();
			$("#rewardDiv").hide();
			$("#loyalityDiv").hide();
		}

		var serveCatId = $("#serveCatId").val();

		if (serveCatId == 1) {
			$("#couponDiv").show();
			$("#rewardDiv").hide();
			$("#loyalityDiv").hide();
		} else if (serveCatId == 2) {
			$("#couponDiv").hide();
			$("#rewardDiv").show();
			$("#loyalityDiv").hide();
		} else if (serveCatId == 3) {
			$("#couponDiv").hide();
			$("#rewardDiv").hide();
			$("#loyalityDiv").show();
		}

		//onchange service category change fields couponDiv rewardDiv loyalityDiv
		$(document).on('change', '#serviceCategoryId', function() {
			var serviceCategoryId = $("#serviceCategoryId").val();

			if (serviceCategoryId == 1) {
				$("#couponDiv").show();
				$("#rewardDiv").hide();
				$("#loyalityDiv").hide();
			}
			if (serviceCategoryId == 2) {
				$("#couponDiv").hide();
				$("#rewardDiv").show();
				$("#loyalityDiv").hide();
			}
			if (serviceCategoryId == 3) {
				$("#couponDiv").hide();
				$("#rewardDiv").hide();
				$("#loyalityDiv").show();
			}

		})

		$('#btnSave').click(function() {

			var emailremainCnt = $('#emailremCnt').val();
			var smsremainCnt = $('#smsremCnt').val();
			var customerCnt = $('#customerId option:selected').length;
			var reminderType = $('input[name="reminderType"]:checked').val();


			if (reminderType == 1 && customerCnt != '') {

				if (emailremainCnt < customerCnt) {
					Swal.fire({
						title: 'Confirmation',
						text: "Insufficient Email Balance!",
						type: 'warning',
						showCancelButton: true,
						confirmButtonColor: '#3085d6',
						cancelButtonColor: '#d33',
						confirmButtonText: 'Update',
						confirmButtonClass: 'btn btn-outline-primary',
						cancelButtonClass: 'btn btn-outline-danger ml-1',
						buttonsStyling: false,
					}).then(function(result) {
						if (result.value) {
							$('#serviceForm').submit();

						}
					});
				} else if (emailremainCnt == customerCnt) {
					$('#serviceForm').submit();
				} else {
					$('#serviceForm').submit();
				}
			} else if (reminderType == 2 && customerCnt != '') {

				if (smsremainCnt < customerCnt) {
					Swal.fire({
						title: 'Confirmation',
						text: "Insufficient SMS Balance!",
						type: 'warning',
						showCancelButton: true,
						confirmButtonColor: '#3085d6',
						cancelButtonColor: '#d33',
						confirmButtonText: 'Update',
						confirmButtonClass: 'btn btn-outline-primary',
						cancelButtonClass: 'btn btn-outline-danger ml-1',
						buttonsStyling: false,
					}).then(function(result) {
						if (result.value) {
							$('#serviceForm').submit();

						}
					});

				} else if (smsremainCnt == customerCnt) {

					$('#serviceForm').submit();
				} else {
					$('#serviceForm').submit();

				}

			} else if (reminderType == 3 && customerCnt != '') {
				if (emailremainCnt < customerCnt || smsremainCnt < customerCnt) {

					Swal.fire({
						title: 'Confirmation',
						text: "Insufficient SMS/Email Balance!",
						type: 'warning',
						showCancelButton: true,
						confirmButtonColor: '#3085d6',
						cancelButtonColor: '#d33',
						confirmButtonText: 'Update',
						confirmButtonClass: 'btn btn-outline-primary',
						cancelButtonClass: 'btn btn-outline-danger ml-1',
						buttonsStyling: false,
					}).then(function(result) {
						if (result.value) {
							$('#serviceForm').submit();

						}
					});

				} else {
					$('#serviceForm').submit();

				}
			}


		});

		//url
		$("#url").on('blur', function() {
			var url = $('#url').val();


			if (!url.match(/^(https?:\/\/)/i)) {

				url = 'http://' + url;
				$('#url').val(url);
			}

			if (url != '') {

				var regExp = /^(https?:\/\/)?([a-z0-9-]+\.)+[a-z]{2,6}(\/\S*)?$/i;
				var match = url.match(regExp);

				if (!match) {

					Swal.fire({
						type: 'warning',
						title: 'Invalid URL.',
						text: 'Please enter a valid URL.',
						confirmButtonClass: 'btn btn-outline-warning btn-glow'
					});

					$('#url').val('');

				}
			}
		});

		// Add a variable to track if warning has been shown
		var smsWarningShown = false;
		var lastLength = 0;

		// Handle character count for SMS messages
		$('textarea').keyup(function() {
			var characterCount = $(this).val().length;
			var reminderType = $('input[name="reminderType"]:checked').val();
			var current = $('#current');
			var textareaValue = $(this).val();

			// Update the character count display
			current.text(characterCount);

			// Check if the textarea was cleared (reset warning flag)
			if (lastLength > 0 && characterCount === 0) {
				smsWarningShown = false;
			}

			// Save current length for next comparison
			lastLength = characterCount;

			// Check if SMS or Both is selected
			if (reminderType == 2 || reminderType == 3) {
				// SMS character limit is 550
				if (characterCount > 550) {
					// Highlight the character count in red
					$('#the-count').css('color', 'red');

					// Only show warning if it hasn't been shown yet
					if (!smsWarningShown) {
						Swal.fire({
							type: 'warning',
							title: 'SMS Character Limit Reached',
							text: 'You have reached the SMS character limit of 550 characters. Your contact will receive a hyperlink to view your message.',
							confirmButtonClass: 'btn btn-outline-warning btn-glow'
						});

						// Set flag to indicate warning has been shown
						smsWarningShown = true;
					}
				} else {
					// Reset the color if under the limit
					$('#the-count').css('color', '');
				}
			} else {
				// For email only, use a higher limit
				// if (characterCount > 2000) {
				// 	Swal.fire({
				// 		type: 'warning',
				// 		title: 'Character Limit Reached',
				// 		text: 'Your message is very long. Consider shortening it for better readability.',
				// 		confirmButtonClass: 'btn btn-outline-warning btn-glow'
				// 	});
				// }
			}
		});

		// Show/hide character counter based on reminder type selection
		$('input[name="reminderType"]').change(function() {
			var reminderType = $(this).val();

			// Reset warning flag when changing reminder types
			smsWarningShown = false;

			// Show character counter only for SMS and Both
			if (reminderType == 2 || reminderType == 3) {
				$('#the-count').show();
				// Trigger keyup to update the count immediately
				$('#description').keyup();
			} else {
				$('#the-count').hide();
			}
		});
	</script>

</body>
<!-- END: Body-->

</html>