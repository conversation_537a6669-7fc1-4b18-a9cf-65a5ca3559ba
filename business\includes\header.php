<?php

//include file and classes
include('./includes/validatebusinesslogin.php');
require_once('../includes/config.php');
include('../class/clsSystemUser.php');
include('../class/clsPushNotification.php');
require_once('../class/clsDB.php');
require_once('../class/clsBusiness.php');


// print_r($_SESSION);exit;
$systemUserId = 0;
if (isset($_SESSION['loggedNewSystemUserId'])) {
	$systemUserId = $_SESSION['loggedNewSystemUserId'];
} else {
	$systemUserId = 0;
}

// Session Variables
$loggedSystemUserFullName = isset($_SESSION['loggedSystemUserFullName']) ? $_SESSION['loggedSystemUserFullName'] : '';
$loggedUserName = isset($_SESSION['loggedUserName']) ? $_SESSION['loggedUserName'] : '';
$loggedBusinessName = $_SESSION['loggedBusinessName'];
$loggedBusinessOwnerName = $_SESSION['loggedBusinessOwnerName'];
$loggedBusinessEmail = $_SESSION['loggedEmail'];
$loggedBusinessLogo = $_SESSION['loggedBusinessLogo'];
$loggedEmployeeId = $_SESSION["loggedEmployeeId"];
$loggedBusinessId = $_SESSION["loggedBusinessId"];
$isPrimary = $_SESSION["isPrimary"];

$chatUserRole = ($isPrimary == 1) ? 2 : 3;

if ($loggedBusinessLogo == BASE_PATH . '/upload/default_store.png') {
	$loggedBusinessLogo = '';
}
//get business name 
$objBusiness = new clsBusiness();
//get business details
$rows = $objBusiness->GetBusinessDetails($_SESSION["loggedBusinessId"]);
$businessCode = stripslashes($rows['businessCode']);
$planId = stripslashes($rows['planId']);
$businessName = stripslashes($rows['businessName']);
$businessemail = stripslashes($rows['email']);
$businessphoneNo = stripslashes($rows['phoneNo']);
$businessaddress = stripslashes($rows['address']);


//variables
$notifications = '';
$notificationCount = 0;
$notificationCountValue = 0;
$viewProBizCaPath = '';

$objNotification = new clsPushNotification();
// if($_SESSION["isPrimary"]==1)
// {
// $notifications = $objNotification->GetNewNotification($_SESSION["loggedBusinessId"], 0 , 0);
// $notificationCount = mysqli_num_rows($notifications);
// }
// else
// {
$notifications = $objNotification->GetNewNotification($_SESSION["loggedBusinessId"], $_SESSION["loggedEmployeeId"], 0, 0);
$notificationCount = mysqli_num_rows($notifications);
$notificationCountValue = mysqli_num_rows($notifications);
// }


//***** GET BUSINESS PLAN DETAILS *****//
$businessPlan = $objNotification->GetBusinessPlanDetails($_SESSION["loggedBusinessId"], 0);
if ($businessPlan != '') {
	$planId = stripslashes($businessPlan['planId']);

	$isCrmIntergration = stripslashes($businessPlan['isCrmIntergration']);
	$isCouponServices = stripslashes($businessPlan['isCouponServices']);
	$pushNotification = stripslashes($businessPlan['pushNotification']);
	$chatRoom = stripslashes($businessPlan['chatRoom']);
	$shoppingCart = stripslashes($businessPlan['shoppingCart']);

	$surveychk = stripslashes($businessPlan['surveychk']);
	$customQrFormchk = stripslashes($businessPlan['customQrFormchk']);

	$geolocation = stripslashes($businessPlan['geolocation']);
	$socialfeed = stripslashes($businessPlan['socialfeed']);
	$statistics = stripslashes($businessPlan['statistics']);
	$support = stripslashes($businessPlan['support']);
	$todo = stripslashes($businessPlan['todo']);
	$probizcaplus = stripslashes($businessPlan['probizcaplus']);
	$invoice = stripslashes($businessPlan['invoice']);
	$subCardLimits = stripslashes($businessPlan['subCardLimit']);

	// $subscriptionStartDate = stripslashes($businessPlan['subscriptionStartDate']);

	if($businessPlan['subscriptionStartDate'] == '0000-00-00 00:00:00' || $businessPlan['subscriptionStartDate'] == '12/31/1969'){
		// echo "in if";exit;
		$subscriptionStartDate = '-';
	}else{
		// echo "in else";exit;
		$subscriptionStartDate = date("m/d/Y", strtotime($businessPlan['subscriptionStartDate']));
	}
	// $subscriptionEndDate = date('Y-m-d', strtotime($businessPlan['subscriptionEndDate']))

	if($businessPlan['subscriptionEndDate'] == '0000-00-00 00:00:00' || $businessPlan['subscriptionEndDate'] == '12/31/1969'){
		// echo "in if";exit;
		$subscriptionEndDate = '-';
	}else{
		// echo "in else";exit;
		$subscriptionEndDate = date("m/d/Y", strtotime($businessPlan['subscriptionEndDate']));
	}

	$currentDate = new DateTime(date('Y-m-d'));

	// Set the end date and time
	$subendDate = new DateTime(date('Y-m-d', strtotime($businessPlan['subscriptionEndDate'])));

	// Calculate the interval between the two dates
	$interval = $currentDate->diff($subendDate);

	// Get the total number of days from the interval
	$numberOfDays = $interval->format('%a');

	if ($currentDate == $subendDate) {
		$msg = "Your Free Trail subscription ends today";
	} else if ($currentDate > $subendDate) {
		$msg = "Subscription expired";
	} else {
		$msg = " Days left to Free Trial";
	}
}
unset($objNotification);
$obj = new clsDB();
$displayURLTag = $obj->GetSingleColumnValueFromTable('business', 'displayURLTag', 'businessId', $_SESSION["loggedBusinessId"]);
//path
if ($displayURLTag != '') {
	$viewProBizCaPath = BASE_PATH . '/mb/' . $displayURLTag . '/' . EncodeQueryData('E') . '/' . EncodeQueryData($loggedEmployeeId);
}
unset($obj);
?>

<style>
	.custom-dropdown-menu {
		display: none;
		/* Initially hide the dropdown menu */
	}

	.showDropdown {
		transform: scale(1, 1) !important;
		opacity: 1;
		display: block !important;
	}

	/* Default style for submenus (hidden by default) */
	.horizontal-menu .navbar-horizontal .dropdown .dropdown-menu {
		overflow: visible !important;
		max-height: fit-content !important;
	}

	.vertical-overlay-menu .main-menu.menu-fixed .main-menu-content {
		height: fit-content !important;
		overflow-y: auto !important;
	}

	.vertical-overlay-menu.menu-open .main-menu {
		overflow-y: auto !important;
	}

	.free-trial {
		position: fixed;
		bottom: 2%;
		right: 2%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		padding: 0 10px;

	}

	.left-count {
		border-radius: 50%;
		width: 70px;
		background: #fff;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		color: black;
		border: 2px solid #ff4961;
		height: 70px;
		margin-right: 0;
		color: #ff4961;
		font-weight: 600;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.expiry-today {
		width: 75px !important;
		height: 75px !important;
	}

	.hidden-text {
		display: none;
		margin-left: 3px;
	}

	.full-text {
		width: fit-content;
		height: 75px;
		max-height: 75px;
		border-radius: 25px;
		flex-direction: row;
		cursor: pointer;
		align-items: center;
	}

	.expire-msg {
		border-radius: 30px;
		width: fit-content;
		background: #fff;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		color: black;
		border: 2px solid #ff4961;
		height: 50px;
		margin-right: 0;
		color: #ff4961;
		font-weight: 600;
		display: flex;
		align-items: center;
		padding: 0 15px;
	}

	.free-trial:hover .left-count {
		margin-right: 5px;
		display: none;

	}

	.free-trial:hover .hidden-text {
		display: flex;
	}



	/* Style the tab buttons */
	.tab {
		overflow: hidden;
		border-bottom: 1px solid #ddd;
	}

	.tab button {
		min-height: 40px;
		background-color: #FFFFFF;
		float: left;
		border: none;
		outline: none;
		cursor: pointer;
		padding: 10px 20px;
		transition: background-color 0.3s;
	}

	.tab button:hover {
		background-color: #ddd;
	}

	/* Style the tab content */
	.tabcontent {
		display: none;
		padding: 20px;
		/* padding: 0px; */
	}

	.header-navbar .navbar-container .dropdown-menu-media .dropdown-menu-footer{
		background-color: #FFFFFF;
	}

	.tabcontent.active {
		display: block;
		/* background-color: #f1f1f1; */
	}

	.tablinks.active {
		/* display: block; */
		background-color: #f1f1f1;
	}

	.header-navbar .navbar-container .dropdown-menu-media {
		width: 30rem;
	}

	.dropdown-notification .notification-tag {
		top: -1px;
		right: 0px;
		font-size: 10px;
	}

	.header-navbar .navbar-container ul.nav li .media-list {
		top: 0;
	}

	.notification-list {
		list-style: none;
		padding-left: 0;
	}

	.notification {
		margin: 10px 0;
	}

	.notification-details {
		padding-left: 20px;
	}

	.notification-avatar {
		width: 50px;
		height: 50px;
	}

	.notification-avatar img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 50%;
	}

	.notification-details h4 {
		font-size: 16px;
		margin-bottom: 5px;
	}

	.notification-heading {
		font-size: 14px;
	}

	.notification-text {
		font-size: 12px;
	}

	.navbar-nav {
		flex-wrap: nowrap;
	}

	.navbar-nav {
		min-height: 5.15rem !important;
		height: fit-content;
	}

	@media (max-width: 767.98px) {
	   /* .navbar-wrapper{
			display: none;
	   } */
		   .mobile-res{
		display: none;
	}

		
	   
		.header-navbar .navbar-header .navbar-brand {
			position: static !important;
			transform: none !important;
			white-space: unset !important;
			text-align: center;
		}

		.header-navbar .navbar-header,
		.navbar-nav {
			height: fit-content;
		}
		/* .header-navbar .navbar-header,
		.navbar-nav, */
		.navbar-wrapper {
			min-height: 6rem !important;
			height: fit-content;
		}
    
		.font-large-1 {
			font-size: 1.5rem !important;
		}

		.header-navbar .navbar-header .navbar-brand .brand-text {
			font-size: 15px;
			padding-left: 0 !important;

		}

		.vertical-overlay-menu .header-navbar .navbar-header .mobile-menu a.menu-toggle {
			padding-top: 1rem;
			margin-right: 10px;
		}

		.header-navbar .navbar-container .show .dropdown-menu {
			right: 0rem;
			left: 3rem !important;
			float: none;
			width: 300px;
			margin-top: 0;
			max-height: 400px;
			overflow-x: hidden;
			overflow-y: hidden;
			/* transform: translate(-190px) !important; */
		}

		.abc {
			transform: translate(-250px) !important;
		}
		.sidepanel-mob{
			margin-top: 25px !important;
		}
		
	}
@media screen and (max-width: 768px) {
  /* Your styles here */
  .header-navbar .navbar-container ul.nav li a.nav-link-label{
	padding: 0 !important;
	margin-top: 10px;
  }
.tab-wrap{
		    padding: 0 !important;
	}
	dropdown-toggle nav-link dropdown-user-link{
				    padding: 0 !important;

	}
	.header-navbar .navbar-container ul.nav li a.dropdown-user-link{
				    padding: 0 !important;

	}
}
	
</style>
<!-- BEGIN: Header-->

<nav
	class="header-navbar navbar-expand-md navbar navbar-with-menu navbar-without-dd-arrow navbar-static-top navbar-light navbar-brand-center">
	<input type="hidden" name="systemUserId" id="systemUserId" value="<?php echo ($systemUserId); ?>">
	<input type="hidden" name="loggedBusinessId" id="loggedBusinessId"
		value="<?php echo EncodeQueryData($loggedBusinessId); ?>">
	<input type="hidden" name="loggedEmployeeId" id="loggedEmployeeId"
		value="<?php echo EncodeQueryData($loggedEmployeeId); ?>">
	<input type="hidden" name="loggedBusinessEmail" id="loggedBusinessEmail"
		value="<?php echo ($loggedBusinessEmail); ?>">
	<input type="hidden" name="isPrimary" id="isPrimary" value="<?php echo ($isPrimary); ?>">
	<input type="hidden" name="chatUserRole" id="chatUserRole" value="<?php echo ($chatUserRole); ?>">
	<input type="hidden" name="notificationCount" id="notificationCount" value="<?php echo ($notificationCount); ?>">

	<div class="navbar-wrapper">
		<div class="navbar-header ">
			<ul class="nav navbar-nav flex-row">
				<li class="nav-item mobile-menu d-md-none mr-auto"><a
						class="nav-link nav-menu-main menu-toggle hidden-xs" href="javascript:void(0);"><i
							class="ft-menu font-large-1"></i></a></li>
				<li class="nav-item" style="width: 90%;text-align: center;"><a class="navbar-brand"
						href="dashboard.html">
						<?php if ($loggedBusinessLogo != '') { ?>
							<img class="brand-logo" alt="modern admin logo" src="<?php echo ($loggedBusinessLogo); ?>">
						<?php } ?>
						<h3 class="brand-text"><?php echo ($_SESSION["loggedBusinessName"]); ?></h3>
					</a>
				</li>
				<li class="nav-item">
					<h3 class="brand-text"> </h3>
				</li>

				<li class="nav-item d-md-none"><a class="nav-link open-navbar-container" data-toggle="collapse"
						data-target="#navbar-mobile"><i class="la la-ellipsis-v"></i></a></li>
			</ul>
		</div>
		<input type="hidden" name="notificationCountValue" id="notificationCountValue"
			value="<?php echo ($notificationCountValue); ?>">
		<div class="navbar-container content">
			<div class="collapse navbar-collapse" id="navbar-mobile">
				<ul class="nav navbar-nav mr-auto float-left">
					<li class="nav-item d-none d-md-block"><a class="nav-link nav-menu-main menu-toggle hidden-xs"
							href="javascript:void(0);"><i class="ft-menu"></i></a></li>
					<li class="nav-item d-none d-lg-block"><a class="nav-link nav-link-expand"
							href="javascript:void(0);"><i class="ficon ft-maximize"></i></a></li>
				</ul>


				<ul class="nav navbar-nav float-right">

					<!--- notification for business start here ----->
					<li class="dropdown dropdown-notification nav-item"><a class="nav-link nav-link-label"
							href="javascript:void(0);" data-toggle="dropdown">
							<i class="ficon ft-bell"></i>
							<span
								id="countValue"
								class="badge badge-pill <?php if ($notificationCount > 0) { ?> badge-danger <?php } else { ?> badge-success <?php } ?> badge-up badge-glow">
								<?php echo ($notificationCount); ?>
							</span></a>
						<ul class="custom-dropdown-menu dropdown-menu dropdown-menu-media dropdown-menu-right abc p-0">
							<li class="dropdown-menu-header">


								<div class="custom-tabs">

									<!-- <h6 class="dropdown-header m-0"><span class="grey darken-2">Notifications</span></h6><span class="notification-tag badge <?php if ($notificationCount > 0) { ?> badge-danger <?php } else { ?> badge-success <?php } ?> float-right m-0" id="countValue1"><?php //echo ($notificationCount); 
												 ?> New</span> -->


									<div class="tab" style="width: 100%;">
										<button
											style="<?php if ($chatRoom == 1) { ?>width: 50%; <?php } else { ?>width: 100%;<?php  } ?>display: flex;justify-content: center;align-items: center;border-radius: 0;"
											class="tablinks active" onclick="openTab(event, 'tab1')">
											<h6 class="dropdown-header m-0 p-0"><span
													class="grey darken-2">Notifications</span></h6><span
												class="notification-tag badge <?php if ($notificationCount > 0) { ?> badge-danger <?php } else { ?> badge-success <?php } ?> float-right ml-10"
												id="countValue1"><?php echo ($notificationCount); ?> New</span>
										</button>
										<?php if ($chatRoom == 1) { ?> 
										<button
											style="width: 50%;;display: flex;justify-content: center;align-items: center;border-radius: 0;"
											class="tablinks" onclick="openTab(event, 'tab2')">
											<h6 class="dropdown-header m-0 p-0"><span class="grey darken-2">Chat</span>
											</h6><span
												class="notification-tag badge <?php if ($notificationCount > 0) { ?> badge-danger <?php } else { ?> badge-success <?php } ?> float-right ml-10"
												id="countValue2"></span>
										</button>
										<?php } ?>
									</div>

									<div id="tab1" class="tabcontent active">
										<div class="scrollable-container media-list w-100">

											<?php
											if ($notifications != '') {
												$objSystemUser = new clsSystemUser();
												while ($row = mysqli_fetch_assoc($notifications)) {
													$notificationDetailId1 = stripslashes($row['notificationDetailId']);
													$notificationId1 = stripslashes($row['notificationId']);
													// $businessId = stripslashes($row['businessId']); 
													$systemUserId = stripslashes($row['systemUserId']);
													$title1 = stripslashes($row['title']);
													$description1 = stripslashes($row['description']);

													//get system user details
													$rowUser = $objSystemUser->GetSystemUserDetails($systemUserId);
													if ($_SESSION["isPrimary"] == 1) {
														$profileImage = GetBusinessWhiteLabelLogo(); //GetSystemUserImagePath($systemUserId, $rowUser['imageName']);
													} else {
														$profileImage = $_SESSION["loggedBusinessOwnerImage"];
													}

													?>

													<a href="viewnotificationpopup.html?id=<?php echo EncodeQueryData($notificationId1); ?>&detailId=<?php echo EncodeQueryData($notificationDetailId1); ?>"
														class="viewNotificationPopup"
														notificationDetailId="<?php echo ($notificationDetailId1); ?>">
														<div class="media"
															id="notification_<?php echo ($notificationDetailId1); ?>">
															<div class="media-left align-self-center"><img
																	src="<?php echo ($profileImage); ?>" class="img-fluid"
																	height="40" width="40"></div>
															<div class="media-body">
																<h6 class="media-heading red darken-1"><?php echo ($title1); ?>
																</h6>
																<p class="notification-text font-small-3 text-muted">
																	<?php echo ($description1); ?>
																</p><small>
																	<time class="media-meta text-muted"
																		datetime="2015-06-11T18:29:20+08:00"><?php GetTimeInLastSeen($row['createdDate']); ?></time></small>
															</div>
														</div>
													</a>

													<?php
												}
												unset($objSystemUser);
											}
											?>
										</div>
									</div>

									<!-- chat App Notifications -->
									<div id="tab2" class="tabcontent">
										<div class="scrollable-container media-list w-100" id="chatNotification">

										</div>
									</div>


								</div>
							</li>
							<li class="dropdown-menu-footer" id="dropdown-menu-footer"><a
									class="dropdown-item text-muted text-center"
									href="viewsystemusernotification.html">Read all notifications</a></li>
						</ul>
					</li>
					<!--- notification for business end here ----->

					<li class="dropdown dropdown-user nav-item"><a class="dropdown-toggle nav-link dropdown-user-link"
							href="javascript:void(0);" data-toggle="dropdown"><span
								class="mr-1 user-name text-bold-700">
								<?php echo ($_SESSION["loggedBusinessOwnerName"]); ?>
							</span><span class="avatar avatar-online" style="overflow: visible"><img
									style="height: 100% !important; object-fit: fill;"
									src="<?php echo ($_SESSION["loggedBusinessOwnerImage"]); ?>"
									alt="avatar"><i></i></span></a>
						<div class="custom-dropdown-menu dropdown-menu dropdown-menu-right">
							<!---a class="dropdown-item" href="accountsettings.html"><i class="la la-user-plus"></i>Account Settings</a--->
							<a class="dropdown-item" href="accountsetting.html"><i class="la la-user-plus"></i>Account
								Settings</a>
							<a class="dropdown-item" href="javascript:void(0);"><i class="la la-comments-o"></i>ProBizCa
								Chat</a>
							<a class="dropdown-item" href="javascript:void(0);"><i class="la la-tag"></i>ProBizCa
								Support</a>
							<div class="dropdown-divider"></div><a class="dropdown-item" id="logoutButton" href="javascript:void(0);"><i
									class="ft-power"></i> Logout</a>
							<?php
							if (isset($_SESSION['loggedNewSystemUserId'])) {
								?>
							<div class="dropdown-divider"></div><a class="dropdown-item loginAs"><i
									class="ft-arrow-left"></i> Back to Super Admin</a>
							<?php
							}
							?>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
	<?php if ($planId == 11) { ?>
	<div style="display: flex;align-items: center;">
		<div class="free-trial">
			<?php if ($currentDate == $subendDate) { ?>

			<span class="left-count expiry-today">Expiring Today</span>
			<div class="full-text hidden-text">
				<div class="expire-msg">
					<?php echo $msg; ?>
				</div>
			</div>

			<?php } else { ?>
			<div style="flex-direction: column;" class="left-count">
				<?php echo $numberOfDays; ?> <span class="text-dark"> Days left</span>
			</div>
			<div class="full-text hidden-text">
				<div class="expire-msg ">
					<?php echo $numberOfDays; ?> &nbsp;<span class="text-dark">
						<?php echo $msg; ?>
					</span>
				</div>
			</div>
			<?php } ?>
		</div>
	</div>
	<?php } ?>
</nav>
<!-- END: Header-->

<div class=" header-navbar navbar-expand-sm navbar navbar-horizontal navbar-fixed navbar-dark navbar-without-dd-arrow navbar-shadow  mobile-res"
	role="navigation" data-menu="menu-wrapper">
	<div class="navbar-container main-menu-content sidepanel-mob" data-menu="menu-container">
		<?php if ($_SESSION["isPrimary"] == 1) { ?>
			<ul class="nav navbar-nav" id="main-menu-navigation" data-menu="menu-navigation">
				<li class="nav-item"><a class="nav-link" href="dashboard.html"><i class="la la-home"></i><span
							data-i18n="Dashboard">Dashboard</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="probizca.html"><i class="la la-globe"></i><span
							data-i18n="Dashboard">ProBizCa</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="viewbusinesscustomers.html"><i class="la la-tty"></i><span
							data-i18n="Dashboard">Contacts</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="employee.html" <?php if ($subCardLimits == 0) { ?>
							data-toggle="tooltip" data-popup="tooltip-custom"
							data-original-title="Subscription Plan Limit Is Reached. Please Upgrade Your Subscription to Continue"
							data-bg-color="warning" <?php } ?>><i class="la la-users"></i><span
							data-i18n="Dashboard">Employee/Independent</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="viewbusinessaffiliate.html"><i
							class="la la-user-plus"></i><span data-i18n="Dashboard">Affiliates</span></a>
				</li>
				<!---li class="nav-item"><a class="nav-link" href="javascript:void(0);"><i class="la la-cubes"></i><span data-i18n="Dashboard">Customize</span></a>                    
					</li---->
				<li class="nav-item"><a class="nav-link" href="calender.html"><i class="la la-calendar"></i><span
							data-i18n="Dashboard">Calendar</span></a>
				</li>
				<li class="dropdown nav-item" data-menu="dropdown"><a class="dropdown-toggle nav-link"
						href="javascript:void(0);" data-toggle="dropdown"><i class="la la-hospital-o"></i><span
							data-i18n="Templates">Communication</span></a>
					<ul class="dropdown-menu">
						<!-- <li class="dropdown"><a class="dropdown-item" id="authenticat_user" href="javascript:void(0);" onclick="probizcaChatAuth()"><i class="la la-comments-o"></i><span data-i18n="Vertical">Chat</span></a> 
						</li> -->
						<!-- chat-app.html/userlogin -->
						<!-- <li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
									class="la la-envelope-o"></i><span data-i18n="Horizontal">Email</span></a>
						</li> -->
						<li class="dropdown"><a class="dropdown-item" <?php if ($pushNotification == 1) { ?>
									href="viewnotification.html" <?php } else { ?> data-toggle="tooltip"
									data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
									data-bg-color="warning" <?php } ?>><i class="ft-message-square"></i><span
									data-i18n="Horizontal">Push Notification</span></a>
						</li>
					</ul>
				</li>
				<!-- <li class="nav-item"><a class="nav-link" id="authenticat_user" href="javascript:void(0);" <?php if ($chatRoom == 1) { ?> onclick="probizcaChatAuth(0,0)" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"<?php } ?>><i class="la la-comments-o"></i><span data-i18n="Dashboard">Chat</span></a>
				</li> -->
				<li class="dropdown nav-item" data-menu="dropdown"><a class="dropdown-toggle nav-link"
						href="javascript:void(0);" data-toggle="dropdown"><i class="la la-comments-o"></i><span
							data-i18n="Templates">Chat</span></a>
					<ul class="dropdown-menu">
						<!-- chat-app.html/userlogin -->
						 <?php if ($systemUserId == 0){ ?>
						<li class="dropdown"><a class="dropdown-item" id="authenticat_user" href="javascript:void(0);" <?php if ($chatRoom == 1) { ?> onclick="probizcaChatAuth(0,0)" <?php } else { ?>
									data-toggle="tooltip" data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
								<?php } ?>><i class="la la-comments-o"></i><span data-i18n="Dashboard">Chat</span></a>
						</li>
						<?php } ?>
						<li class="dropdown"><a class="dropdown-item"  <?php if ($chatRoom == 1) { ?> href="chatAppNotification.html" <?php } else { ?> href="javascript:void(0);" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" <?php } ?>>
						<i class="ft-message-square"></i><span data-i18n="Horizontal">Chat App Notification</span></a>
						</li>
					</ul>
				</li>
				<li class="dropdown nav-item" data-menu="dropdown"><a class="dropdown-toggle nav-link"
						href="viewinvoicelist.html" data-toggle="dropdown"><i class="la la-file-archive-o"></i><span
							data-i18n="Templates">Invoice</span></a>
					<ul class="dropdown-menu">
						<li class="dropdown"><a class="dropdown-item" <?php if ($invoice == 1) { ?>
									href="viewinvoicelist.html" <?php } else { ?> data-toggle="tooltip"
									data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
									data-bg-color="warning" <?php } ?>><i class="la la-file-archive-o"></i><span
									data-i18n="Vertical">Invoice</span></a>
						</li>
						<li class="dropdown"><a class="dropdown-item" <?php if ($invoice == 1) { ?>
									href="viewaffiliatepayoutlist.html" <?php } else { ?> data-toggle="tooltip"
									data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
									data-bg-color="warning" <?php } ?>><i class="la la-file-archive-o"></i><span
									data-i18n="Vertical">Affiliate Payout</span></a>
						</li>
						<li class="dropdown"><a class="dropdown-item" <?php if ($invoice == 1) { ?> href="viewpricing.html"
								<?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
									data-bg-color="warning" <?php } ?>><i class="la la-money"></i><span
									data-i18n="Vertical">Prices</span></a>
						</li>

					</ul>
				</li>
				<li class="nav-item"><a class="nav-link" <?php if ($todo == 1) { ?> href="todo.html" <?php } else { ?>
							data-toggle="tooltip" data-popup="tooltip-custom"
							data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
							data-bg-color="warning" <?php } ?>><i class="la la-check-square-o"></i><span
							data-i18n="Dashboard">To Do</span></a>
				</li>
				<!-- <li class="nav-item"><a class="nav-link" <?php if ($todo == 1) { ?> href="todo.html" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" data-bg-color="warning" <?php } ?>><i class="la la-bullhorn"></i><span data-i18n="Dashboard">Announcements</span></a>
				</li> -->
				<li class="dropdown nav-item" data-menu="dropdown"><a class="dropdown-toggle nav-link"
						href="javascript:void(0);" data-toggle="dropdown"><i class="la la-ellipsis-v"></i><span
							data-i18n="Templates">More</span></a>
					<ul class="dropdown-menu" style="left: -7rem;">
						<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
									class="la la-map-marker"></i><span data-i18n="Vertical">Geolocation</span></a>
						</li>
						<li class="dropdown"><a class="dropdown-item" <?php if ($isCouponServices == 1) { ?>
									href="businessservices.html" <?php } else { ?> data-toggle="tooltip"
									data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
									data-bg-color="warning" <?php } ?>><i class="la la-money"></i><span
									data-i18n="Horizontal">Incentives</span></a>
						</li>
						<li class="dropdown"><a class="dropdown-item" href="myOrders.html"><i class="la la-money"></i><span
									data-i18n="Horizontal">My Orders</span></a>
						</li>
						<?php if ($surveychk == 0) { ?>
							<li class="dropdown" <?php if ($surveychk == 0) { ?>data-toggle="tooltip" data-popup="tooltip-custom"
									data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
								<?php } ?>><a class="dropdown-item" href="#" data-toggle="dropdown"><i
										class="la la-file-text"></i><span data-i18n="Form Layouts">Survey</span></a>
							</li>
						<?php } else { ?>
							<li class="dropdown dropdown-submenu" data-menu="dropdown-submenu"><a
									class="dropdown-item dropdown-toggle" href="#" data-toggle="dropdown"><i
										class="la la-file-text"></i><span data-i18n="Form Layouts">Survey</span></a>
								<ul class="dropdown-menu">
									<li data-menu=""><a class="dropdown-item"
											href="<?php if ($surveychk == 1) { ?>viewsurveylist.html<?php } else { ?>javascript:void(0); <?php } ?>"
											<?php if ($surveychk == 0) { ?>data-toggle="tooltip" data-popup="tooltip-custom"
												data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
											<?php } ?>><span data-i18n="Basic Forms">Surveys</span></a></li>
									<li data-menu=""><a class="dropdown-item"
											href="<?php if ($surveychk == 1) { ?>surveymasters.html<?php } else { ?>javascript:void(0); <?php } ?>"
											<?php if ($surveychk == 0) { ?>data-toggle="tooltip" data-popup="tooltip-custom"
												data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
											<?php } ?>><span data-i18n="Basic Forms">Survey Masters</span></a></li>
								</ul>
							</li>
						<?php } ?>
						<li class="dropdown dropdown-submenu" data-menu="dropdown-submenu"><a
								class="dropdown-item dropdown-toggle" href="#" data-toggle="dropdown"><i
									class="la la-file-text"></i><span data-i18n="Form Layouts">Reports</span></a>
							<ul class="dropdown-menu">
								<li data-menu=""><a class="dropdown-item" href="newcontactreport.html" data-toggle=""><span
											data-i18n="Basic Forms">New Contact Report</span></a>
								</li>
								<li data-menu=""><a class="dropdown-item" href="viewformsubmissionreport.html"
										data-toggle=""><span data-i18n="Basic Forms">Form Submission</span></a>
								</li>
								<li data-menu=""><a class="dropdown-item" href="referredByReport.html" data-toggle=""><span
											data-i18n="Basic Forms">Referred By</span></a>
								</li>
								<li data-menu=""><a class="dropdown-item" href="incentiveReport.html" data-toggle=""><span
											data-i18n="Basic Forms">Incentive Report</span></a>
								</li>
								<li data-menu=""><a class="dropdown-item"
										href="<?php if ($surveychk == 1) { ?>surveyReport.html<?php } else { ?>javascript:void(0); <?php } ?>"
										<?php if ($surveychk == 0) { ?>data-toggle="tooltip" data-popup="tooltip-custom"
											data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
										<?php } ?>><span data-i18n="Basic Forms">Survey Report</span></a>
								</li>
								<li data-menu=""><a class="dropdown-item"
										href="<?php if ($customQrFormchk == 1) { ?>customQRFormReport.html<?php } else { ?>javascript:void(0); <?php } ?>"
										<?php if ($customQrFormchk == 0) { ?>data-toggle="tooltip" data-popup="tooltip-custom"
											data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
										<?php } ?>><span data-i18n="Basic Forms">Custom QR Report</span></a>
								</li>
							</ul>
						</li>
						<li class="dropdown"><a class="dropdown-item" href="shoppingCart.html"><i
									class="la la-cart-plus"></i><span data-i18n="Horizontal">Shopping Cart</span></a>
						</li>
						<!-- <li class="dropdown"><a class="dropdown-item" href="socialfeed.html"><i class="la la-paw"></i><span
									data-i18n="Horizontal">Social Feed</span></a>
						</li> -->
						<li class="dropdown"><a class="dropdown-item" href="statistics.html"><i
									class="la la-sliders"></i><span data-i18n="Horizontal">Statistics</span></a>
						</li>
						<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
									class="la la-tag"></i><span data-i18n="Horizontal">Support</span></a>
						</li>
						<!-- <li class="dropdown"><a class="dropdown-item" <?php if ($todo == 1) { ?> href="todo.html" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" data-bg-color="warning" <?php } ?>><i class="la la-check-square-o"></i><span data-i18n="Horizontal">To Do</span></a>
						</li> -->
						<li class="dropdown dropdown-submenu" data-menu="dropdown-submenu"><a
								class="dropdown-item dropdown-toggle" href="#" data-toggle="dropdown"><i
									class="la la-briefcase"></i><span data-i18n="Form Layouts">Tools</span></a>
							<ul class="dropdown-menu">
								<li class="dropdown dropdown-submenu" data-menu="dropdown-submenu"><a
										class="dropdown-item dropdown-toggle" href="#" data-toggle="dropdown"><i
											class="la la-calculator"></i><span
											data-i18n="Form Layouts">Calculators</span></a>
									<ul class="dropdown-menu" style="top: -21rem;">
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Total Sales
													Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Cost Per
													Acquisition Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Customer
													Lifetime Value Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Email
													Marketing ROI Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Direct Mail
													ROI Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Profit Loss
													Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Churn Rate
													Calculator</span></a>
										</li>
										<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
													class="la la-plus-circle"></i><span data-i18n="Horizontal">Pay Per Click
													ROI Calculator</span></a>
										</li>
									</ul>
								</li>
							</ul>
						</li>
						<li class="dropdown"><a class="dropdown-item" href="ProBizCa+.html"><i
									class="la la-plus-circle"></i><span data-i18n="Horizontal">ProBizCa +</span></a>
						</li>
						<!-- onclick="probizcaPlus()" -->

						<div class="dropdown-divider"></div>
						<li class="dropdown"><a class="dropdown-item" href="javascript:void(0);"><i
									class="la la-cubes"></i><span data-i18n="Vertical">Customize</span></a>
						</li>
					</ul>
				</li>
			</ul>
		<?php } else { ?>
			<ul class="nav navbar-nav" id="main-menu-navigation" data-menu="menu-navigation">
				<li class="nav-item"><a class="nav-link" href="dashboard.html"><i
							class="la la-home"></i><span data-i18n="Dashboard">Dashboard</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="<?php echo $viewProBizCaPath; ?>"
						target="_blank"><i class="la la-street-view"></i><span data-i18n="business">View ProBizCa</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="services.html"><i
							class="la la-wrench"></i><span data-i18n="business">Incentives</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="viewemployeecontacts.html"><i class="la la-tty"></i><span>
							data-i18n="Dashboard">Contacts</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="employeecalender.html"><i class="la la-calendar"></i><span
							data-i18n="Dashboard">Calendar</span></a>
				</li>
				<li class="nav-item"><a class="nav-link" href="todo.html"><i class="la la-check-square-o"></i><span
							data-i18n="Dashboard">To Do</span></a>
				<?php if ($systemUserId == ''){ ?>
				<li class="nav-item"><a class="nav-link" id="authenticat_user" href="javascript:void(0);" <?php if ($chatRoom == 1) { ?> onclick="probizcaChatAuth(0,0)" <?php } else { ?> data-toggle="tooltip"
							data-popup="tooltip-custom"
							data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue"
						<?php } ?>><i class="la la-comments-o"></i><span data-i18n="Vertical">Chat</span></a>
				</li>
				<?php } ?>

			</ul>
		<?php } ?>
	</div>
</div>


<script>
	document.addEventListener('DOMContentLoaded', () => {
		let notification = document.querySelector('.dropdown-notification');
		let dropdownMenu = document.querySelector('.custom-dropdown-menu');
		let isDropdownVisible = false;

		notification.addEventListener('click', (event) => {
			event.stopPropagation();

			notification.classList.toggle('showDropdown');
			dropdownMenu.classList.toggle('showDropdown');
			isDropdownVisible = !isDropdownVisible;
		});

		dropdownMenu.addEventListener('click', (event) => {
			event.stopPropagation();

			let headerClicked = event.target.closest('.dropdown-menu-header');

			if (headerClicked) {
				return;
			}

			notification.classList.toggle('showDropdown');
			dropdownMenu.classList.toggle('showDropdown');
			isDropdownVisible = !isDropdownVisible;
		});

		document.addEventListener('click', (event) => {
			if (isDropdownVisible) {
				if (!notification.contains(event.target) && !dropdownMenu.contains(event.target)) {
					notification.classList.remove('showDropdown');
					dropdownMenu.classList.remove('showDropdown');
					isDropdownVisible = false;
				}
			}
		});
	});
</script>
<script>
	function probizcaPlus() {
		var loggedBusinessName = '<?php echo $loggedBusinessName; ?>';
		var loggedBusinessOwnerName = '<?php echo $loggedBusinessOwnerName; ?>';
		var loggedBusinessEmail = '<?php echo $loggedBusinessEmail; ?>';
		var businessphoneNo = '<?php echo $businessphoneNo; ?>';

		$.ajax({
			type: "POST",
			url: './../ajax/createVboutAccount.html',
			data: {
				'busunessName': loggedBusinessName,
				'businessOwnerName': loggedBusinessOwnerName,
				'businessEmail': loggedBusinessEmail,
				'businessphoneNo': businessphoneNo,
			},
			// dataType: "json",
			success: function (data) {
				console.log(data);
				// alert(data);
				window.open(data, "_blank");

			}

		});

	}


	// function probizcaChatAuth(conversationId, authorRole) {


	// 	// var userconversationId = (conversationId > 0) ? conversationId : 0;

	// 	var loggedBusinessId = $('#loggedBusinessId').val();
	// 	var loggedUserId = $('#loggedEmployeeId').val();
	// 	var isPrimary = $('#isPrimary').val();
	// 	var roleId = (isPrimary == 1) ? 2 : 3;

	// 	$.ajax({
	// 		type: "GET",
	// 		url: './../ajax/ajax_authenticat_chat_user.html',
	// 		data: {
	// 			'loggedBusinessId': loggedBusinessId,
	// 			'loggedUserId': loggedUserId,
	// 			'roleId': roleId,
	// 			'userconversationId': conversationId,
	// 			'authorRole': authorRole

	// 		},
	// 		// dataType: "json",
	// 		success: function (data) {
	// 			console.log(data);
	// 			// alert(data);
	// 			window.open(data, "_blank");
	// 			setTimeout(() => {
	// 				console.log('inside here');
	// 				GetChatNotificationsList();


	// 			}, 15000);

	// 		}

	// 	});

	// }
</script>

<script>
	function openTab(event, tabId) {
		// Get all elements with class="tabcontent" and hide them
		var tabcontents = document.getElementsByClassName("tabcontent");
		for (var i = 0; i < tabcontents.length; i++) {
			tabcontents[i].style.display = "none";
		}

		// Get all elements with class="tablinks" and remove the "active" class
		var tablinks = document.getElementsByClassName("tablinks");
		for (var i = 0; i < tablinks.length; i++) {
			tablinks[i].classList.remove("active");
		}

		// Show the current tab, and add an "active" class to the button that opened the tab
		document.getElementById(tabId).style.display = "block";
		event.currentTarget.classList.add("active");

		if (tabId == 'tab2') {
			$('.dropdown-menu-footer').addClass('hide');
			GetChatNotificationsList(role, loggedUserEmail, businessId, ref_userId);
		}
		else {
			$('.dropdown-menu-footer').removeClass('hide');

		}
	}

	// Set the default tab to be opened on page load (e.g., Tab 1)
	document.getElementById("tab1").style.display = "block";
	document.querySelector(".tablinks:first-child").classList.add("active");
</script>