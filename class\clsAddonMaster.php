<?php
class clsAddonMaster
{

	var $addonId = 0;
	var $name = 0;
	var $subscriptionType = 0;
	var $amount = 0;
	var $qty = 0;

	var $addondtlId = 0;
	var $businessId = 0;
	var $defaultCount = 0;
	var $addonNmId = 0;
	var $dfCurrentMonth = 0;
	var $dfUsed = 0;
	var $purchasedCount = 0;
	var $purUsed = 0;
	var $usageDate = '';



	function SaveAddons($addonId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($addonId > 0) {
			$sql = "UPDATE addons SET
							name='" . $this->name . "',
							subscriptionType=" . $this->subscriptionType . ",
							amount='" . ($this->amount) . "',
							qty='" . ($this->qty) . "'
							WHERE addonId=" . $addonId;

			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO addons(name,subscriptionType,amount,qty,usageDate) 
								VALUES(								  				
									  '" . addslashes($this->name) . "',								
									  '" . addslashes($this->subscriptionType) . "',								
									  '" . addslashes($this->amount) . "',								
									  '" . addslashes($this->qty) . "',
									  '" . (date("Y-m-d h:i:s")) . "'						
									  )";
			// echo $sql;exit;									  
			$addonId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $addonId;
	}

	function SaveAddonsDtls($addondtlId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($addondtlId > 0) {
			$sql = "UPDATE addondetails SET
							businessId='" . $this->businessId . "',
							addonNmId=" . $this->addonNmId . ",
							dfCurrentMonth='" . ($this->dfCurrentMonth) . "',
							defaultCount='" . ($this->defaultCount) . "'
							dfUsed='" . ($this->dfUsed) . "'
							purchasedCount='" . ($this->purchasedCount) . "'
							purUsed='" . ($this->purUsed) . "'
							WHERE addondtlId=" . $addondtlId;

			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO addondetails(businessId,addonNmId,dfCurrentMonth,defaultCount) 
								VALUES(								  				
									  " . ($this->businessId) . ",								
									  " . ($this->addonNmId) . ",								
									  " . ($this->dfCurrentMonth) . ",								
									  " . ($this->defaultCount) . "									
									  )";
			// echo $sql;exit;									  
			$addondtlId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $addondtlId;
	}

	function GetAddonDetails($addonId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM addons WHERE name = $addonId AND subscriptionType=3";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllAddons()
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM addons GROUP by name";
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAddonpurchaseDtls($businessId, $addonNameId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM addondetails WHERE businessId=$businessId AND addonNmId=$addonNameId AND dfCurrentMonth = MONTH(CURRENT_DATE())";
		// echo $sql;exit;  		 
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetAddonpurchaseUsedDtls($businessId, $addonNameId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT SUM(purUsed) AS purUsed, SUM(dfUsed) AS dfUsed FROM addondetails WHERE businessId=$businessId AND addonNmId=$addonNameId";
		// echo $sql;exit;  		 
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetsubscriptionTypeDetails($addonNameId, $subscriptionType)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM addons WHERE name = " . $addonNameId . " AND subscriptionType=" . $subscriptionType . " ORDER BY amount ASC";
		// if($subscriptionType > 0)
		// {
		// 	$sql.=" AND subscriptionType=".$subscriptionType." ORDER BY amount ASC";
		// }
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetPurchaseAddonDetails($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM addondetails WHERE businessId=$businessId ORDER BY addonNmId";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetPurchaseAddonDetailsInRow($businessId)
	{
		$month = date('m');
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT
					adtls1.addondtlId AS addondtlId_Email,
					adtls1.*,
					adtls2.addondtlId AS addondtlId_Sms,
					adtls2.addonNmId AS addonNm,
					adtls2.defaultCount AS dfSMS,
					adtls2.dfUsed AS dfsmsU,
					adtls2.purchasedCount AS SmsPurchaseCnt,
					adtls2.purUsed AS SmsPurUsed
				FROM
					addondetails AS adtls1
				LEFT JOIN
					addondetails AS adtls2 ON adtls2.businessId=$businessId AND adtls2.addonNmId=2
				WHERE
					adtls1.businessId=$businessId AND adtls1.addonNmId=1
					AND adtls2.dfCurrentMonth = MONTH(CURRENT_DATE())
					AND adtls1.dfCurrentMonth = MONTH(CURRENT_DATE()) AND YEAR(adtls1.usageDate) = YEAR(CURRENT_DATE()) AND YEAR(adtls2.usageDate) = YEAR(CURRENT_DATE())";
		// 
		// echo "<br>";
		// echo $sql;
		// echo "<br>";
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	// date 20-12-2023
	function GetAllAddonsDetails($businessId)
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT adtls1.*,adtls2.addonNmId as addonNm,adtls2.defaultCount as dfSMS, adtls2.dfUsed AS dfsmsU,adtls2.purchasedCount as SmsPurchaseCnt,adtls2.purUsed AS SmsPurUsed
				FROM addondetails AS adtls1
				LEFT JOIN addondetails AS adtls2 ON adtls2.businessId=$businessId AND adtls2.addonNmId=2
				WHERE adtls1.businessId=$businessId AND adtls1.addonNmId=1 ";
		// AND adtls2.dfCurrentMonth = MONTH(CURRENT_DATE()) AND adtls1.dfCurrentMonth = MONTH(CURRENT_DATE())
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetAllfreeAddonsdtls($businessId)
	{


		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT
				SUM(CASE WHEN addonNmId = 1 THEN defaultCount ELSE 0 END) AS totaldfEmail,
				SUM(CASE WHEN addonNmId = 2 THEN defaultCount ELSE 0 END) AS totaldfSms
				FROM
				addondetails
				WHERE
				(addonNmId = 1 OR addonNmId = 2) AND businessId = $businessId; ";
		// AND adtls2.dfCurrentMonth = MONTH(CURRENT_DATE()) AND adtls1.dfCurrentMonth = MONTH(CURRENT_DATE())
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function GetMTDAddonDtls($businessId)
	{

		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT businessId, SUM(CASE WHEN addonNmId = 1 THEN dfUsed + purUsed ELSE 0 END) AS Email_MTD_Total, SUM(CASE WHEN addonNmId = 2 THEN dfUsed + purUsed ELSE 0 END) AS SMS_MTD_Total FROM addondetails WHERE businessId=$businessId AND  MONTH(usageDate) = MONTH(CURRENT_DATE()) AND YEAR(usageDate) = YEAR(CURRENT_DATE()) AND dfCurrentMonth = MONTH(CURRENT_DATE()) GROUP BY businessId";
		// echo $sql;exit;  		 
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetYTDAddonDtls($businessId)
	{

		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT businessId, SUM(CASE WHEN addonNmId = 1 THEN dfUsed + purUsed ELSE 0 END) AS Email_YTD_Total, SUM(CASE WHEN addonNmId = 2 THEN dfUsed + purUsed ELSE 0 END) AS SMS_YTD_Total FROM addondetails WHERE businessId=$businessId AND YEAR(usageDate) = YEAR(CURRENT_DATE()) AND dfCurrentMonth <= MONTH(CURRENT_DATE()) GROUP BY businessId";
		// echo $sql;exit;  		 
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function UpdateAddondtls($dfCurrentMonth, $defaultCount, $businessId, $addonNameId)
	{
		if ($businessId > 0 && $addonNameId > 0) {
			$objDB = new clsDB();
			$sql = "UPDATE addondetails SET dfCurrentMonth =" . $dfCurrentMonth . ",defaultCount=" . $defaultCount . " WHERE businessId = $businessId AND addonNmId=$addonNameId";
			// echo $sql;exit; 
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}


	function UpdateAddonPurchasedtls($businessId, $addonNameId, $purchasedCount)
	{
		if ($businessId > 0 && $addonNameId > 0) {
			$objDB = new clsDB();
			$sql = "UPDATE addondetails SET purchasedCount =" . $purchasedCount . " WHERE businessId = $businessId AND addonNmId=$addonNameId AND dfCurrentMonth = MONTH(CURRENT_DATE())";
			// echo $sql;exit; 
			$addondtlId = $objDB->ExecuteQuery($sql);
			unset($objDB);
			return $addondtlId;
		}
	}

	function UpdateDefaultUsedCount($businessId, $addonNameId, $dfUsed, $addondtlId)
	{
		if ($businessId > 0 && $addonNameId > 0) {
			$objDB = new clsDB();
			$sql = "UPDATE addondetails SET dfUsed =" . $dfUsed . " WHERE businessId = $businessId AND addonNmId=$addonNameId AND addondtlId =$addondtlId";
			// echo $sql;exit; 
			$addondtlId = $objDB->ExecuteQuery($sql);
			unset($objDB);
			return $addondtlId;
		}
	}

	function UpdatePurchaseUsedCount($businessId, $addonNameId, $purUsed, $addondtlId)
	{
		if ($businessId > 0 && $addonNameId > 0) {
			$objDB = new clsDB();
			$sql = "UPDATE addondetails SET purUsed =" . $purUsed . " WHERE businessId = $businessId AND addonNmId=$addonNameId AND addondtlId =$addondtlId";
			// echo $sql;exit; 
			$addondtlId = $objDB->ExecuteQuery($sql);
			unset($objDB);
			return $addondtlId;
		}
	}


	function renewMonthlyDefaultAddon()
	{
		$month = date('m');
		$objDB = new clsDB();
		$sql = "UPDATE `addondetails` SET dfCurrentMonth=$month,dfUsed=0 WHERE dfCurrentMonth!=" . $month;
		// echo $sql;exit;
		$addondtlId = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $addondtlId;
	}


	function checkcurrentMonth()
	{

		$month = 3;
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT *  FROM `addondetails` WHERE  YEAR(usageDate) = YEAR(CURRENT_DATE()) AND MONTH(usageDate) = $month AND dfCurrentMonth = $month";
		// $sql = "SELECT * FROM `addondetails` WHERE YEAR(usageDate) = '2024' AND MONTH(usageDate) = $month AND dfCurrentMonth = $month";

		echo $sql;
		exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;

		// 
	}

	function getlastMonthAddondtls()
	{
		$month = date('m');
		$rows = "";
		$objDB = new clsDB();
		if ($month != 01) {
			$sql = "SELECT *
            		FROM addondetails
            		WHERE (addonNmId = 1 OR addonNmId = 2)
            		  AND dfCurrentMonth = $month - 1
            		  AND YEAR(usageDate) = YEAR(CURRENT_DATE())
            		  AND MONTH(usageDate) = IF($month = 1, 12, $month - 1)";
		} else {

			$sql = "SELECT *
                        FROM addondetails
                        WHERE (addonNmId = 1 OR addonNmId = 2)
                          AND dfCurrentMonth = 12
                          AND YEAR(usageDate) = YEAR(CURRENT_DATE()) - 1";
		}

		// 		echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function getlastMonthAddondtlsOfBusiness($businessId)
	{
		$month = date('m');
		$rows = "";
		$objDB = new clsDB();
		if ($month != 01) {
			$sql = "SELECT *
            		FROM addondetails
            		WHERE (addonNmId = 1 OR addonNmId = 2)
            		  AND dfCurrentMonth = $month - 1
            		  AND YEAR(usageDate) = YEAR(CURRENT_DATE())
            		  AND MONTH(usageDate) = IF($month = 1, 12, $month - 1) 
					  AND businessId = $businessId
                      ORDER BY businessId ASC";
		} else {

			$sql = "SELECT *
                        FROM addondetails
                        WHERE (addonNmId = 1 OR addonNmId = 2)
                          AND dfCurrentMonth = 12
                          AND YEAR(usageDate) = YEAR(CURRENT_DATE()) - 1
						  AND businessId = $businessId
                     	ORDER BY businessId ASC";
		}

				// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function checkBusinessAddonEntryForCurrentMonth($businessId, $addonNmId)
	{
		$month = date('m');	
		$rows = "";
		$objDB = new clsDB();
		// $sql = "SELECT COUNT(*) AS recordcount FROM `addondetails` WHERE `businessId` = $businessId AND addonNmId=$addonNmId  AND dfCurrentMonth=$month";	
		$sql = "SELECT COUNT(*) AS recordcount FROM `addondetails` WHERE `businessId` = $businessId AND dfCurrentMonth=$month AND YEAR(usageDate) = YEAR(CURRENT_DATE())";
		// echo "<br>";		 
		// echo "<br>";		 
		// echo $sql;
		// echo "<br>";		 
		// echo "<br>";		 
		// exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $rows;
	}

	function renewAddonPerMonth()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO addondetails(businessId,addonNmId,dfCurrentMonth,defaultCount,dfUsed,purchasedCount,purUsed,usageDate) 
		VALUES(								  				
			  " . ($this->businessId) . ",								
			  " . ($this->addonNmId) . ",								
			  " . ($this->dfCurrentMonth) . ",								
			  " . ($this->defaultCount) . "	,								
			  " . ($this->dfUsed) . "	,								
			  " . ($this->purchasedCount) . "	,								
			  " . ($this->purUsed) . "	,								
			  '" . ($this->usageDate) . "'							
			  )";
		// echo $sql;exit;			 						  
		$addondtlId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $addondtlId;
	}

	function downgredAddonPlan($businessId)
	{
		// $objDB = new clsDB();
		// $sql = "UPDATE `addondetails` SET dfUsed=0,purchasedCount=0,purUsed=0 WHERE businessId=$businessId";
		// // echo $sql;
		// $addondtlId = $objDB->ExecuteQuery($sql);
		// unset($objDB);
		// return $addondtlId;

	}


	function deleteAddonData($addonId)
	{
		$result = "";
		if ($addonId > 0) {
			$objDB = new clsDB();
			$sql = 'DELETE FROM addons WHERE addonId=' . $addonId;
			// echo $sql;exit;	
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}
}
