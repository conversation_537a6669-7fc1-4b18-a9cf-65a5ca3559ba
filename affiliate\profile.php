<?php
    //include classes and config file here	
	include('./includes/validateaffiliatelogin.php');
	include('../includes/config.php');
    require_once('../includes/commonfun.php');
    include('../class/clsDB.php');
    include('../class/clsCountryStateMaster.php');
    include('../class/clsAffiliate.php');
	include('../class/clsChatApp.php');

	
	//variables
	$countries = '';	
	$dbCountryId = '224';
	$stateId = 0;
	$affiliateId = $_SESSION["loggedAffiliateId"];	
	$firstName = '';
	$lastName = '';
	$email = '';
	$phoneNo = '';
	$address = '';
	$city = '';
	$zipCode = '';
	$imageName = '';
	$affiliateImagePath = '';
	$pageTitle ='Edit';
	$buttonTitle ='Update';
	
	//Get Location
    $objCountryStateMaster= new clsCountryStateMaster();
    $countries = $objCountryStateMaster->GetAllCountry();
    unset($objCountryStateMaster);
		
	//object
	$objAffiliate = new clsAffiliate();
	
	//get employee details
	$row = $objAffiliate->GetAffiliateDetails($affiliateId);
	unset($objAffiliate);
	
	$firstName  = stripslashes($row['firstName']);
	$lastName  = stripslashes($row['lastName']);
	$email  = stripslashes($row['email']);
	$phoneNo  = stripslashes($row['phoneNo']);
	$address  = stripslashes($row['address']);
	$address2  = stripslashes($row['address2']);
	$city  = stripslashes($row['city']);
	$zipCode  = stripslashes($row['zipCode']);
	$stateId  = stripslashes($row['stateId']);
	$imageName  = stripslashes($row['imageName']);
	
	//get employee image path
	$affiliateImagePath = GetAffiliateImagePath($affiliateId,$imageName);		
	
	
	//get Country from State
	$objCountryStateMaster = new clsCountryStateMaster();
	$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
	unset($objCountryStateMaster);


	$objChatApp = new clsChatApp();
    $usermanagemetData=$objChatApp->getUserDetails(5,$affiliateId);
    $usermanagemetId = $usermanagemetData['id'];
?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">
<head>
   <title>Profile</title>
   <?php include('includes/headerCss.php'); ?> 
   <link rel="stylesheet" href="<?php echo BASE_PATH;?>/assets/vendors/js/magnificpopup/css/magnific-popup.css">
   <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/vendors/css/forms/selects/select2.min.css">
   <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/css/plugins/forms/extended/form-extended.css">
   		<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/assets/cropper.css">

   	<style>
        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }
 
        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }
 
 
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }
 
        .required-select2 {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }
 
        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }
 
        .select2-container--default .select2-selection--single {
            border: none !important;
        }
 
        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }
 
        .form-control {
            height: 45px;
        }
 
 
        .input-group {
            width: 100%;
        }
 
        .required-input {
            border-left: 3px solid red !important;
        }
 
        .input-group-addon {
            background: transparent;
        }
 
        .formSubHeading {
            border-bottom: 2px solid #d9d6d657;
            padding: 3px 0;
            position: relative;
        }
 
        input[type="file"] {
            background-color: #fff !important;
        }
 
        /* Cropped image state styling */
        .upload-area.cropped-state {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border: 2px solid #667eea;
        }
 
        .upload-area.cropped-state:hover {
            background: linear-gradient(135deg, #f0f4ff 0%, #e0f0f6 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
 
        .modal-lg {
            width: 90%;
            max-width: 1200px;
        }
 
        .cropped-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 10px;
        }
 
        .success-message {
            color: #01A750;
            margin-top: 5px;
            font-weight: bold;
        }
 
        /* Make sure the cropper is visible in the modal */
        #cropperModal #cropperSection {
            display: block !important;
        }
		button{
        color: black !important;
		font-size:14px !important;
}

	/* Responsive wrapper */
.cropper-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

/* Make upload and cropper section full width on smaller screens */
@media (max-width: 1024px) {
    .upload-section,
    .cropper-section {
        width: 100% !important;
    }

    .image-container {
        width: 100%;
        height: auto;
    }

    .preview-section {
        width: 100%;
    }
}

/* Stack image and preview vertically on tablets and below */
@media (max-width: 768px) {
    .cropper-section > div {
        flex-direction: column !important;
        align-items: center !important;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .image-container {
        width: 100% !important;
        height: 300px !important;
    }

    .preview-section {
        width: 100%;
        margin-top: 20px;
        text-align: center;
    }

    .preview-section canvas {
        max-width: 100%;
        height: auto;
    }

    .upload-section {
        padding: 15px;
    }
}
@media (max-width: 357px) {
form .form-actions.right {
    text-align: right;
    display: flex;
}
form .form-actions {
    border-top: 1px solid #d1d5ea;
    padding: 20px 0;
    margin-top: 20px;
    display: flex;
}
}
/* Smaller mobile adjustments */
@media (max-width: 576px) {
    .mode-buttons {
        flex-direction: column;
        width: 100%;
    }

    .mode-btn {
        width: 100%;
        text-align: center;
    }

    .action-btn {
        width: 100%;
    }

    .zoom-controls {
        /* flex-direction: column; */
        gap: 5px;
    }

    .zoom-slider {
        width: 100%;
    }

    .upload-content p {
        font-size: 1rem;
    }

    .upload-content .file-types {
        font-size: 0.8rem;
    }

    .controls {
        padding: 10px;
        gap: 15px;
    }

	button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled) {
    cursor: pointer;
    font-size: 14px  !important;
}
}
@media (max-width: 576px) {
     .cropper-section {
        width: 100% !important;
    }
}


    </style>
</head>
<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

    <!-- include header-->
    <?php include('includes/header.php'); ?>
	
    <!-- BEGIN: Content-->
    <div class="app-content content">
        <div class="content-overlay"></div>
        <div class="content-wrapper">            
            <div class="content-body">
                
                <!-- Basic form layout section start -->
                <section id="horizontal-form-layouts">
                    <div class="row">
                        <div class="col-md-12">
						    <!--- show status messages --->
							<?php
								if (isset($_GET["status"]))
								{
									if($_GET["status"] == "added")
									{
										?>											
										<div class="alert alert-success alert-dismissible mt-2" role="alert">
											<button type="button" class="close" data-dismiss="alert" aria-label="Close">
												<span aria-hidden="true">&times;</span>
											</button>
											<strong>Added.</strong>
										</div>
										 
									<?php 
									}
									else if($_GET["status"] == "updated")
									{
										?>											 
										<div class="alert alert-success alert-dismissible mt-2" role="alert">
											<button type="button" class="close" data-dismiss="alert" aria-label="Close">
												<span aria-hidden="true">&times;</span>
											</button>
											<strong>Updated.</strong>
										</div>
									<?php 
									}
									else if($_GET["status"] == "error")
									{
										?>											 
										<div class="alert alert-danger alert-dismissible mt-2" role="alert">
											<button type="button" class="close" data-dismiss="alert" aria-label="Close">
												<span aria-hidden="true">&times;</span>
											</button>
											<strong>Error.</strong>
										</div>
									<?php 
									}
								}
							?>
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title" id="horz-layout-colored-controls">Profile</h4>
                                    <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                    <div class="heading-elements">
                                        <ul class="list-inline mb-0">                                         
                                            <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                            <li><a data-action="expand"><i class="ft-maximize"></i></a></li>                                           
                                        </ul>
                                    </div>
                                </div>								
                                <div class="card-content collpase show">								   
                                    <div class="card-body">                                       
                                        <form class="form form-horizontal" id="employeeForm" method="post" action="profilesubmit.html" enctype="multipart/form-data">
										    <input type="hidden" name="affiliateId" id="affiliateId" value="<?php echo ($affiliateId);?>">										    
										    <input type="hidden" name="email" id="email" value="<?php echo ($email);?>">
											<input type="hidden" name="usermanagemetId" id="usermanagemetId" value="<?php echo ($usermanagemetId); ?>">

                                            <div class="form-body">
                                                <h4 class="form-section"><i class="ft-user"></i>Personal Details</h4>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput4">First Name<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="firstName" class="form-control" placeholder="First Name" name="firstName" value="<?php echo ($firstName);?>" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="lastName">Last Name<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="lastName" class="form-control" placeholder="Last Name" name="lastName" value="<?php echo ($lastName);?>" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
												<div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="address">Address 1</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="address" class="form-control" placeholder="Address 1" name="address" value="<?php echo ($address);?>">
                                                            </div>
                                                        </div>
                                                    </div>
													<div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="address">Address 2</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="address2" class="form-control" placeholder="Address 2" name="address2" value="<?php echo ($address2);?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="city">City</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="city" class="form-control" placeholder="City" name="city" value="<?php echo ($city);?>">
                                                            </div>
                                                        </div>
                                                    </div>													
                                                </div>
                                                <div class="row">                                                    
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="phoneNo">Mobile No.</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="phoneNo" class="form-control phone-inputmask" placeholder="Mobile No." name="phoneNo" value="<?php echo ($phoneNo);?>">
                                                            </div>
                                                        </div>
                                                    </div>
													<div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="city">Zip Code</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="zipCode" class="form-control" placeholder="Zip Code" name="zipCode" value="<?php echo ($zipCode);?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>                                                
												<div class="row">												    
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput3">Country</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <select id="cboCountry" name="cboCountry" class="select2 form-control step1">
																	<option value="" selected>Select</option>
																	<?php
																		
																		if($countries!="")
																		{
																			while($row = mysqli_fetch_assoc($countries))
																			{
																				 $location_id  = $row['location_id'];
																				 $name  = stripslashes($row['name']);

																				 ?>
																				  <option value="<?php echo($location_id); ?>" <?php if($dbCountryId==$location_id){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
																				 <?php

																			}
																		}
																	?>
																</select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput4">State</label>
                                                            <div class="col-md-9 mx-auto">
                                                               <select id="stateId" name="stateId" class="select2 form-control step2" required>
																<option value="" selected>Select</option>
																</select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>												
                                                <h4 class="form-section"><i class="ft-mail"></i> Profile Image</h4>
	 <input type="hidden" id="fileLogo" name="fileLogo" value="">
            <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                      <div class="cropper-container cropper-instance-1">
                                                    <input type="hidden" name="imageName" id="imageName" class="fileLogo" value="<?php echo ($imageName);?>">
                                                    <input type="hidden" class="hasCroppedImage" value="0">


        <section class="upload-section" style="width: 37%">
            <div class="upload-area">
                <input type="file" class="fileInput" accept="image/*" hidden>
                <div class="upload-content">
                    <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    <p>Click to browse or drag and drop an image</p>
                    <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                </div>
            </div>
        </section>

        <section class="cropper-section" style="display: none;">
            <div class="controls">
                <div class="control-group">
                    <div class="mode-buttons">
                        <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                        <button type="button" class="mode-btn" data-mode="square">Square</button>
                    </div>
                </div>
                <div class="control-group">
                    <button type="button" class="action-btn resetBtn">Reset</button>
                    <button type="button" class="action-btn backBtn">Back</button>
                    <button type="button" class="action-btn bgRemovelToggle" style="display: none;">Remove Background</button>
                    <button type="button" class="action-btn primary cropBtn" style="display: none;">Crop & Save</button>
                </div>
                <div class="control-group">
                    <label>Zoom:</label>
                    <div class="zoom-controls">
                        <button type="button" class="zoom-btn zoomOut">-</button>
                        <span class="zoom-level zoomLevel">100%</span>
                        <button type="button" class="zoom-btn zoomIn">+</button>
                        <input type="range" class="zoom-slider zoomSlider" min="20" max="300" value="100">
                    </div>
                </div>
            </div>
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div class="image-container">
                    <img class="previewImage" src="" alt="Preview">
                    <div class="crop-overlay">
                        <div class="crop-selection"></div>
                    </div>
                </div>
                <div class="preview-section">
                    <h4>Cropped Preview</h4>
                    <canvas class="previewCanvas" name="previewCanvas"></canvas>
                </div>
            </div>
        </section>
    </div>

                                                    </div>
													<div class="col-md-6">
                                                    </div>
                                                    <div class="col-md-6">
													    <div class="form-group row">
														   <label class="col-md-3 label-control"></label>
														   <!-- <div class="col-md-9 mx-auto">
														   <?php if($affiliateImagePath!=''){?>
														   <img id="profileimg" class="rounded-circle" height="250" width="250" src="<?php echo ($affiliateImagePath);?>">
														   <?php }?>
														</div> -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-actions text-right">
											    <a href="dashboard.html">
													<button type="button" class="btn btn-outline-warning btn-glow mr-1">
														<i class="ft-x"></i> Cancel
													</button>
												</a>
                                                <button type="submit" class="btn btn-outline-primary btn-glow">
                                                    <i class="la la-check-square-o"></i> <?php echo ($buttonTitle);?>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
    <!-- END: Content-->
   
    <!-- BEGIN: Footer-->
    <?php include('includes/footer.php'); ?> 
    <?php include('includes/footerJs.php'); ?> 
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/custom.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/js/scripts/forms/select/form-select2.js"></script>	
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/forms/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/js/scripts/forms/extended/form-inputmask.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/cropper.js" type="text/javascript"></script>

	 <script>
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
        
		  const coverCropper =	  new ImageCropper('.cropper-instance-1');


    
            
            // Check if default school image exists and display it
            const defaultImagePath = '<?php echo $affiliateImagePath; ?>';

            console.log("Default image path:", defaultImagePath);
            // console.log("profileImageFilePath image path:", profileImageFilePath);
            
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('.cropper-instance-1 .upload-area');
                const $uploadContent = $('.cropper-instance-1 .upload-content');
                
                console.log("Upload area element exists:", $uploadArea.length > 0);
                console.log("Upload content element exists:", $uploadContent.length > 0);
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
                
                // Create the image element first to check if it loads
                const imgElement = document.createElement('img');
                imgElement.onload = function() {
                    console.log("Image loaded successfully");
                };
                imgElement.onerror = function() {
                    console.error("Failed to load image from path:", defaultImagePath);
                };
                imgElement.src = defaultImagePath + "?randId=" + new Date().getTime();
                imgElement.alt = "School Logo Preview";
                imgElement.style = "max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #667eea; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-top: 10px !important;";
                
                // Update the content with the existing image
                $uploadContent.empty().append(
                    $('<div>').css({
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '10px'
                    }).append(
                        imgElement,
                        $('<div>').css({
                            textAlign: 'center'
                        }).append(
                            $('<p>').css({
                                color: 'green',
                                fontWeight: 'bold',
                                margin: '5px 0',
                                fontSize: '14px'
                            }).text('✓ Current image'),
                            $('<span>').addClass('file-types').css({
                                color: '#666',
                                fontSize: '12px'
                            }).text('Click to select a different image if needed')
                        )
                    )
                );
            } 
			
        });
  
  </script>

    <script>
	    //form validation
		$('#employeeForm').parsley().on('field:validated', function() 
		{
			var ok = $('.parsley-error').length === 0;
        })
		.on('form:submit', function() 
		{												  
			return true; // Don't submit form for this demo
		});

		//browse image
		function browseProfileImage(input) {
			const fileSize = input.files[0].size; // in MiB

			if (input.files && input.files[0]) {
				var reader = new FileReader();
				reader.onload = function(e) {
					$('#profileimg').attr('src', e.target.result);
				};
				reader.readAsDataURL(input.files[0]);
			}

		}
		
		//country dropdown code
		$('#employeeForm').cascadingDropdown({
			selectBoxes:[
				{
					selector: '.step1',
					selected: '<?php echo($dbCountryId); ?>'
				}, 
				{
					selector: '.step2',
					selected: '<?php echo($stateId); ?>',
					requires: ['.step1'],
					requireAll: true,
					source: function(request, response) {

						$.getJSON('../ajax/getStates.html', request, function(data) {
							response($.map(data, function(item, index) {
								return {
									label: item['StateName'],
									value: item['StateId']
								};
							}));
						});
					}
				}
			]
        });
	    <?php			
			if($dbCountryId==0)
			{
				?>
					$('#cboCountry').val('224').trigger('change');
				<?php
			}
        ?>
    </script>

</body>
<!-- END: Body-->

</html>