<?php
    //include classes and config file here
	include('./includes/validatebusinesslogin.php');
	include('../includes/config.php');
	include('../includes/commonfun.php');
	include('../class/clsDB.php');
	include('../class/clsBusiness.php');
	include('../class/clsCountryStateMaster.php');
	include('../class/clsBusinessPlanMaster.php');
	
	//variables
	$employeeData = '';
	$subCardLimitr = 0;
	$businessId = $_SESSION["loggedBusinessId"];
	$loggedEmployeeId=$_SESSION["loggedEmployeeId"];

			
	
	//object
	$objBusiness = new clsBusiness();
	$objBusinessPlanMaster= new clsBusinessPlanMaster();
	
	//***** GET BUSINESS PLAN DETAILS *****//
	$businessPlans = $objBusinessPlanMaster->GetBusinessPlanDetails($businessId,0);	
	if($businessPlans!='')
	{
	  	$subCardLimitr = stripslashes($businessPlans['subCardLimit']);	
	}
	
	//get employee data
	$employeeData = $objBusiness->GetAllBusinessEmployees($status = -1,$businessId);
	

	$objDB= new clsDB();
    $subcardlimit = $objDB-> GetSingleColumnValueFromTable('businessplansubscriptiondetails', 'subCardLimit','businessId',$businessId);
	$empcount = $objBusiness->GetBusinessEmployeeCount($businessId);

  unset($objBusiness);



?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">
<head>
    <title>Employee</title>
    <?php include('includes/headerCss.php'); ?> 
    <?php include('includes/datatableCss.php'); ?> 
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/vendors/css/animate/animate.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH;?>/assets/vendors/css/extensions/sweetalert2.min.css">
	<style>
		@media  screen and (max-width:768px) {
			.mob-wrap{
		white-space: nowrap;
		/* display: flex; */
	}
}
	@media  screen and (max-width:991px) {
			.mob-wrap{
		white-space: nowrap;
		/* display: flex; */
	}
		}
	</style>
</head>
<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

    <!-- include header-->
    <?php include('includes/header.php'); ?>
	
    <!-- BEGIN: Content-->
    <div class="app-content content">
        <div class="content-overlay"></div>
        <div class="content-wrapper">
            <div class="content-header row">
                <div class="content-header-left col-md-6 col-12 mb-2">
                    <h3 class="content-header-title">Employee</h3>
                    <div class="row breadcrumbs-top">
                        <div class="breadcrumb-wrapper col-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.html">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item active"><a href="#">Employee</a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="content-header-right col-md-6 col-12">
                    <div class="text-right">
						<?php if($subCardLimitr==0){ ?> 
							<button id="isLimitFull" type="button"  class="btn btn-outline-primary btn-min-width btn-glow mb-1  ">Add</button>
						<?php }elseif($subcardlimit==$empcount){ ?>
							<button id="isSubcardLimitFull" type="button"  class="btn btn-outline-primary btn-min-width btn-glow mb-1  ">Add</button>
						<?php } else { ?>
							<a href="allemployeepermissionDtls.html">
								<button type="button" class="btn btn-outline-warning btn-min-width btn-glow mb-1">Permissions</button>
							</a>
							<a href="addemployee.html">                          
						   		<button type="button"  class="btn btn-outline-primary btn-min-width btn-glow mb-1">Add</button>
					   		</a>
						<?php } ?>
					</div>
                </div>
            </div>
			<!--- show status messages --->
			<?php if($subcardlimit==$empcount){ ?>
					<div class="alert alert-success alert-dismissible mt-2" role="alert">
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
						<strong> Subscription Plan Limit Is Reached. Please Upgrade Your Subscription to Continue.</strong>
					</div>
			<?php  }?>		
			<?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] == "added")
					{
						?>											
						<div class="alert alert-success alert-dismissible mt-2" role="alert">
							<button type="button" class="close" data-dismiss="alert" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
							<strong>Added.</strong>
						</div>
						 
					<?php 
					}
					else if($_GET["status"] == "updated")
					{
						?>											 
						<div class="alert alert-success alert-dismissible mt-2" role="alert">
							<button type="button" class="close" data-dismiss="alert" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
							<strong>Updated.</strong>
						</div>
					<?php 
					}
					else if($_GET["status"] == "limitover")
					{
						?>											 
						<div class="alert alert-success alert-dismissible mt-2" role="alert">
							<button type="button" class="close" data-dismiss="alert" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
							<strong>Subscription Plan Limit Is Reached. Please Upgrade Your Subscription to Continue.</strong>
						</div>
					<?php 
					}
				}
			?>
            <div class="content-body">
                
                <!-- Default ordering table -->
                <section id="ordering">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">View</h4>
                                    <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                    <div class="heading-elements">
                                        <ul class="list-inline mb-0">                                            
                                            <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                            <li><a data-action="expand"><i class="ft-maximize"></i></a></li>                                           
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-content collapse show">
                                    <div class="card-body card-dashboard">                                        
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered default-ordering" id="employeeTable">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Address</th>
                                                        <th>Contact Info</th>
                                                        <th class="text-center">Status</th>
                                                        <th class="text-center">Action</th>                                                     
                                                    </tr>
                                                </thead>
                                                <tbody>
												    <?php						  
														if($employeeData!='')
														{
														    $objCountryStateMaster = new clsCountryStateMaster();
															while($row = mysqli_fetch_assoc($employeeData))
															{
															    $employeeId = $row['employeeId']; 															  
															    $businessId = $row['businessId']; 															  
															    $firstName = $row['firstName']; 
															    $lastName = ($row['lastName']);
															    $email = stripslashes($row['email']);
															    $address = ($row['address']);									   
															    $address2 = ($row['address2']);									   
															    $status = ($row['status']);		
															    $isPrimary = ($row['isPrimary']);		
															    $phoneNo = ($row['phoneNo']);		
															    $city = ($row['city']);		
															    $zipCode = ($row['zipCode']);		
															    $stateId = ($row['stateId']);		
                                                 				
																//get state name
																$state  = $objCountryStateMaster->GetLocationName($stateId);
																$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
																$country = $objCountryStateMaster->GetLocationName($dbCountryId);
																
																$contactInfo = "";
							
																if($phoneNo!=''){ $contactInfo .= '<b>Phone No :</b> '.$phoneNo;}
																if($email!=''){ $contactInfo .= '<br><b>Email :</b> '.$email;}
																
																$fullAddress='';
																if($address!=''){ $fullAddress .= $address.' '.$address2.',';}
																if($city!=''){ $fullAddress .= '<br> '.$city.',';}
																if($zipCode!=''){ $fullAddress .= '&nbsp  '.$zipCode;}
																if($state!=''){ $fullAddress .= '<br> '.$state;}
																if($country!=''){ $fullAddress .= ' - '.$country;} 																
																
															  	//status
																if($status==0)
																{
																	$currntStatus = 'Inactive';
																	$btnClass = "btn btn-sm btn-outline-secondary btn-glow";
																}
																else
																{
																	$currntStatus = 'Active';
																	$btnClass = "btn btn-sm btn-outline-success btn-glow";
																} 								   
													?>
                                                    <tr>
                                                        <td><?php echo ($firstName.' '.$lastName);?></td>                                                       
                                                        <td><?php echo ($fullAddress);?></td>                                                       
                                                        <td><?php echo ($contactInfo);?></td>
														<td class="text-center">
														   <a href="javascript:void(0)" id="isActive_<?php echo ($employeeId);?>" class="isStatus <?php echo ($btnClass);?>" isStatus="<?php echo ($status);?>"  employeeId="<?php echo ($employeeId);?>" ><?php echo $currntStatus;?> </a>
														</td>														
                                                        <td class="text-center mob-wrap">
														   <a href="permission.html?id=<?php echo EncodeQueryData($employeeId);?>" class="btn btn-sm btn-outline-warning btn-glow mr-1 mb-1">Permissions</a>										   
														   <a href="javascript:void(0)" class="sendLoginDeatils btn btn-sm btn-outline-primary btn-glow mr-1 mb-1" employeeId="<?php echo EncodeQueryData($employeeId);?>"  businessLogo="<?php echo $loggedBusinessLogo; ?>">Send Email</a>
														   <a href="addemployee.html?id=<?php echo EncodeQueryData($employeeId);?>" class="btn btn-sm btn-outline-success btn-glow mr-1 mb-1">Edit</a>														   
														   <a href="javascript:void(0)" class="<?php if($isPrimary == 0){?> deleteAjaxRow <?php }else{?> <?php }?> btn btn-sm btn-outline-danger  btn-glow mr-1 mb-1" employeeId="<?php echo EncodeQueryData($employeeId);?>" businessId="<?php echo EncodeQueryData($businessId);?>">Delete</a>
														</td>                                                   
                                                    </tr>
                                                    <?php
														    } 
														}
													?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!--/ Default ordering table -->
                

            </div>
        </div>
    </div>
    <!-- END: Content-->
   
    <!-- BEGIN: Footer-->
    <?php include('includes/footer.php'); ?> 
    <?php include('includes/footerJs.php'); ?> 
    <?php include('includes/datatableJs.php'); ?> 
	<script src="<?php echo BASE_PATH;?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
    <script src="<?php echo BASE_PATH;?>/assets/vendors/js/extensions/polyfill.min.js"></script>
	<script src="<?php echo BASE_PATH;?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
    <script>
	    //datatable alignment
        var current_datatable = $("#employeeTable").DataTable
		({			
			"order": [[ 1, "ASC" ]],			
			"language": {
			    // "info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
		    "aoColumns": [ 						
				{"sWidth": "20%", "bSortable": true},		
				{"sWidth": "20%", "bSortable": true},
				{"sWidth": "25%", "bSortable": true},
				{"sWidth": "10%", "bSortable": false},
				{"sWidth": "25%", "bSortable": false}
			],
		});
		
		//delete
	    // $('.deleteAjaxRow').on('click', function () 
		$("#employeeTable tbody").on("click", ".deleteAjaxRow", function()
	    {
		    var current_datatable_row = current_datatable.row($(this).parents('tr'));	
			var employeeId = $(this).attr('employeeId'); 
			var businessId = $(this).attr('businessId'); 
			
			Swal.fire({
			  title: 'Confirmation',
			  text: "Continue with delete?",
			  type: 'warning',
			  showCancelButton: true,
			  confirmButtonColor: '#3085d6',
			  cancelButtonColor: '#d33',
			  confirmButtonText: 'Delete',
			  confirmButtonClass: 'btn btn-outline-primary btn-glow',
			  cancelButtonClass: 'btn btn-outline-danger ml-1',
			  buttonsStyling: false,
			}).then(function (result) 
			{
			    if (result.value) 
			    {				   
				    $.ajax({
						type: "GET",						
						url: "../ajax/ajax_delete_employee.html",
						data: {employeeId:employeeId,businessId:businessId},
						success:function()
						{
							current_datatable.row(current_datatable_row).remove().draw( false );
							Swal.fire({
							type: "success",
							title: 'Deleted!',
							text: 'Deleted.',
							confirmButtonClass: 'btn btn-success btn-glow',
							})
						}
					});
			    }
			})
	    });
		
		
		//update status
	    $('.isStatus').on('click', function () 
	    {
		   var thisanchor = $(this);
			var employeeId = $(this).attr('employeeId');
			var isStatus = $(this).attr('isStatus');
			
			Swal.fire({
			  title: 'Confirmation',
			  text: "Continue with status change?",
			  type: 'warning',
			  showCancelButton: true,
			  confirmButtonColor: '#3085d6',
			  cancelButtonColor: '#d33',
			  confirmButtonText: 'Ok',
			  confirmButtonClass: 'btn btn-outline-primary btn-glow',
			  cancelButtonClass: 'btn btn-outline-danger btn-glow ml-1',
			  buttonsStyling: false,
			}).then(function (result) 
			{
			    if (result.value) 
			    {				   
				    $.ajax({
					type: "GET",					
					url: "../ajax/ajax_set_employee_status.html",
					data: {employeeId:employeeId,isStatus:isStatus},
					success: function() 
						{
							thisanchor.attr('isStatus',isStatus==0? 1 : 0);
							if(isStatus==0)
							{
								thisanchor.html('Active');							
								$('#isActive_'+employeeId).removeClass('btn btn-sm btn-outline-secondary btn-glow');
								$('#isActive_'+employeeId).addClass('btn btn-sm btn-outline-success btn-glow');									
							}
							else
							{								
								thisanchor.html('Inactive');							
								$('#isActive_'+employeeId).removeClass('btn btn-sm btn-outline-success btn-glow');
								$('#isActive_'+employeeId).addClass('btn btn-sm btn-outline-secondary btn-glow');
							}
							Swal.fire({
							type: "success",
							title: 'Status updated!',							
							confirmButtonClass: 'btn btn-outline-success btn-glow',
							})
						}
				    });
			    }
			})
	    });
		
		//send login details
	    $('.sendLoginDeatils').on('click', function () 
	    {
		    var current_datatable_row = current_datatable.row($(this).parents('tr'));	
			var employeeId = $(this).attr('employeeId'); 
			var businessLogo = $(this).attr('businessLogo'); 
			var loggedEmployeeId = '<?php echo $loggedEmployeeId; ?>'; 
			
			Swal.fire({
			  title: 'Confirmation',
			  text: "Continue with send login details?",
			  type: 'warning',
			  showCancelButton: true,
			  confirmButtonColor: '#3085d6',
			  cancelButtonColor: '#d33',
			  confirmButtonText: 'Ok',
			  confirmButtonClass: 'btn btn-outline-primary btn-glow',
			  cancelButtonClass: 'btn btn-outline-danger ml-1',
			  buttonsStyling: false,
			}).then(function (result) 
			{
			    if (result.value) 
			    {				   
				    $.ajax({
						type: "GET",						
						url: "../ajax/ajax_send_employee_login_details.html",
						data: {employeeId:employeeId,loggedEmployeeId:loggedEmployeeId,businessLogo:businessLogo},
						dataType: "json",
						success:function(data)
						{
							if(data == 0)
							{
								Swal.fire({
								type: "warning",
								title: 'Insufficient Email Balance!',										
								confirmButtonClass: 'btn btn-warning',
								})
							}
							else if(data == 1)
							{
								Swal.fire({
								type: "success",
								title: 'Sent successfully.',							
								confirmButtonClass: 'btn btn-outline-success btn-glow',
								})

							}

							
						}
					});
			    }
			})
	    });
		

		//Is Limit Full
		$('#isLimitFull').on('click', function () 
	    {
			Swal.fire('Item is not in your Subscription Plan. Please upgrade your subscription to continue.')
		});

		//Subcard Limit Full
		$('#isSubcardLimitFull').on('click', function () 
	    {
			Swal.fire('Subscription Plan Limit Is Reached. Please Upgrade Your Subscription to Continue.')
		});
    </script>

</body>
<!-- END: Body-->

</html>