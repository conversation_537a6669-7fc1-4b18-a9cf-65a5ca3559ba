<?php
session_start();

$sessionCount = count($_SESSION);

// header('X-Frame-Options: DENY');
include('includes/config.php');
include('includes/commonfun.php');
include('includes/visitor_tracking.php');
include('class/clsDB.php');
include('class/clsBusiness.php');
include('class/clsTemplateEngine.php');
include('class/clsPages.php');
include('class/clsOtherProducts.php');
include('class/clsProBizCaPreview.php');
include('class/clsBusinessProBizCa.php');
include('class/clsCountryStateMaster.php');
include('class/clsThemeMaster.php');
include('class/clsBusinessPlanMaster.php');
include('class/clsTemplateCustomSettings.php');
include('class/clsCustomer.php');
include('class/clsEmployee.php');
include('class/clsAffiliate.php');
include('class/clsBusinessProBizCaForms.php');

//Check Org And Page Slug 
//----------------------------
$orgslug = '';
$orgName = '';
$displayName = '';
$currentDomainName = '';
$ownerName = '';
$orgPhone = '';
$orgEmail = '';
$pageslug = '';
$themeColor = '';
$displayURLTag = '';
$shareType = '4';
$dynamicOrgUrl = BASE_PATH;
$shareDetails = '';
$customerId = '0';
$isPopUp = '';
$businessOwnerName = '';
$probizcaLogo = GetBusinessWhiteLabelLogo();
$rightArrowImagePath = BASE_PATH . '/assets/images/image_right_arrow.png';
$sharedMessage = '';
$refType = 0;
$refId = '';
$isPrimary = 0;
$newUserId = 0;
$qrflag = 0;


if (isset($_SESSION['scustomerId'])) {
	$loggrdcustomerId = $_SESSION['scustomerId'];
} else {
	$loggrdcustomerId = '';
}

if (isset($_SESSION['loggedEmployeeId'])) {
	$semployeeId = $_SESSION['loggedEmployeeId'];
} else {
	$semployeeId = '';
}

if (isset($_SESSION['loggedAffiliateId'])) {
	$loggedAffiliateId = $_SESSION["loggedAffiliateId"];
} else {
	$loggedAffiliateId = '';
}


if (isset($_SESSION['loggedEmployeeId'])) {
	$loggedUserId = $_SESSION["loggedEmployeeId"];
} elseif (isset($_SESSION['loggedAffiliateId'])) {
	$loggedUserId = $_SESSION["loggedAffiliateId"];
} else {
	$loggedUserId = 0;
}

//Is Employee is Owner.
if (isset($_SESSION['isPrimary'])) {
	$isPrimary = $_SESSION['isPrimary'];
}
if (isset($_GET['orgslug'])) {
	$orgslug = $_GET['orgslug'];
}
$previewtemplateId = 0;
if (isset($_GET['templateId'])) {
	$previewtemplateId = DecodeQueryData($_GET['templateId']);
}

$scustomerId = 0;
if (isset($_GET['shareType']) && isset($_GET['qrcode'])) {
	$shareType = DecodeQueryData($_GET['shareType']);
	$customerId = DecodeQueryData($_GET['qrcode']);
	$scustomerId = $customerId;
}

if (isset($_GET['refType'])) {
	$newUserId = DecodeQueryData($_GET['refType']);
	// echo "refType -> ".$newUserId; exit;

}

if ($shareType == 'A') {
	$refType = 3;
	$newSecureCode = 0;

	if ($newUserId > 0) {
		$refId = $newUserId;
		$newSecureCode = $refId;
	} else {
		$refId = $scustomerId;
	}
} else if ($shareType == 'E') {
	$objEmployee = new clsEmployee;
	$employeeRows = $objEmployee->GetEmployeeDetails($scustomerId);
	$isIndependent = $employeeRows['isIndependent'];
	$isPrimary = 0;
	if ($isIndependent == 1) {
		$refType = 5;
	} else {
		$refType = 2;
	}
	$newSecureCode = 0;

	if ($newUserId > 0) {
		$refId = $newUserId;
		$newSecureCode = $refId;
	} else {
		$refId = $scustomerId;
	}
	// $refId = $scustomerId;

} else {
	$newSecureCode = 0;
	$refType = 0;
	if ($newUserId > 0) {
		$refId = $newUserId;
		$newSecureCode = $refId;
	} else {
		$refId = $scustomerId;
	}
}
// echo "refId ".$refId;
// echo "<br>";
// echo "newSecureCode ".$newSecureCode;exit;

if (isset($_GET['isPrimary'])) {
	$isPrimary = DecodeQueryData($_GET['isPrimary']);
	$qrflag = $isPrimary;
}

//Validate Org
if ($orgslug == '') {
	//Include 404 Template  
	//----------------------------------
	$objTemplateEngine = new clsTemplateEngine(0, '', '', '', $shareType, $scustomerId, $semployeeId, $isPrimary, $previewtemplateId);
	$loadTemplateName = 'common/template/404.html';
	echo $objTemplateEngine->render($loadTemplateName);
	unset($objTemplateEngine);
	exit;
	//----------------------------------
}
//Read business
//----------------------------
$loadPageNo = 1;
$employeeId = 1;
$dynamicePageTitle = '';
$currentBusinessId = 0;
$currentOrganizationDisplayname = '';
$currentOrganizationGoogleAnalyticsTrackingID = '';
$currentOrganizationIsActivateDomain = 0;
$currentOrganizationDomain = '';
$templateFolderName = '';
$themeName = '';
$previewURL = '';
$objBusiness = new clsBusiness();

$objCountryStateMaster = new clsCountryStateMaster();
$rowsStates = $objCountryStateMaster->GetAllStates($CountryId = '224');
unset($objCountryStateMaster);

$currentBusinessRow =  $objBusiness->GetBusinessDetailsBySlug($orgslug);

//unset($objBusiness);
$tracking_id = '';
if ($currentBusinessRow != '') {
	$currentBusinessId = $currentBusinessRow['businessId'];
	$currentBusinessId = $currentBusinessRow['businessId'];
	$templateId = $previewtemplateId != 0 ? $previewtemplateId : $currentBusinessRow['templateId'];
	$templateType = $previewtemplateId != 0 && $previewtemplateId != 1 ? '' : $currentBusinessRow['templateType'];
	$displayURLTag = $currentBusinessRow['displayURLTag'];
	$displayName = $currentBusinessRow['displayName'];
	$status = $currentBusinessRow['status'];

	$tracking_id = $currentBusinessRow['tracking_id'];

	$CurrentBuinessLogo  = GetBusinessImagePath($currentBusinessRow['businessId'], $currentBusinessRow['logoName']);

	if ($status == 0) {

		//Include 404 Template  
		//----------------------------------
		$objTemplateEngine = new clsTemplateEngine(0, '', '', '', $shareType, $scustomerId, $semployeeId, $isPrimary, $previewtemplateId);
		$loadTemplateName = 'common/template/404.html';
		echo $objTemplateEngine->render($loadTemplateName);
		unset($objTemplateEngine);
		exit;
	}

	//redirect URL
	$previewURL = BASE_PATH . "/mb/" . $displayURLTag;

	// $profileImageFilePath  = GetBusinessProfileImagePath($currentBusinessId, $currentBusinessRow['profileImageName']);
	$profileImageFilePath  = GetBusinessCoverImagePath($currentBusinessId, $currentBusinessRow['coverImageName']);

	$employeeId =  $objBusiness->CheckBusinesAsAnEmployee($currentBusinessId);

	$objThemeMaster = new clsThemeMaster();
	//get template details
	if (isset($_GET['templateName']) && isset($_GET['themefile'])) {
		$templateFolderName = $_GET['templateName'];
		$themeColor = $_GET['themefile'];
	} else {
		$tempRow =  $objThemeMaster->GetTemplateDetails($templateId);
		if ($tempRow != '') {
			$templateFolderName = $tempRow['templateName'];
			$themeName = $tempRow['templateName'];
		}
	}

	$currentOrganizationDisplaynames = $templateFolderName;
	$currentOrganizationIsActivateDomain = $currentBusinessRow['website'];
	$currentOrganizationDomain = stripslashes($currentBusinessRow['website']);
	$currentOrganizationGoogleAnalyticsTrackingID =  trim(stripslashes($currentBusinessRow['website']));
} else {
	//Include 404 Template  
	//----------------------------------
	$objTemplateEngine = new clsTemplateEngine(0, '', '', '', $shareType, $scustomerId, $semployeeId, $isPrimary, $previewtemplateId);
	$loadTemplateName = 'common/template/404.html';
	echo $objTemplateEngine->render($loadTemplateName);
	unset($objTemplateEngine);
	exit;
	//----------------------------------
}
//----------------------------
//Generate Dynamic Path
//----------------------------
if (isset($_GET['templateType']) == 'C' || $templateType == 'C' && $templateId == 1) {
	// echo "Custom";
	$template_ROOT_FolderPath = ROOT_PATH . '/templates/customtemplates/' . $templateFolderName;
	$template_BASE_ASSETS_FolderPath = $dynamicOrgUrl . '/templates/customtemplates/' . $templateFolderName . '/assets/';

	$template_BASE_CSSFolderPath = $dynamicOrgUrl . '/templates/customtemplates/' . $templateFolderName . '/assets/css';
	$template_ROOT_CSSFolderPath = ROOT_PATH . '/templates/' . $templateFolderName . '/assets/css';

	$template_BASE_CSS_THEME_Path = $dynamicOrgUrl . '/templates/customtemplates/' . $templateFolderName . '/assets/themes/' . $themeName;
	$template_ROOT_CSS_THEME_Path = ROOT_PATH . '/templates/' . $templateFolderName . '/assets/themes/' . $themeName;


	$template_BASE_JSFolderPath = $dynamicOrgUrl . '/templates/customtemplates/' . $templateFolderName . '/assets/js';
	$template_ROOT_JSFolderPath = ROOT_PATH . '/templates/customtemplates/' . $templateFolderName . '/assets/js';

	$template_BASE_ImagesFolderPath = $dynamicOrgUrl . '/templates/customtemplates/' . $templateFolderName . '/assets/images';
	$template_ROOT_ImagesFolderPath = ROOT_PATH . '/templates/customtemplates/' . $templateFolderName . '/assets/images';
} else {

	// echo "Default".$templateId;
	$template_ROOT_FolderPath = ROOT_PATH . '/templates/' . $templateFolderName;
	$template_BASE_ASSETS_FolderPath = $dynamicOrgUrl . '/templates/' . $templateFolderName . '/assets/';

	$template_BASE_CSSFolderPath = $dynamicOrgUrl . '/templates/' . $templateFolderName . '/assets/css';
	$template_ROOT_CSSFolderPath = ROOT_PATH . '/templates/' . $templateFolderName . '/assets/css';

	$template_BASE_CSS_THEME_Path = $dynamicOrgUrl . '/templates/' . $templateFolderName . '/assets/themes/' . $themeName;
	$template_ROOT_CSS_THEME_Path = ROOT_PATH . '/templates/' . $templateFolderName . '/assets/themes/' . $themeName;

	$template_BASE_JSFolderPath = $dynamicOrgUrl . '/templates/' . $templateFolderName . '/assets/js';
	$template_ROOT_JSFolderPath = ROOT_PATH . '/templates/' . $templateFolderName . '/assets/js';

	$template_BASE_ImagesFolderPath = $dynamicOrgUrl . '/templates/' . $templateFolderName . '/assets/images';
	$template_ROOT_ImagesFolderPath = ROOT_PATH . '/templates/' . $templateFolderName . '/assets/images';
}

//----------------------------
// echo $shareType;exit;
//Load Template Engine
//----------------------------
$objTemplateEngine = new clsTemplateEngine($currentBusinessId, $dynamicOrgUrl, $loadPageNo, $themeColor, $shareType, $scustomerId, $newUserId, $isPrimary, $previewtemplateId);
$objTemplateEngine->template_assets_folder_path = $template_BASE_ASSETS_FolderPath;
$objTemplateEngine->currentOrgDetails =  array("CurrentOrgBasePath" => $objTemplateEngine->org_base_path);


// Custom QR Form Generator
$customQrFormchk = $objTemplateEngine->customQrFormchk;
$org_BusinessQrForms = $objTemplateEngine->org_BusinessQrForms;

$customerCount = $objTemplateEngine->customerCount;
$contactLimit = $objTemplateEngine->contactLimit;




// echo "<pre>";
// print_r($objTemplateEngine);exit;
//comment code
// $returnPageDataArray  = $objTemplateEngine->setCurrentPageSlug($pageslug);
$currentPageTitle = ''; //$returnPageDataArray['PageTitle'];
$isDefaultPage = ''; //$returnPageDataArray['IsDefaultPage'];
$currentPageLargeDescription = ''; //$returnPageDataArray['PageLargeContent'];
$currentPageMetaKeyword = ''; //$returnPageDataArray['PageMetaKeyword'];
$currentPageMetaDescription = ''; //$returnPageDataArray['PageMetaDescription'];
$currentPageBannerImagePath = ''; //$returnPageDataArray['PageBannerImagePath'];

$row =  $objBusiness->GetBusinessEmployeeDetails($currentBusinessId);
if ($row != '') {
	$employeeId  = stripslashes($row['employeeId']);
	$firstName  = stripslashes($row['firstName']);
	$lastName  = stripslashes($row['lastName']);
	$imageName  = stripslashes($row['imageName']);

	//business owner name
	$businessOwnerName = $firstName . ' ' . $lastName;
}

$returnOrgData = $objBusiness->GetBusinessDetails($currentBusinessId);
if ($returnOrgData) {

	$orgName = $returnOrgData['businessName'];
	$displayName = $returnOrgData['profileTitle'];
	$sharedMessage = $returnOrgData['sharedMessage'];
}


if ($shareType == 'A') {



	$objAffiliate = new clsAffiliate();

	$row = $objAffiliate->GetAffiliateDetails($scustomerId);
	unset($objAffiliate);

	$vcardfirstName  = stripslashes($row['firstName']);
	$vcardlastName  = stripslashes($row['lastName']);
	$vcardEmail  = stripslashes($row['email']);
	$vcardPhoneNo  = stripslashes($row['phoneNo']);
	$stateId = $currentBusinessRow['stateId'];

	$objCountryStateMaster = new clsCountryStateMaster();
	$state  = $objCountryStateMaster->GetLocationName($stateId);
	$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
	$country = $objCountryStateMaster->GetLocationName($dbCountryId);

	$vcardAddress = $currentBusinessRow['address'] . ' ';
	$vcardAddress2 = $currentBusinessRow['address2'] . ' ';
	$vcardCity = $currentBusinessRow['city'] . ' ';
	$vcardState = $state . ' ';
	$vcardZipeCode = $currentBusinessRow['zipCode'] . ' ';
	$vcardCountry = $country;


	$vcardDisplayName = $vcardfirstName . $vcardlastName;

	$returnOrgData = $objBusiness->GetBusinessDetails($currentBusinessId);
	if ($returnOrgData) {
		$vcardCompanyName = $returnOrgData['businessName'];
	}
} elseif ($shareType == 'E') {

	$objEmployee = new clsEmployee();
	//get employee details
	$row = $objEmployee->GetEmployeeDetails($scustomerId);
	unset($objEmployee);

	$vcardfirstName  = stripslashes($row['firstName']);
	$vcardlastName  = stripslashes($row['lastName']);
	$vcardEmail  = stripslashes($row['email']);
	$vcardPhoneNo  = stripslashes($row['phoneNo']);
	$stateId = $currentBusinessRow['stateId'];

	$objCountryStateMaster = new clsCountryStateMaster();
	$state  = $objCountryStateMaster->GetLocationName($stateId);
	$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
	$country = $objCountryStateMaster->GetLocationName($dbCountryId);

	$vcardAddress = $currentBusinessRow['address'] . ' ';
	$vcardAddress2 = $currentBusinessRow['address2'] . ' ';
	$vcardCity = $currentBusinessRow['city'] . ' ';
	$vcardState = $state . ' ';
	$vcardZipeCode = $currentBusinessRow['zipCode'] . ' ';
	$vcardCountry = $country;


	$vcardDisplayName = $vcardfirstName . $vcardlastName;

	$returnOrgData = $objBusiness->GetBusinessDetails($currentBusinessId);
	if ($returnOrgData) {
		$vcardCompanyName = $returnOrgData['businessName'];
	}
} else {


	if ($returnOrgData) {




		$vcardEmail = $currentBusinessRow['email'];
		$vcardPhoneNo = $currentBusinessRow['phoneNo'];
		$stateId = $currentBusinessRow['stateId'];

		$objCountryStateMaster = new clsCountryStateMaster();
		$state  = $objCountryStateMaster->GetLocationName($stateId);
		$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
		$country = $objCountryStateMaster->GetLocationName($dbCountryId);

		$vcardAddress = $currentBusinessRow['address'] . ' ';
		$vcardAddress2 = $currentBusinessRow['address2'] . ' ';
		$vcardCity = $currentBusinessRow['city'] . ' ';
		$vcardState = $state . ' ';
		$vcardZipeCode = $currentBusinessRow['zipCode'] . ' ';
		$vcardCountry = $country;


		$vcardlogo = $profileImageFilePath;

		$vcardDisplayName = $businessOwnerName;
		$vcardfirstName = $firstName;
		$vcardlastName = $lastName;

		$vcardCompanyName = $orgName;
	}
}

$context = stream_context_create([
	'http' => [
		'header' => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
	],
]);

if ($currentBusinessRow['coverImageName'] != '') {

	$businessLogo  = ROOT_PATH . "/upload/business/" . $currentBusinessId . "/probizca/coverimage/" . $currentBusinessRow['coverImageName'];
	if (file_exists($businessLogo)) {
		$img = file_get_contents($businessLogo, false, $context);

		$vcardbusinessLogoext = pathinfo($currentBusinessRow['coverImageName'], PATHINFO_EXTENSION);

		$vcardbusinessLogo = base64_encode($img);
	} else {
		$businessLogo  = ROOT_PATH . "/templates/SystemDefault/assets/images/defaultlogo.png";

		$img = file_get_contents($businessLogo, false, $context);

		$vcardbusinessLogoext = pathinfo($currentBusinessRow['coverImageName'], PATHINFO_EXTENSION);

		$vcardbusinessLogo = base64_encode($img);
	}
} else {
	// echo "else";
	$businessLogo  = ROOT_PATH . "/templates/SystemDefault/assets/images/defaultlogo.png";

	$img = file_get_contents($businessLogo, false, $context);

	$vcardbusinessLogoext = pathinfo($currentBusinessRow['coverImageName'], PATHINFO_EXTENSION);

	$vcardbusinessLogo = base64_encode($img);
}
// echo $vcardbusinessLogo;exit;
$isAuthVerified = 1;
if($newSecureCode > 0)
{

	$objCustomer = new clsCustomer();
	$customerDetail = $objCustomer->GetCustomerDetails($newSecureCode, $currentBusinessId);
	if ($customerDetail != '') {
		
		$customerMobileNumber = isset($customerDetail['phoneNo']) ? $customerDetail['phoneNo'] : '';
		$customerAddedType = isset($customerDetail['shareType']) ? $customerDetail['shareType'] : '';
		if ($customerAddedType  == 2) {
			$isAuthVerified = isset($customerDetail['isAuthVerified']) ? $customerDetail['isAuthVerified'] : 0;
		} else {
			$isAuthVerified = 1;
		}
	} else {
		$loadTemplateName = 'common/template/404.html';
		echo $objTemplateEngine->render($loadTemplateName);
		unset($objTemplateEngine);
		exit;
	}
	
}
// echo $isAuthVerified;exit;

//***** CREATE JSON FILE DATA FOR ADD TO HOMESCREEN ******//
file_put_contents('manifest.json', $json_data = '');
//redirect URL
$URL = '';
$isAddToHome = 1;

if ($shareType == 'A') {
	$urlRefType = 3;
	$URL = $previewURL . '/' . EncodeQueryData($shareType) . '/' . EncodeQueryData($scustomerId) . '/' . EncodeQueryData($newSecureCode);
	// 	echo $URL;
} else if ($shareType == 'E') {
	$urlRefType = 2;
	$URL = $previewURL . '/' . EncodeQueryData($shareType) . '/' . EncodeQueryData($scustomerId) . '/' . EncodeQueryData($newSecureCode);
} else {
	$urlRefType = 1;
	$URL = $previewURL . '/' . EncodeQueryData($shareType) . '/' . EncodeQueryData($scustomerId) . '/' . EncodeQueryData($newSecureCode);
}

$json_data = json_encode([
	"short_name" => $orgName,
	"name" => $orgName,
	"theme_color" => "#4A90E2",
	"background_color" => "#F7F8F9",
	"display" => "standalone",
	"icons" => [
		[
			"src" => $CurrentBuinessLogo,
			"sizes" => "192x192",
			"type" => "image/png"
		]
	],
	"scope" => "/mb/",
	"start_url" => $URL
]);

file_put_contents('manifest.json', $json_data);
//----------------------------

//Check Page Type
//----------------------------
$isProductsTemplate = 0;
$isProductListTemplate = 0;
$isProductViewTemplate = 0;
$isOtherProductListTemplate = 0;
$isNewsTemplate = 0;
$isTestimonialTemplate = 0;
$isGalleryTemplate = 0;
$isNewsViewTemplate = 0;
$isAboutUsTemplate = 0;
$isContactTemplate = 0;
$isJoinLICTemplate = 0;

//load page by slug
$currentSEOPage =  'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

$profileHeadTitle = '<h1>' . (($orgName) . '</h1><br><h5>' . ($businessOwnerName) . '</h5><br><h5>' . ($displayName)) . '<h5>';
// echo $scustomerId;exit;

if (isset($_GET['isSaveVcard'])) {
	require_once "VcardExport.php";
	$vcardExport = new VcardExport();
	$vcardExport->contactVcardExportService($vcardlogo, $vcardEmail, $vcardPhoneNo, $vcardAddress, $vcardDisplayName);
	// 		exit;
}

$loaderImg = BASE_PATH . "/assets/images/logo/logo.png";
?>
<!DOCTYPE html>
<html lang="en-US" id="html" prefix="og: http://ogp.me/ns# fb: http://ogp.me/ns/fb# article: http://ogp.me/ns/article#">

<head>

	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0">
	<link rel="profile" href="http://gmpg.org/xfn/11">
	<link rel="pingback" href="">
	<title>
		<?php echo ($orgName); ?> |
		<?php echo $businessOwnerName; ?> |
		<?php echo ($displayName); ?>
	</title>
	<meta name='mobile-web-app-capable' content='yes'>
	<meta name="robots" content="index,follow" />
	<meta property="og:type" content="website" />
	<meta property="og:title" content="<?php echo ($businessOwnerName); ?> | <?php echo $orgName; ?> | <?php echo ($displayName); ?>" />
	<?php $msg = 'We create quality solutions with a lasting impact in website design, application development, website maintenance and live streaming.'; ?>
	<meta property="og:description" content="<?php if ($sharedMessage != '') {
													echo strip_tags($sharedMessage);
												} else {
													echo $msg;
												} ?>" />
	<meta property="og:url" content="https://www.probizca.net" />
	<meta property="og:image" itemprop="image" content="<?php echo $profileImageFilePath; ?>" />
	<meta property="og:site_name" content="https://www.probizca.net" />
	<meta name="twitter:card" content="summary" />
	<link rel="shortcut icon" type="image/x-icon" href="<?php echo BASE_PATH; ?>/assets/images/icons/logo-512.png">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/assets/js/scripts/magnificpopup/magnific-popup.css">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/customtemplates/0001/assets/css/slick.css">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/customtemplates/0001/assets/css/slick-theme.css">
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/common/js/jquery.min.js"></script>

	<link rel="icon" type="image/png" sizes="192x192" href="<?php echo BASE_PATH; ?>/assets/images/icons/android-icon-192x192.png">
	<link rel="icon" type="image/png" sizes="32x32" href="<?php echo BASE_PATH; ?>/assets/images/icons/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="96x96" href="<?php echo BASE_PATH; ?>/assets/images/icons/favicon-96x96.png">
	<link rel="icon" type="image/png" sizes="16x16" href="<?php echo BASE_PATH; ?>/assets/images/icons/favicon-16x16.png">
	<link rel="manifest" href="/manifest.json">
	<meta name="msapplication-TileColor" content="#ffffff">
	<meta name="msapplication-TileImage" content="<?php echo BASE_PATH; ?>/assets/images/icons/ms-icon-144x144.png">
	<meta name="theme-color" content="#ffffff">

	<meta name="mobile-web-app-capable" content="yes">
	<meta name="appplication-name" content="<?php echo ($profileImageFilePath); ?>">
	<link rel="icon" sizes="32x32" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-32x32.png">
	<link rel="icon" sizes="48x48" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-48x48.png">
	<link rel="icon" sizes="96x96" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-96x96.png">
	<link rel="icon" sizes="128x128" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-128x128.png">
	<link rel="icon" sizes="144x144" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-144x144.png">
	<link rel="icon" sizes="152x152" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-152x152.png">
	<link rel="icon" sizes="192x192" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-192x192.png">
	<link rel="icon" sizes="256x256" href="<?php echo BASE_PATH; ?>/assets/images/icons/icon-256x256.png">
	<meta name="msapplication-TileImage" content="<?php echo BASE_PATH; ?>/assets/images/icons/icon-144x144.png">

	<!--- alertify css ---->
	<script src="https://cdn.ckeditor.com/ckeditor5/35.3.2/classic/ckeditor.js"></script>
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/animate/animate.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/extensions/sweetalert2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/js/summernote/dist/summernote-bs4.css" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />

	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/calendars/fullcalendar.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/calendars/daygrid.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/calendars/timegrid.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/core/colors/palette-gradient.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/calendars/fullcalendar.css">

	<!-- Range Selector -->
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/rangeselector.css">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
	<?php
	//Load Template CSS
	//----------------------------------
	if (is_dir($template_ROOT_CSSFolderPath)) {
		$dir = opendir($template_ROOT_CSSFolderPath);

		//Read Files and Sort Options
		$filenames = array();
		while ($filename = readdir($dir)) {
			if ($filename != '.' && $filename != '..') {
				$filenames[] = $filename;
			}
		}
		sort($filenames);
		for ($fileCounter = 0; $fileCounter < count($filenames); $fileCounter++) {
			$file = $filenames[$fileCounter];

			$dynamiceCSSPath = $template_BASE_CSSFolderPath . '/' . $file;
	?>

			<link href="<?php echo ($dynamiceCSSPath); ?>" type="text/css" rel="stylesheet" />
		<?php
		}
		closedir($dir);
	}
	//---------------------------------- 
	//Load Template Theme CSS
	//----------------------------------
	if (file_exists($template_ROOT_CSS_THEME_Path) && $themeName != '') {

		?>
		<link href="<?php echo ($template_BASE_CSS_THEME_Path); ?>" type="text/css" rel="stylesheet" />

	<?php
	}
	//----------------------------------
	?>
	<style>
		.close {
			font-size: 27px;
			padding-top: 10px !important;
			padding-right: 10px !important;
			cursor: pointer;
		}

		.swal2-popup .swal2-validation-message {
			display: none;
			align-items: center;
			justify-content: center;
			padding: .625em;
			background: #f0f0f0;
			color: #666;
			font-size: 1em;
			font-weight: 300;
			overflow: hidden;
			margin-top: 20px;
		}

		.btn-group-lg>.btn,
		.btn-lg {
			padding: 0.4rem 1rem;
			font-size: 1rem;
			line-height: 1.5;
			border-radius: 0.3rem;
		}

		.modal:before {
			height: 100%;
			width: 100%;
			background: #4C4C4C;
			position: fixed;
			content: '';
			left: 0;
			top: 0;
			z-index: -1;
		}

		.btn-background {
			/* background-image: linear-gradient(90deg, #5d2d88, #ecb2ff); */
			border: 1px solid;
			border-color: linear-gradient(45deg, #5d2d88, white);
			transition: 5s;
		}

		.btn-background:hover {
			border: 1px solid;
			border-color: linear-gradient(225deg, #5d2d88, white);
		}

		.btn-background:active {
			border: 1px solid !important;
			border-color: linear-gradient(45deg, #5d2d88, white) !important;
		}

		.btn-background:focus {
			box-shadow: none !important;
		}

		.btn-background i {
			width: 3rem;
		}

		.share-popup-tag {
			visibility: hidden;
			font-size: 15px;
			font-weight: 600;
		}

		@media screen and (max-width: 430px) {
			#welcomeModal {
				padding: 0;
				padding-right: 0 !important;
			}

			.modal-content {
				width: 92%;
				margin: 0 auto;
			}

			.btn-background i {
				width: 2rem;
			}

			.modal-body {
				padding: 10px 20px !important;
			}

			.modal-header {
				padding: 10px 20px !important;
			}

			.modal-header h4 {
				font-size: 21px;
			}

			.response-font-size {
				font-size: 21px;
			}

			.share-popup-tag {
				display: block;
				font-size: 13px;
				font-weight: 600;
			}
		}

		@media (max-width: 430px) and (min-width: 375px) {
			.btn-background i {
				width: 3rem;
			}
		}

		/* yogesh css */
		#mytabs {
			display: none !important;
		}

		#mytabs a {
			margin: 0 10px !important;
			display: inline-block !important;
		}

		.fade:not(.show) {
			opacity: 1 !important;
		}

		.modal-header {
			padding: 1rem 1rem 0 1rem !important;
			border-bottom: none !important;
		}

		.modal-content {
			border: none !important;
			border-radius: 20px !important;
			padding: 5px 0;
		}

		.modal-footer {
			border-top: none !important;
		}

		.modal:before {
			background: transparent;
		}

		.btn-primary,
		.btn-secondary {
			color: #fff !important;
			padding: 7px 25px !important;
			border-radius: 10px !important;
			font-size: 16px !important;
			justify-content: center;
		}

		.custom-arrow {
			padding: 2px 50px 2px 2px;
			border: none;
			background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='grey'><polygon points='0,0 100,0 50,50'/></svg>");
			background-repeat: no-repeat;
			background-position: right center;
			-webkit-appearance: none;

			background-size: 16px;
			background-position: calc(100% - 20px) 17px;
			background-repeat: no-repeat;
		}

		/* / loader css start / */
		.loader {
			/* border: 4px solid #2B67AF; */
			border-left-color: transparent;
			border-radius: 50%;
		}

		.loader {
			/* border: 4px solid #2B67AF; */
			border-left-color: transparent;
			width: 36px;
			height: 36px;
		}

		.loader {
			border: 2px solid #2B67AF;
			border-left-color: transparent;
			width: 85px;
			height: 85px;
			animation: spin89345 1s linear infinite;
		}

		.loader-container1 {
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			height: 100%;
			width: 100%;
			position: fixed;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			z-index: 9999;
			background-color: #ffffff;
			overflow: hidden;
		}

		@keyframes spin89345 {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		/* / loader css End / */
	</style>
</head>

<body oncontextmenu="return true" class="modalRomove">
	<link itemprop="thumbnailUrl" href="<?php echo $profileImageFilePath; ?>">
	<span itemprop="thumbnail" itemscope itemtype="http://schema.org/ImageObject">
		<link itemprop="url" href="<?php echo $profileImageFilePath; ?>">
	</span>

	<div class="loader-container1">
		<div class="loader"></div>
		<div style="position: absolute;width: 80px;">
			<img style="width: 100%;height:100%;" src="<?php echo $loaderImg; ?>" alt="image">
		</div>
	</div>

	<?php
	//Load Template Content
	//----------------------------------
	$loadTemplateName = '';
	$templateArray = array();
	if ($isDefaultPage == 0) {
		$loadTemplateName = $template_ROOT_FolderPath . '/home.html';
		echo $objTemplateEngine->render($loadTemplateName);
	} else {
		$loadTemplateName = $template_ROOT_FolderPath . '/subpage.html';
		echo $objTemplateEngine->render($loadTemplateName);
	}



	?>
	<input type="hidden" name="customerId" id="customerId" value="<?php echo ($newUserId); ?>">
	<input type="hidden" name="businessUserId" id="businessUserId" value="<?php echo ($scustomerId); ?>">
	<input type="hidden" name="customerMobileNumber" id="customerMobileNumber" value="<?php echo ($customerMobileNumber); ?>">
	<input type="hidden" name="currentBusinessId" id="currentBusinessId" value="<?php echo ($currentBusinessId); ?>">
	<input type="hidden" name="isPopUp" id="isPopUp" value="<?php echo ($isPopUp); ?>">
	<input type="hidden" name="hiddenMobileNumber" id="hiddenMobileNumber">
	<input type="hidden" name="tracking_id" id="tracking_id" value="<?php echo $tracking_id; ?>">

	<!-- Modal -->
	<div class="container">
		<div class="row">
			<div class="col-md-12">
				<div class="modal fade" id="startupModal" role="dialog">
					<div class="modal-dialog">
						<!-- Modal content 1-->
						<div class="modal-content">
							<div class="modal-header" style="padding:15px 33px;">
								<h5 class="justify-content-center">
									<?php echo ($displayName); ?>
								</h5>
							</div>
							<div class="modal-body" style="padding:40px 50px;">

								<form class="form form-horizontal" id="serviceForm" method="post" action="<?php echo BASE_PATH; ?>/setsessionlogin.html" enctype="multipart/form-data">
									<input type="hidden" name="slug" id="slug" value="<?php echo ($displayURLTag); ?>">
									<input type="hidden" name="businessId" id="businessId" value="<?php echo ($currentBusinessId); ?>">
									<input type="hidden" name="shareType" id="shareType" value="<?php echo ($shareType); ?>">
									<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($employeeId); ?>">

									<div class="form-group">
										<div class="row">
											<div class="col-sm-4">
												<label for="cfname"><span class="glyphicon glyphicon-eye-open"></span>
													Name:</label>
											</div>
											<div class="col-sm-8">
												<input type="text" class="form-control" id="cfname" name="cfname" placeholder="Enter Name" required>
											</div>
										</div>
									</div>
									<div class="form-group">
										<div class="row">
											<div class="col-sm-4">
												<label for="cphoneNo"><span class="glyphicon glyphicon-eye-open"></span>
													Phone Number:</label>
											</div>
											<div class="col-sm-8">
												<input type="tel" class="form-control phone" id="cphoneNo" name="cphoneNo" placeholder="Enter Phone Number">
											</div>
										</div>
									</div>
									<div class="form-group">
										<div class="row">
											<div class="col-sm-4">
												<label for="cemail"><span class="glyphicon glyphicon-eye-open"></span>
													Email:</label>
											</div>
											<div class="col-sm-8">
												<input type="email" class="form-control" id="cemail" name="cemail" placeholder="Enter Email" pattern="^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$">
											</div>
										</div>
									</div>
									<button type="submit" id="btnSave" class="btn btn-primary btn-block">
										<i class="glyphicon glyphicon-off"></i>Submit
									</button>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>



	<?php
	//Load Template JS
	//----------------------------------
	if (is_dir($template_ROOT_JSFolderPath)) {
		$dir = opendir($template_ROOT_JSFolderPath);

		//Read Files and Sort Options
		$filenames = array();
		while ($filename = readdir($dir)) {
			if ($filename != '.' && $filename != '..') {
				$filenames[] = $filename;
			}
		}

		sort($filenames);


		for ($fileCounter = 0; $fileCounter < count($filenames); $fileCounter++) {
			$file = $filenames[$fileCounter];
			$dynamiceJSPath = $template_BASE_JSFolderPath . '/' . $file;
	?>
			<script src="<?php echo ($dynamiceJSPath); ?>" type="text/javascript"></script>
	<?php
		}
		closedir($dir);
	}
	//----------------------------------
	?>

	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/moment.min.js"></script>

	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/daygrid.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/timegrid.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/interactions.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/calendar/fullcalendar.min.js"></script>

	<!-- Social Media -->
	<!--Check Jquery Exists-->
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/magnificpopup/jquery.magnific-popup.min.js"></script>
	<script type="text/javascript" src="<?php echo BASE_PATH; ?>/assets/js/input-mask/js/jquery.mask.min.js"></script>
	<script type="text/javascript" src="<?php echo BASE_PATH; ?>/assets/js/input-mask/js/jquery.maskedinput.min.js"></script>
	<!---- alertify js ---->
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/polyfill.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/summernote/dist/summernote-bs4.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/@dsvllc/summernote-image-attributes@1.0.0/summernote-image-attributes.min.js"></script>
	<!--- magnipopjs ---->
	<script type="text/javascript">
		//input mask/js/jquery
		$(".phone").mask("(*************");
		// console.clear();
		//**** ADD TO HOMESCREEN FUNCTION HERE *****//

		(function() {
			var method;
			var noop = function noop() {};
			var methods = [
				'assert', 'clear', 'count', 'debug', 'dir', 'dirxml', 'error',
				'exception', 'group', 'groupCollapsed', 'groupEnd', 'info', 'log',
				'markTimeline', 'profile', 'profileEnd', 'table', 'time', 'timeEnd',
				'timeStamp', 'trace', 'warn'
			];
			var length = methods.length;
			var console = (window.console = window.console || {});

			while (length--) {
				method = methods[length];
				console[method] = noop;
			}
		}());



		var galleryAutoScroll = 0;
		var infoAutoScroll = 0;
		var imageScroll = '';
		galleryAutoScroll = $("#galleryAutoScroll").val();
		infoAutoScroll = $("#infoAutoScroll").val();
		imageScroll = $("#imageScroll").val();
		var infoSpeed = $("#infoSpeed").val();
		var gallerySpeed = $("#gallerySpeed").val();

		//right arrow image pathinfo
		var rightImagePath = '<?php echo ($rightArrowImagePath); ?>';
		//gallery slider
		if (imageScroll == 'horizontal') {
			if (galleryAutoScroll == 1) {
				$(".vertical-center-5").slick({
					vertical: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: true,
					cssEase: 'ease',
					rtl: false,
					autoplaySpeed: gallerySpeed,
					nextArrow: '<div><i class="fa fa-chevron-right next"></i></div>',
					prevArrow: '<div><i class="fa fa-chevron-left prev"></i></div>'
				});
			} else {
				$(".vertical-center-5").slick({
					vertical: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: false,
					cssEase: 'ease',
					rtl: false,
					autoplaySpeed: gallerySpeed,
					nextArrow: '<div><i class="fa fa-chevron-right next"></i></div>',
					prevArrow: '<div><i class="fa fa-chevron-left prev"></i></div>'
				});
			}
		}
		if (imageScroll == 'vertical') {
			if (galleryAutoScroll == 1) {
				$(".vertical-center-5").slick({
					vertical: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: true,
					cssEase: 'ease',
					rtl: false,
					autoplaySpeed: gallerySpeed,
					nextArrow: '<div><i class="fa fa-chevron-up next"></i></div>',
					prevArrow: '<div><i class="fa fa-chevron-down prev"></i></div>'
				});
			} else {
				$(".vertical-center-5").slick({
					vertical: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: false,
					cssEase: 'ease',
					rtl: false,
					autoplaySpeed: gallerySpeed,
					nextArrow: '<div><i class="fa fa-chevron-up next"></i></div>',
					prevArrow: '<div><i class="fa fa-chevron-down prev"></i></div>'
				});
			}
		}

		//infographic slider
		if (imageScroll == 'horizontal') {
			if (infoAutoScroll == 1) {
				$(".vertical-center-6").slick({
					vertical: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: true,
					cssEase: 'ease',
					adaptiveHeight: true,
					rtl: false,
					autoplaySpeed: infoSpeed,
					nextArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-right next"></i></div>',
					prevArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-left prev"></i></div>'
				});
			} else {
				$(".vertical-center-6").slick({
					vertical: false,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: false,
					cssEase: 'ease',
					adaptiveHeight: true,
					rtl: false,
					autoplaySpeed: infoSpeed,
					nextArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-right next"></i></div>',
					prevArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-left prev"></i></div>'
				});
			}
		}
		if (imageScroll == 'vertical') {
			if (infoAutoScroll == 1) {
				$(".vertical-center-6").slick({
					vertical: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: true,
					cssEase: 'ease',
					adaptiveHeight: true,
					rtl: false,
					autoplaySpeed: infoSpeed,
					nextArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-up next"></i></div>',
					prevArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-down prev"></i></div>'
				});
			} else {
				$(".vertical-center-6").slick({
					vertical: true,
					slidesToShow: 1,
					slidesToScroll: 1,
					arrow: true,
					autoplay: false,
					cssEase: 'ease',
					adaptiveHeight: true,
					rtl: false,
					autoplaySpeed: infoSpeed,
					nextArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-up next"></i></div>',
					prevArrow: '<div style="margin-top:15px;"><i class="fa fa-chevron-down prev"></i></div>'
				});
			}
		}


		//onclick hide/show divs 
		$("#textDiv").hide();
		$('#isEmail').on('click', function() {
			$("#textDiv").hide();
			$("#emailDiv").show();
		});

		$('#isText').on('click', function() {
			$("#emailDiv").hide();
			$("#textDiv").show();
		});

		$(document).ready(function() {
			$('.zoom-gallery').magnificPopup({
				delegate: 'a',
				type: 'image',
				closeOnContentClick: false,
				closeBtnInside: false,
				showCloseBtn: false,
				mainClass: 'mfp-with-zoom mfp-img-mobile',
				image: {
					verticalFit: true,
					titleSrc: function(item) {
						return item.el.attr('title') + ' &middot; <a class="image-source-link" href="' + item.el.attr('data-source') + '" target="_blank">image source</a>';
					}
				},
				gallery: {
					enabled: true
				},
				zoom: {
					enabled: true,
					duration: 300, // don't foget to change the duration also in CSS
					opener: function(element) {
						return element.find('img');
					}
				}

			});

			//form open
			$('.surveyPopup').magnificPopup({
				type: 'inline',
				gallery: {
					enabled: true
				},
				preloader: false,
				// When elemened is focused, some mobile browsers in some cases zoom in
				// It looks not nice, so we disable it:
				callbacks: {
					beforeOpen: function() {
						if ($(window).width() < 700) {
							this.st.focus = false;
						} else {

						}
					}
				}
			});

			$('.surveyPopup').magnificPopup({
				type: 'ajax',
				midClick: true,
				mainClass: 'mfp-fade',
				closeOnBgClick: false
			});
			$('form input').keydown(function(e) {
				if (e.keyCode == 13) {
					e.preventDefault();
					return false;
				}
			});
		});

		$('.copyboard').on('click', function(e) {
			// alert('hello');
			$("#welcomeModal").show();
			// $("#welcomeModal").addClass('show');
			$('.modal-backdrop').addClass('show');
			$('.modal-backdrop').css({
				'display': 'block'
			})
		});

		function CopyUrl() {
			var copyText = $('#Copy').attr('data-text');
			// alert(copyText)
			var textarea = document.createElement("textarea");
			textarea.textContent = copyText;
			textarea.style.position = "fixed"; // Prevent scrolling to bottom of page in MS Edge.
			document.body.appendChild(textarea);
			textarea.select();
			document.execCommand("copy");

			document.body.removeChild(textarea);

		}
	</script>

	<?php

	// if(isset($_GET['qrcode']) && isset($_GET['refType']) &&  $customerId == -1 && isset($_GET['isPrimary']) && !(isset($_GET['isAddToHome'])))
	if (isset($_GET['shareType']) && $customerId > 0 && $newUserId > 0 && $qrflag == -1) {
	?>

		<script type="text/javascript">
			$(window).on('load', function() {
				$('#startupModalfsgssd').modal('show', {
					backdrop: 'static',
					keyboard: false
				});
				// $("#phonea").mask("(*************");
			});
		</script>

		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="modal show Contact" id="startupModalfsgssd" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
						<div class="modal-dialog">
							<?php
							$businessFormId = 0;
							if (count($org_BusinessQrForms) > 0) {
								$businessFormId = $org_BusinessQrForms[0]['businessFormId'];
							}
							?>
							<!-- Modal content 2-->
							<div class="modal-content">
								<div class="modal-header" style="padding:15px 29px !important;">
									<h5 class="justify-content-center">
										<?php echo ($orgName); ?>
									</h5>
									<?php
									$headerMsg = "Complete info below to accept invitation:";
									if ($customQrFormchk == 1 && count($org_BusinessQrForms) > 0 && $org_BusinessQrForms[0]['isActive'] == 1 && $org_BusinessQrForms[0]['isShowonQr'] == 1) {
										$headerMsg = "Complete the information below, Click on the <b>Next</b> button to continue with the process:";
									}
									?>
								</div>
								<div class="modal-body" style="padding:5px 29px 0 29px;padding-bottom: 0 !important;">
									<?php
									$businessFormId = 0;
									if (count($org_BusinessQrForms) > 0) {
										$businessFormId = $org_BusinessQrForms[0]['businessFormId'];
									}
									?>
									<form class="form form-horizontal" id="qrcodeForm" method="post" action="<?php echo BASE_PATH; ?>/contactqrcodedetailssubmit.html" enctype="multipart/form-data">
										<input type="hidden" name="shareType" id="shareType" value="<?php echo ($shareType); ?>">
										<input type="hidden" name="slug" id="slug" value="<?php echo ($previewURL); ?>">
										<input type="hidden" name="businessId" id="businessId" value="<?php echo ($currentBusinessId); ?>">
										<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($scustomerId); ?>">
										<input type="hidden" name="displayURLTag" id="displayURLTag" value="<?php echo ($displayURLTag); ?>">
										<input type="hidden" value="<?php echo $refType; ?>" name="refType" />
										<input type="hidden" value="<?php echo $refId; ?>" name="refId" />
										<input type="hidden" value="<?php //echo $qrIsPrimary;
																	?>" name="isPrimary" />

										<input type="hidden" name="businessFormId" id="businessFormId" value="<?php echo ($businessFormId); ?>">


										<div class="row justify-content-left">
											<label class="form-check-label" style="padding-left: 10px !important;">
												<?php echo ($headerMsg); ?>
											</label>
										</div><br />
										<div>
											<ul id="mytabs" class="nav nav-tabs" role="tablist">
												<li class="active">
													<a href="#first" role="tab" data-toggle="tab">
														<icon class="fa fa-home"></icon> First tab
													</a>
												</li>
												<li><a href="#second" role="tab" data-toggle="tab">
														<i class="fa fa-user"></i> Second tab
													</a>
												</li>
											</ul>
											<div class="tab-content">
												<div class="tab-pane fade active in" id="first">
													<input type="hidden" value="<?php echo $refType; ?>" name="refType" />
													<input type="hidden" value="<?php echo $refId; ?>" name="refId" />
													<input type="hidden" value="<?php //echo $qrIsPrimary;
																				?>" name="isPrimary" />
													<div class="form-group">
														<div class="row">
															<div class="col-sm-12"><label for="contactFirstName"><span class="glyphicon glyphicon-user"></span>First Name <span style="color:red;">*</span></label>
															</div>
															<div class="col-sm-12">
																<input type="text" name="contactFirstName" class="form-control" id="contactFirstName" placeholder="Enter First Name" oninput="charactersOnly(this.id);" required>
																<span id="contactFirstNameSpan" style="color: red; font-size: 12px;"></span>
															</div>
														</div>
													</div>
													<div class="form-group">
														<div class="row">
															<div class="col-sm-12"><label for="contactLastName"><span class="glyphicon glyphicon-user"></span>Last Name <span style="color:red;">*</span></label>
															</div>
															<div class="col-sm-12"> <input type="text" name="contactLastName" class="form-control" id="contactLastName" placeholder="Enter Last Name" oninput="charactersOnly(this.id);" required>
																<span id="contactLastNameSpan" style="color: red; font-size: 12px;"></span>

															</div>

														</div>
													</div>
													<div class="form-group">
														<div class="row">
															<div class="col-sm-12">
																<label for="contactemails"><span class="glyphicon glyphicon-eye-open"></span> Email <span style="color:red;">*</span></label>
															</div>
															<div class="col-sm-12">
																<input type="email" class="form-control" id="contactemails" name="contactemails" placeholder="Enter Email" pattern="^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$" required>
																<span id="contactemailSpan" style="color: red; font-size: 12px;"></span>

															</div>
														</div>
													</div>
													<div class="form-group">
														<div class="row">
															<div class="col-sm-12">
																<label for="contactphone"><span class="glyphicon glyphicon-eye-open"></span> Mobile
																	Number <span style="color:red;">*</span></label>
															</div>
															<div class="col-sm-12">
																<input type="tel" class="form-control phonea" id="phonea" name="contactphone" placeholder="Enter Mobile Number" onkeyup="myFunction();" required>
																<span id="contactphoneSpan" style="color: red; font-size: 12px;"></span>
															</div>

														</div>
													</div>
													<div class="form-group">
														<div class="row">
															<div class="col-sm-12">
																<label for="contactphone"><span class="glyphicon glyphicon-eye-open"></span> Comments </label>
															</div>
															<div class="col-sm-12">
																<textarea id="notesDescription" class="form-control " rows='5' name="notesDescription" placeholder="Enter Comments Here"></textarea>
															</div>
															<div class="col-12">
																<input style="width: 17px;height: 17px;margin: 0 5px;margin-top: 15px;" class="form-check-input" type="checkbox" value="" name="flexCheckChecked" id="flexCheckChecked" checked>
																<label style="font-size: 14px;margin-left: 35px;margin-top: 10px;" class="form-check-label">
																	By checking this box I acknowledge I am opting-in to receiving SMS Messages to <span id="phnamespan"></span> on behalf of my ProBizCa.
																</label>
																<span id="checkboxSpan" style="color: red; font-size: 12px;"></span>

															</div>
														</div>
													</div>


												</div>
												<div class="tab-pane fade" id="second">
													<?php

													if ($customQrFormchk == 1 && count($org_BusinessQrForms) > 0 && $org_BusinessQrForms[0]['isActive'] == 1 && $org_BusinessQrForms[0]['isShowonQr'] == 1) {

														if (count($org_BusinessQrForms) > 0) {
															$businessFormId = $org_BusinessQrForms[0]['businessFormId'];

															$objBusinessProBizCaForms = new clsBusinessProBizCaForms();
															$formSections = $objBusinessProBizCaForms->GetAllFormSections($businessFormId);

															if ($formSections != '') {
																$count = 0;
																foreach ($formSections as $rows) {

																	$businessFormSectionId = $rows['businessFormSectionId'];

																	/*---- get section fields ------*/
																	$sectionFields = $objBusinessProBizCaForms->GetAllFormSectionsFields($businessFormSectionId);

																	if ($sectionFields != '') {
																		$fieldTypeName = '';
																		$lableColor = '';
																		$maskClass = '';
																		while ($rows = mysqli_fetch_assoc($sectionFields)) {

																			$businessSectionFieldId = $rows['businessSectionFieldId'];
																			$fieldLabel = ($rows['fieldLabel']);
																			$fieldType = ($rows['fieldType']);
																			$isRequried = ($rows['isRequried']);
																			$isPrimaryField = ($rows['isPrimaryField']);
																			$representativeName = ($rows['representativeName']);

																			if ($rows['fieldLabel'] == 'Phone No.' || $rows['fieldLabel'] == 'Phone No') {
																				$maskClass = 'phone';
																			} else {
																				$maskClass = '';
																			}

																			$fieldValue = (explode(",", $rows['fieldValue']));
																			$checkboxArray = array();
																			foreach ($fieldValue as $value) {
																				$currentArray = array("checkBoxFieldValue" => $value);
																				array_push($checkboxArray, $currentArray);
																			}

																			//type name 
																			if ($fieldType == 1) {
																				$fieldTypeName = "Check Box";
																			}
																			if ($fieldType == 2) {
																				$fieldTypeName = "Date";
																			}
																			if ($fieldType == 3) {
																				$fieldTypeName = "Date & Time";
																			}
																			if ($fieldType == 4) {
																				$fieldTypeName = "Dropdown";
																			}
																			if ($fieldType == 5) {
																				$fieldTypeName = "Large Text Box";
																			}
																			if ($fieldType == 6) {
																				$fieldTypeName = "Note";
																			}
																			if ($fieldType == 7) {
																				$fieldTypeName = "Radio Button";
																			}
																			if ($fieldType == 8) {
																				$fieldTypeName = "Representative Name";
																			}
																			if ($fieldType == 9) {
																				$fieldTypeName = "Simple Text Box";
																			}
																			if ($fieldType == 10) {
																				$fieldTypeName = "Single Image/PDF Field";
																			}
																			if ($fieldType == 11) {
																				$fieldTypeName = "Time Only";
																			}
																			if ($fieldType == 12) {
																				$fieldTypeName = "US State Names";
																			}
																			if ($fieldType == 13) {
																				$fieldTypeName = "Signture Box";
																			}
																			if ($fieldType == 14) {
																				$fieldTypeName = "Single Range Selector";
																			}
																			if ($fieldType == 15) {
																				$fieldTypeName = "Two Range Selector";
																			}

													?>
																			<div class="">
																				<div class="">
																					<div class="form-group">
																						<input type="hidden" name="isPrimaryField" id="isPrimaryField" value="<?php echo ($isPrimaryField); ?>">
																						<label style="" for="for_<?php echo ($businessSectionFieldId); ?>" class="label_style">
																							<?php echo ($fieldLabel); ?>
																							<?php if ($isRequried == 1) { ?><span class="danger" style="color:red;">*</span>
																							<?php } ?>
																						</label>
																						<div class="controls">
																							<?php if ($fieldType == 1) { ?>
																								<div class="checkboxes">
																									<?php
																									$newId = 1;
																									foreach ($checkboxArray as $k => $id) {
																										$checkBoxFieldValue = $id['checkBoxFieldValue'];
																									?>
																										<label style=""><input type="checkbox" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>][]" value="<?php echo ($checkBoxFieldValue); ?>" <?php if ($isRequried == 1) { ?> required <?php } ?> /> <span>
																												<?php echo ($checkBoxFieldValue); ?>
																											</span>
																											<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																										</label>

																									<?php
																										$newId++;
																									}
																									?>
																								</div>
																							<?php } elseif ($fieldType == 2) { ?>
																								<fieldset class="form-group">
																									<input type="date" class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 3) { ?>
																								<fieldset class="form-group">
																									<input type="datetime-local" class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 4) { ?>
																								<fieldset>
																									<select id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" class="select form-control custom-arrow" <?php
																																																																				if ($isRequried == 1) { ?> required <?php } ?>>
																										<?php
																										$i = 1;
																										$checkBoxFieldValues = '';
																										foreach ($checkboxArray as $k => $id) {
																											$checkBoxFieldValues = $id['checkBoxFieldValue'];
																										?>
																											<option value="<?php echo ($checkBoxFieldValues); ?>">
																												<?php echo ($checkBoxFieldValues); ?>
																											</option>
																										<?php
																											$i++;
																										}
																										?>
																									</select>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 5) { ?>
																								<fieldset class="form-group">
																									<textarea class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" rows="3" placeholder="" <?php
																																																			if ($isRequried == 1) { ?> required <?php } ?> name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]"></textarea>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 6) { ?>
																								<input type="text" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" id="fieldId_<?php echo ($businessSectionFieldId); ?>" class="form-control mb-1" <?php
																																																																			if ($isRequried == 1) { ?> required <?php } ?>>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																							<?php } elseif ($fieldType == 7) { ?>
																								<fieldset>
																									<?php
																									$j = 1;
																									$inputTypeRadio = '';
																									foreach ($checkboxArray as $k => $id) {
																										$inputTypeRadio = $id['checkBoxFieldValue'];
																									?>
																										<input type="radio" value="<?php echo ($inputTypeRadio); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																										<label for="input-radio-15">
																											<?php echo ($inputTypeRadio); ?>
																										</label>
																									<?php
																										$j++;
																									}
																									?>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 8) { ?>
																								<input type="text" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" id="id_<?php echo ($businessSectionFieldId); ?>" value="<?php echo ($representativeName); ?>" readonly class="form-control mb-1" <?php
																																																																															if ($isRequried == 1) { ?> required <?php } ?>>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																							<?php } elseif ($fieldType == 9) { ?>
																								<input type="text" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" id="id_<?php echo ($businessSectionFieldId); ?>" textFieldId="<?php echo ($businessSectionFieldId); ?>" class="form-control mb-1 <?php echo ($maskClass); ?>" onchange="checkEmailexist(this);" <?php
																																																																																															if ($isRequried == 1) { ?> required <?php } ?>>
																								<span id="emailError_<?php echo ($businessSectionFieldId); ?>" class="hide" style="color:red;"> Email already exist
																								</span>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																							<?php } elseif ($fieldType == 10) { ?>
																								<fieldset class="form-group">
																									<div class="custom-file">
																										<input type="file" class="custom-file-input" id="id_<?php echo ($businessSectionFieldId); ?>" <?php if ($isRequried == 1) { ?> required <?php } ?> name="businessSectionImage[
																						<?php echo ($businessSectionFieldId . '_' . $fieldLabel); ?>]">
																										<label style="color:<?php echo ($formTextColor); ?>;" class="custom-file-label" for="inputGroupFile02" aria-describedby="inputGroupFile02">Choose
																											file</label>
																									</div>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 11) { ?>
																								<fieldset class="form-group">
																									<input type="time" class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 12) { ?>
																								<fieldset class="form-group">
																									<select id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" class="select form-control custom-arrow" <?php
																																																																				if ($isRequried == 1) { ?> required <?php } ?>>
																										<option value="">Please Select</option>
																										<?php
																										foreach ($rowsStates as $row) {
																											$countryStateMasterId = $row['countryStateMasterId'];
																											$location_name =  stripslashes($row['name']);
																										?>
																											<option value="<?php echo ($location_name); ?>">
																												<?php echo ($location_name); ?>
																											</option>
																										<?php
																										}
																										?>
																									</select>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 13) { ?>

																								<?php
																								$isRequired = $isRequried == 1 ? 1 : 0;

																								?>
																								<fieldset class="form-group">
																									<!-- <p>Default signature:</p> -->

																									<div class="ui-content popUpHeight">
																										<div id="div_signcontract" isRequired="<?php echo $isRequired; ?>">
																											<input type="hidden" class="" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" value='1'>
																											<canvas id="canvas" style="width:100%" width="100%">Canvas is not
																												supported</canvas>
																											<div>
																												<span id="signtureDiv"></span>
																												<br>
																												<input id="btnSubmitSign" type="button" data-inline="true" data-mini="true" data-theme="b" value="Submit Sign" onclick="fun_submit(this)" />
																												<input id="btnClearSign" type="button" data-inline="true" data-mini="true" data-theme="b" value="Clear" onclick="init_Sign_Canvas()" />
																											</div>

																											<div id="signtureImageDiv">

																											</div>
																										</div>

																									</div>
																									<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																								</fieldset>
																							<?php } elseif ($fieldType == 14) { ?>
																								<fieldset class="form-group">
																									<?php
																									$min = isset($checkboxArray[0]['checkBoxFieldValue']) ? $checkboxArray[0]['checkBoxFieldValue'] : 0;
																									$max = isset($checkboxArray[1]['checkBoxFieldValue']) ? $checkboxArray[1]['checkBoxFieldValue'] : 10;
																									?>
																									<div class="range-slider grad" style='--min:<?php echo $min; ?>; --max:<?php echo $max; ?>; --step:100; --value:<?php echo $min; ?>; --text-value:"<?php echo $min; ?>";width: 100%;'>
																										<input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $min; ?>" oninput="updateTextInput(this.value);this.parentNode.style.setProperty('--value',this.value); this.parentNode.style.setProperty('--text-value', JSON.stringify((+this.value).toLocaleString()))">
																										<output></output>
																										<div class='range-slider__progress'></div>
																									</div>
																								</fieldset>

																								<input type="hidden" class="input-min" id="minValue" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" value="<?php echo $min; ?>" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																							<?php } elseif ($fieldType == 15) { ?>
																								<!-- <p>Two Way Range Selector</p> -->
																								<fieldset class="form-group">
																									<?php
																									$min = isset($checkboxArray[0]['checkBoxFieldValue']) ? $checkboxArray[0]['checkBoxFieldValue'] : 0;
																									$max = isset($checkboxArray[1]['checkBoxFieldValue']) ? $checkboxArray[1]['checkBoxFieldValue'] : 10;
																									?>
																									<!-- Two Way Range slider -->
																									<div class="range-slider flat" data-ticks-position='top' style='--min:<?php echo $min; ?>; --max:<?php echo $max; ?>; --value-a:<?php echo $min; ?>; --value-b:<?php echo $max; ?>;  --text-value-a:"<?php echo $min; ?>"; --text-value-b:"<?php echo $max; ?>";width:100%;'>
																										<input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $min; ?>" oninput="updateRangeValue('min', this.value);this.parentNode.style.setProperty('--value-a',this.value); this.parentNode.style.setProperty('--text-value-a', JSON.stringify(this.value))">
																										<output></output>
																										<input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $max; ?>" oninput="updateRangeValue('max', this.value);this.parentNode.style.setProperty('--value-b',this.value); this.parentNode.style.setProperty('--text-value-b', JSON.stringify(this.value))">
																										<output></output>
																										<div class='range-slider__progress'></div>
																									</div>
																								</fieldset>
																								<input type="hidden" id="minValue2" value="<?php echo $min; ?>">
																								<input type="hidden" id="maxValue2" value="<?php echo $max; ?>">
																								<input type="hidden" id="rangeValues" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" value="<?php echo $min . ',' . $max; ?>" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																							<?php } ?>
																						</div>
																					</div>
																				</div>

																			</div>
													<?php
																		}
																	}
																	$count++;
																}
															}
														}
													}
													?>
												</div>

											</div>

										</div>
									</form>
								</div>
								<div class="modal-footer" style="padding: 0 20px;">
									<?php if ($contactLimit == $customerCount) { ?>
										<span class="text-danger" style="float: left;width: 97%;"> Your contact enroll limit is over</span>
									<?php } ?>
									<?php
									if ($customQrFormchk == 1 && count($org_BusinessQrForms) > 0 && $org_BusinessQrForms[0]['isActive'] == 1 && $org_BusinessQrForms[0]['isShowonQr'] == 1) {
									?>
										<div style='width: 100%;display: flex;justify-content: end;'>
											<a type="button" class="btn btn-secondary prev" href="javascript:void(0);">Previous</a>
											<a href="javascript:void(0);" class="btn btn-primary next" style="width: 70px;">Next</a>
											<button type="button" id="btnQRCodeFormSave" class="btn btn-primary btn-block hidden" style="width: fit-content;" <?php if ($contactLimit == $customerCount) { ?> disabled <?php } ?>>
												<i class="glyphicon glyphicon-off"></i> Submit
											</button>
										</div>
										<div class="row justify-content-center" style="width: 100%;display: flex;justify-content: start;">
											<label class="form-check-label" style="padding : 6px;">
												<a style="cursor: pointer;"
													onclick="confirmCustomerMobileNumber(<?php echo $refId; ?>,<?php echo $refType; ?>,<?php echo $currentBusinessId; ?>);"><strong>
														Already have ProBizCa? Click Here!</strong></a>
											</label>
										</div>
									<?php
									} else {
									?>
										<button type="button" id="btnQRCodeFormSave" class="btn btn-primary btn-block" <?php if ($contactLimit == $customerCount) { ?> disabled <?php } ?>>
											<i class="glyphicon glyphicon-off"></i> Submit
										</button>
										<div class="row justify-content-center" style="width: 100%;text-align: center;">
											<label class="form-check-label" style="padding : 6px;">
												<a style="cursor: pointer;"
													onclick="confirmCustomerMobileNumber(<?php echo $refId; ?>,<?php echo $refType; ?>,<?php echo $currentBusinessId; ?>);"><strong>
														Already have ProBizCa? Click Here!</strong></a>
											</label>
										</div>
									<?php
									}
									?>
								</div>

							</div>


						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/custom.js"></script>
		<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/parsleyjs/parsley.js"></script>

		<script>
			/* Range Selector Js Start */
			function updateTextInput(val) {
				document.getElementById('minValue').value = val;
			}

			function updateRangeValue(type, value) {
				if (type === 'min') {
					document.getElementById('minValue2').value = value;
				} else if (type === 'max') {
					document.getElementById('maxValue2').value = value;
				}
				var minValue = document.getElementById('minValue2').value;
				var maxValue = document.getElementById('maxValue2').value;
				document.getElementById('rangeValues').value = minValue + ',' + maxValue;
			}
			/* Range Selector Js End */
			//***** HIDE WELCOME DIV *****//
			$("#welcomeDiv").hide();

			//form validation
			$('#qrcodeForm').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					return true; // Don't submit form for this demo
				});
		</script>
	<?php
	} else  if (isset($_GET['shareType']) && $customerId > 0 && $newUserId == -1) {
	?>

		<script type="text/javascript">
			$(window).on('load', function() {
				$('#startupModalfsgssd').modal('show', {
					backdrop: 'static',
					keyboard: false
				});
				// $("#phonea").mask("(*************");
			});
		</script>
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="modal show businessShare" id="startupModalfsgssd" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
						<div class="modal-dialog">
							<?php
							$businessFormId = 0;
							if (count($org_BusinessQrForms) > 0) {
								$businessFormId = $org_BusinessQrForms[0]['businessFormId'];
							}
							?>
							<!-- Modal content 2-->
							<div class="modal-content">
								<div class="modal-header" style="padding:15px 29px !important;">
									<h5 class="justify-content-center">
										<?php echo ($orgName); ?>
									</h5>
									<?php
									$headerMsg = "Complete info below to accept invitation:";
									if ($customQrFormchk == 1 && count($org_BusinessQrForms) > 0 && $org_BusinessQrForms[0]['isActive'] == 1 && $org_BusinessQrForms[0]['isShowonQr'] == 1) {
										$headerMsg = "Complete the information below, Click on the <b>Next</b> button to continue with the process:";
									}
									?>


								</div>
								<div class="modal-body" style="padding:5px 29px 0 29px; padding-bottom: 0 !important;">


									<form class="form form-horizontal" id="qrcodeForm" method="post" action="<?php echo BASE_PATH; ?>/contactqrcodedetailssubmit.html" enctype="multipart/form-data">
										<input type="hidden" name="shareType" id="shareType" value="<?php echo ($shareType); ?>">
										<input type="hidden" name="slug" id="slug" value="<?php echo ($previewURL); ?>">
										<input type="hidden" name="businessId" id="businessId" value="<?php echo ($currentBusinessId); ?>">
										<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($scustomerId); ?>">
										<input type="hidden" name="displayURLTag" id="displayURLTag" value="<?php echo ($displayURLTag); ?>">
										<input type="hidden" name="businessFormId" id="businessFormId" value="<?php echo ($businessFormId); ?>">

										<div class="row justify-content-left">
											<label class="form-check-label" style="padding-left: 10px !important;">
												<?php echo $headerMsg; ?>
											</label>
										</div><br />

										<ul id="mytabs" class="nav nav-tabs" role="tablist">
											<li class="active">
												<a href="#first" role="tab" data-toggle="tab">
													<icon class="fa fa-home"></icon> First tab
												</a>
											</li>
											<li><a href="#second" role="tab" data-toggle="tab">
													<i class="fa fa-user"></i> Second tab
												</a>
											</li>

										</ul>
										<!-- Tab panes -->
										<div class="tab-content">
											<div class="tab-pane fade active in" id="first">
												<input type="hidden" value="<?php echo $refType; ?>" name="refType" />
												<input type="hidden" value="<?php echo $refId; ?>" name="refId" />
												<input type="hidden" value="<?php //echo $qrIsPrimary;
																			?>" name="isPrimary" />
												<div class="form-group">
													<div class="row">
														<div class="col-sm-12"><label for="contactFirstName"><span class="glyphicon glyphicon-user"></span>First Name <span style="color:red;">*</span></label>
														</div>
														<div class="col-sm-12">
															<input type="text" name="contactFirstName" class="form-control" id="contactFirstName" placeholder="Enter First Name" oninput="charactersOnly(this.id);" required>
															<span id="contactFirstNameSpan" style="color: red; font-size: 12px;"></span>
														</div>
													</div>
												</div>
												<div class="form-group">
													<div class="row">
														<div class="col-sm-12"><label for="contactLastName"><span class="glyphicon glyphicon-user"></span>Last Name <span style="color:red;">*</span></label>
														</div>
														<div class="col-sm-12"> <input type="text" name="contactLastName" class="form-control" id="contactLastName" placeholder="Enter Last Name" oninput="charactersOnly(this.id);" required>
															<span id="contactLastNameSpan" style="color: red; font-size: 12px;"></span>

														</div>

													</div>
												</div>
												<div class="form-group">
													<div class="row">
														<div class="col-sm-12">
															<label for="contactemails"><span class="glyphicon glyphicon-eye-open"></span> Email <span style="color:red;">*</span></label>
														</div>
														<div class="col-sm-12">
															<input type="email" class="form-control" id="contactemails" name="contactemails" placeholder="Enter Email" pattern="^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$" required>
															<span id="contactemailSpan" style="color: red; font-size: 12px;"></span>

														</div>
													</div>
												</div>
												<div class="form-group">
													<div class="row">
														<div class="col-sm-12">
															<label for="contactphone"><span class="glyphicon glyphicon-eye-open"></span> Mobile
																Number <span style="color:red;">*</span></label>
														</div>
														<div class="col-sm-12">
															<input type="tel" class="form-control phonea" id="phonea" name="contactphone" placeholder="Enter Mobile Number" onkeyup="myFunction();" required>
															<span id="contactphoneSpan" style="color: red; font-size: 12px;"></span>
														</div>

													</div>
												</div>
												<div class="form-group">
													<div class="row">
														<div class="col-sm-12">
															<label for="contactphone"><span class="glyphicon glyphicon-eye-open"></span> Comments </label>
														</div>
														<div class="col-sm-12">
															<textarea id="notesDescription" class="form-control " rows='5' name="notesDescription" placeholder="Enter Comments Here"></textarea>
														</div>
														<div class="col-12">
															<input style="width: 17px;height: 17px;margin: 0 5px;margin-top: 15px;" class="form-check-input" type="checkbox" value="" name="flexCheckChecked" id="flexCheckChecked" checked>
															<label style="font-size: 14px;margin-left: 35px;margin-top: 10px;" class="form-check-label">
																By checking this box I acknowledge I am opting-in to receiving SMS Messages to <span id="phnamespan"></span> on behalf of my ProBizCa.
															</label>
															<span id="checkboxSpan" style="color: red; font-size: 12px;"></span>

														</div>
													</div>
												</div>


											</div>
											<div class="tab-pane fade" id="second">
												<?php
												$customQrFormchk = $objTemplateEngine->customQrFormchk;
												$org_BusinessQrForms = $objTemplateEngine->org_BusinessQrForms;

												if ($customQrFormchk == 1 && count($org_BusinessQrForms) > 0 && $org_BusinessQrForms[0]['isActive'] == 1 && $org_BusinessQrForms[0]['isShowonQr'] == 1) {

													if (count($org_BusinessQrForms) > 0) {
														$businessFormId = $org_BusinessQrForms[0]['businessFormId'];

														$objBusinessProBizCaForms = new clsBusinessProBizCaForms();
														$formSections = $objBusinessProBizCaForms->GetAllFormSections($businessFormId);

														if ($formSections != '') {
															$count = 0;
															foreach ($formSections as $rows) {

																$businessFormSectionId = $rows['businessFormSectionId'];

																/*---- get section fields ------*/
																$sectionFields = $objBusinessProBizCaForms->GetAllFormSectionsFields($businessFormSectionId);

																if ($sectionFields != '') {
																	$fieldTypeName = '';
																	$lableColor = '';
																	$maskClass = '';
																	while ($rows = mysqli_fetch_assoc($sectionFields)) {

																		$businessSectionFieldId = $rows['businessSectionFieldId'];
																		$fieldLabel = ($rows['fieldLabel']);
																		$fieldType = ($rows['fieldType']);
																		$isRequried = ($rows['isRequried']);
																		$isPrimaryField = ($rows['isPrimaryField']);
																		$representativeName = ($rows['representativeName']);

																		if ($rows['fieldLabel'] == 'Phone No.' || $rows['fieldLabel'] == 'Phone No') {
																			$maskClass = 'phone';
																		} else {
																			$maskClass = '';
																		}

																		$fieldValue = (explode(",", $rows['fieldValue']));
																		$checkboxArray = array();
																		foreach ($fieldValue as $value) {
																			$currentArray = array("checkBoxFieldValue" => $value);
																			array_push($checkboxArray, $currentArray);
																		}

																		//type name 
																		if ($fieldType == 1) {
																			$fieldTypeName = "Check Box";
																		}
																		if ($fieldType == 2) {
																			$fieldTypeName = "Date";
																		}
																		if ($fieldType == 3) {
																			$fieldTypeName = "Date & Time";
																		}
																		if ($fieldType == 4) {
																			$fieldTypeName = "Dropdown";
																		}
																		if ($fieldType == 5) {
																			$fieldTypeName = "Large Text Box";
																		}
																		if ($fieldType == 6) {
																			$fieldTypeName = "Note";
																		}
																		if ($fieldType == 7) {
																			$fieldTypeName = "Radio Button";
																		}
																		if ($fieldType == 8) {
																			$fieldTypeName = "Representative Name";
																		}
																		if ($fieldType == 9) {
																			$fieldTypeName = "Simple Text Box";
																		}
																		if ($fieldType == 10) {
																			$fieldTypeName = "Single Image/PDF Field";
																		}
																		if ($fieldType == 11) {
																			$fieldTypeName = "Time Only";
																		}
																		if ($fieldType == 12) {
																			$fieldTypeName = "US State Names";
																		}
																		if ($fieldType == 13) {
																			$fieldTypeName = "Signture Box";
																		}
																		if ($fieldType == 14) {
																			$fieldTypeName = "Single Range Selector";
																		}
																		if ($fieldType == 15) {
																			$fieldTypeName = "Two Range Selector";
																		}

												?>
																		<div class="">
																			<div class="">
																				<div class="form-group">
																					<input type="hidden" name="isPrimaryField" id="isPrimaryField" value="<?php echo ($isPrimaryField); ?>">
																					<label style="" for="for_<?php echo ($businessSectionFieldId); ?>" class="label_style">
																						<?php echo ($fieldLabel); ?>
																						<?php if ($isRequried == 1) { ?><span class="danger" style="color:red;">*</span>
																						<?php } ?>
																					</label>
																					<div class="controls">
																						<?php if ($fieldType == 1) { ?>
																							<div class="checkboxes">
																								<?php
																								$newId = 1;
																								foreach ($checkboxArray as $k => $id) {
																									$checkBoxFieldValue = $id['checkBoxFieldValue'];
																								?>
																									<label style=""><input type="checkbox" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>][]" value="<?php echo ($checkBoxFieldValue); ?>" <?php if ($isRequried == 1) { ?> required <?php } ?> /> <span>
																											<?php echo ($checkBoxFieldValue); ?>
																										</span>
																									</label>

																								<?php
																									$newId++;
																								}
																								?>
																							</div>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																						<?php } elseif ($fieldType == 2) { ?>
																							<fieldset class="form-group">
																								<input type="date" class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																							</fieldset>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																						<?php } elseif ($fieldType == 3) { ?>
																							<fieldset class="form-group">
																								<input type="datetime-local" class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																							</fieldset>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																						<?php } elseif ($fieldType == 4) { ?>
																							<fieldset>
																								<select id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" class="select form-control custom-arrow" <?php
																																																																			if ($isRequried == 1) { ?> required <?php } ?>>
																									<?php
																									$i = 1;
																									$checkBoxFieldValues = '';
																									foreach ($checkboxArray as $k => $id) {
																										$checkBoxFieldValues = $id['checkBoxFieldValue'];
																									?>
																										<option value="<?php echo ($checkBoxFieldValues); ?>">
																											<?php echo ($checkBoxFieldValues); ?>
																										</option>
																									<?php
																										$i++;
																									}
																									?>
																								</select>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																							</fieldset>
																						<?php } elseif ($fieldType == 5) { ?>
																							<fieldset class="form-group">
																								<textarea class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" rows="3" placeholder="" <?php
																																																		if ($isRequried == 1) { ?> required <?php } ?> name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]"></textarea>
																							</fieldset>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																						<?php } elseif ($fieldType == 6) { ?>
																							<input type="text" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" id="fieldId_<?php echo ($businessSectionFieldId); ?>" class="form-control mb-1" <?php
																																																																		if ($isRequried == 1) { ?> required <?php } ?>>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																						<?php } elseif ($fieldType == 7) { ?>
																							<fieldset>
																								<?php
																								$j = 1;
																								$inputTypeRadio = '';
																								foreach ($checkboxArray as $k => $id) {
																									$inputTypeRadio = $id['checkBoxFieldValue'];
																								?>
																									<input style="margin-left: 15px;" type="radio" value="<?php echo ($inputTypeRadio); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																									<label for="input-radio-15">
																										<?php echo ($inputTypeRadio); ?>
																									</label>
																								<?php
																									$j++;
																								}
																								?>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																							</fieldset>
																						<?php } elseif ($fieldType == 8) { ?>
																							<input type="text" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" id="id_<?php echo ($businessSectionFieldId); ?>" value="<?php echo ($representativeName); ?>" readonly class="form-control mb-1" <?php
																																																																														if ($isRequried == 1) { ?> required <?php } ?>>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>

																						<?php } elseif ($fieldType == 9) { ?>
																							<input type="text" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" id="id_<?php echo ($businessSectionFieldId); ?>" textFieldId="<?php echo ($businessSectionFieldId); ?>" class="form-control mb-1 <?php echo ($maskClass); ?>" onchange="checkEmailexist(this);" <?php
																																																																																														if ($isRequried == 1) { ?> required <?php } ?>>
																							<span id="emailError_<?php echo ($businessSectionFieldId); ?>" class="hide" style="color:red;"> Email already exist
																							</span>
																							<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																						<?php } elseif ($fieldType == 10) { ?>
																							<fieldset class="form-group">
																								<div class="custom-file">
																									<input type="file" class="custom-file-input" id="id_<?php echo ($businessSectionFieldId); ?>" <?php if ($isRequried == 1) { ?> required <?php } ?> name="businessSectionImage[
																						<?php echo ($businessSectionFieldId . '_' . $fieldLabel); ?>]">
																									<label style="color:<?php echo ($formTextColor); ?>;" class="custom-file-label" for="inputGroupFile02" aria-describedby="inputGroupFile02">Choose
																										file</label>
																								</div>
																							</fieldset>
																						<?php } elseif ($fieldType == 11) { ?>
																							<fieldset class="form-group">
																								<input type="time" class="form-control" id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																							</fieldset>
																						<?php } elseif ($fieldType == 12) { ?>
																							<fieldset class="form-group">
																								<select id="id_<?php echo ($businessSectionFieldId); ?>" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" class="select form-control custom-arrow" <?php
																																																																			if ($isRequried == 1) { ?> required <?php } ?>>
																									<option value="">Please Select</option>
																									<?php
																									foreach ($rowsStates as $row) {
																										$countryStateMasterId = $row['countryStateMasterId'];
																										$location_name =  stripslashes($row['name']);
																									?>
																										<option value="<?php echo ($location_name); ?>">
																											<?php echo ($location_name); ?>
																										</option>
																									<?php
																									}
																									?>
																								</select>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																							</fieldset>
																						<?php } elseif ($fieldType == 13) { ?>

																							<?php
																							$isRequired = $isRequried == 1 ? 1 : 0;

																							?>
																							<fieldset class="form-group">
																								<!-- <p>Default signature:</p> -->

																								<div class="ui-content popUpHeight">
																									<div id="div_signcontract" isRequired="<?php echo $isRequired; ?>">
																										<input type="hidden" class="" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" value='1'>
																										<canvas id="canvas" style="width:100%" width="100%">Canvas is not
																											supported</canvas>
																										<div>
																											<span id="signtureDiv"></span>
																											<br>
																											<input id="btnSubmitSign" type="button" data-inline="true" data-mini="true" data-theme="b" value="Submit Sign" onclick="fun_submit(this)" />
																											<input id="btnClearSign" type="button" data-inline="true" data-mini="true" data-theme="b" value="Clear" onclick="init_Sign_Canvas()" />
																										</div>

																										<div id="signtureImageDiv">

																										</div>
																									</div>

																								</div>
																								<span id="error_id_<?php echo ($businessSectionFieldId); ?>" style="color: red; font-size: 12px;"></span>
																							</fieldset>
																						<?php } elseif ($fieldType == 14) { ?>
																							<fieldset class="form-group">
																								<?php
																								$min = isset($checkboxArray[0]['checkBoxFieldValue']) ? $checkboxArray[0]['checkBoxFieldValue'] : 0;
																								$max = isset($checkboxArray[1]['checkBoxFieldValue']) ? $checkboxArray[1]['checkBoxFieldValue'] : 10;
																								?>
																								<div class="range-slider grad" style='--min:<?php echo $min; ?>; --max:<?php echo $max; ?>; --step:100; --value:<?php echo $min; ?>; --text-value:"<?php echo $min; ?>";width: 100%;'>
																									<input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $min; ?>" oninput="updateTextInput(this.value);this.parentNode.style.setProperty('--value',this.value); this.parentNode.style.setProperty('--text-value', JSON.stringify((+this.value).toLocaleString()))">
																									<output></output>
																									<div class='range-slider__progress'></div>
																								</div>
																							</fieldset>

																							<input type="hidden" class="input-min" id="minValue" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" value="<?php echo $min; ?>" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																						<?php } elseif ($fieldType == 15) { ?>
																							<!-- <p>Two Way Range Selector</p> -->
																							<fieldset class="form-group">
																								<?php
																								$min = isset($checkboxArray[0]['checkBoxFieldValue']) ? $checkboxArray[0]['checkBoxFieldValue'] : 0;
																								$max = isset($checkboxArray[1]['checkBoxFieldValue']) ? $checkboxArray[1]['checkBoxFieldValue'] : 10;
																								?>
																								<!-- Two Way Range slider -->
																								<div class="range-slider flat" data-ticks-position='top' style='--min:<?php echo $min; ?>; --max:<?php echo $max; ?>; --value-a:<?php echo $min; ?>; --value-b:<?php echo $max; ?>;  --text-value-a:"<?php echo $min; ?>"; --text-value-b:"<?php echo $max; ?>";width:100%;'>
																									<input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $min; ?>" oninput="updateRangeValue('min', this.value);this.parentNode.style.setProperty('--value-a',this.value); this.parentNode.style.setProperty('--text-value-a', JSON.stringify(this.value))">
																									<output></output>
																									<input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $max; ?>" oninput="updateRangeValue('max', this.value);this.parentNode.style.setProperty('--value-b',this.value); this.parentNode.style.setProperty('--text-value-b', JSON.stringify(this.value))">
																									<output></output>
																									<div class='range-slider__progress'></div>
																								</div>
																							</fieldset>
																							<input type="hidden" id="minValue2" value="<?php echo $min; ?>">
																							<input type="hidden" id="maxValue2" value="<?php echo $max; ?>">
																							<input type="hidden" id="rangeValues" name="businessSectionFieldId[<?php echo ($businessSectionFieldId); ?>]" value="<?php echo $min . ',' . $max; ?>" <?php if ($isRequried == 1) { ?> required <?php } ?>>
																						<?php } ?>
																					</div>
																				</div>
																			</div>

																		</div>
												<?php
																	}
																}
																$count++;
															}
														}
													}
												}
												?>
											</div>

										</div>


									</form>
								</div>
								<div class="modal-footer" style="padding: 0 20px;">
									<?php if ($contactLimit == $customerCount) { ?>
										<span class="text-danger" style="float: left;width: 97%;"> Your contact enroll limit is over</span>
									<?php } ?>
									<?php
									if ($customQrFormchk == 1 && count($org_BusinessQrForms) > 0 && $org_BusinessQrForms[0]['isActive'] == 1 && $org_BusinessQrForms[0]['isShowonQr'] == 1) {
									?>
										<div style='width: 100%;display: flex;justify-content: end;'>
											<a type="button" class="btn btn-secondary prev" href="javascript:void(0);">Previous</a>
											<a href="javascript:void(0);" class="btn btn-primary next" style="width: 70px;">Next</a>
											<button type="button" id="btnQRCodeFormSave" class="btn btn-primary btn-block hidden" style="width: fit-content;" <?php if ($contactLimit == $customerCount) { ?> disabled <?php } ?>>
												<i class="glyphicon glyphicon-off"></i> Submit
											</button>
										</div>
										<div class="row justify-content-center" style="width: 100%;display: flex;justify-content: start;">
											<label class="form-check-label" style="padding : 6px;">
												<a style="cursor: pointer;"
													onclick="confirmCustomerMobileNumber(<?php echo $refId; ?>,<?php echo $refType; ?>,<?php echo $currentBusinessId; ?>);"><strong>
														Already have ProBizCa? Click Here!</strong></a>
											</label>
										</div>
									<?php
									} else {
									?>
										<button type="button" id="btnQRCodeFormSave" class="btn btn-primary btn-block" <?php if ($contactLimit == $customerCount) { ?> disabled <?php } ?>>
											<i class="glyphicon glyphicon-off"></i> Submit
										</button>
										<div class="row justify-content-center" style="width: 100%;text-align: center;">
											<label class="form-check-label" style="padding : 6px;">
												<a style="cursor: pointer;"
													onclick="confirmCustomerMobileNumber(<?php echo $refId; ?>,<?php echo $refType; ?>,<?php echo $currentBusinessId; ?>);"><strong>
														Already have ProBizCa? Click Here!</strong></a>
											</label>
										</div>
									<?php
									}
									?>
								</div>
							</div>


						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/custom.js"></script>
		<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/parsleyjs/parsley.js"></script>
		<script>
			/* Range Selector Js Start */
			function updateTextInput(val) {
				document.getElementById('minValue').value = val;
			}

			function updateRangeValue(type, value) {
				if (type === 'min') {
					document.getElementById('minValue2').value = value;
				} else if (type === 'max') {
					document.getElementById('maxValue2').value = value;
				}
				var minValue = document.getElementById('minValue2').value;
				var maxValue = document.getElementById('maxValue2').value;
				document.getElementById('rangeValues').value = minValue + ',' + maxValue;
			}
			/* Range Selector Js End */

			//***** HIDE WELCOME DIV *****//
			$("#welcomeDiv").hide();

			//form validation
			$('#qrcodeForm').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					return true; // Don't submit form for this demo
				});
		</script>

	<?php
	}
	?>
	<div class="container">
		<div class="row">
			<div class="col-md-12">
				<div class="modal show" id="welcomeModal" role="dialog">
					<div class="modal-dialog">
						<!-- Modal content  3-->

						<div class="modal-content">
							<div class="modal-header" style="padding:15px 33px;">
								<h4 class="justify-content-center text-center">
									<?php echo ($orgName); ?>
								</h4> <span id="closePopup" class="close">&times;</span>
							</div>
							<div class="modal-body" style="padding:20px 29px; padding-top: 10px;">
								<form class="form form-horizontal" id="qrcodeForm" method="post" action="<?php echo BASE_PATH; ?>/contactqrcodedetailssubmit.html" enctype="multipart/form-data">
									<input type="hidden" name="slug" id="slug" value="<?php echo ($previewURL); ?>">
									<input type="hidden" name="businessId" id="businessId" value="<?php echo ($currentBusinessId); ?>">
									<input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($employeeId); ?>">
									<input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId); ?>">
									<div class="row justify-content-left">

									</div>
									<div>
										<div class="col-sm-12 pl-0">
											<h4 class="justify-content-center response-font-size"></h4>
										</div>
									</div>
									<div class="col-sm-12 text-center d-flex justify-content-between px-0 mb-4">
										<div class="">
											<button type="button" class="btn btn-primary btn-lg btn-background home-tooltip" id="demo2">
												<i class="fa-solid fa-house" aria-hidden="true"></i>
											</button>
											<p class="share-popup-tag mouse-hover-home">Home</p>
										</div>
										<div class="">
											<button type="button" class="btn btn-primary btn-lg btn-background copy-tooltip" data-text="<?php echo $URL; ?>" id="Copy" onclick="CopyUrl();">
												<i class="fa fa-files-o" aria-hidden="true"></i>
											</button>
											<p class="share-popup-tag mouse-hover-copy">Copy</p>
										</div>
										<div class="">
											<button type="button" class="btn btn-primary btn-lg btn-background contact-tooltip" id="SaveVcf" onclick="dnldVcf();">
												<i class="fa fa-id-card" aria-hidden="true"></i>
											</button>
											<p class="share-popup-tag mouse-hover-contact">Contacts </p>
										</div>

									</div>
									<div style="text-align: center;">
										<input type="checkbox" checked name="istrusted_device" id="istrusted_device">
										<label>Trusted Device</label>
									</div>
									<br />
									<div>
										<div class="col-sm-12 text-center">
											<img src="<?php echo ($probizcaLogo); ?>" class="img-fluid">
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<?php
	// 	}
	// }
	?>


	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/custom-file-input.js"></script>
	<!-- custom QR form Next js -->
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
	<!-- custom QR form Next js End -->
	<!-- Google tag (gtag.js) -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo $tracking_id; ?>"></script>
	<script>
		var trackingId = $('#tracking_id').val();
		// alert(trackingId);
		window.dataLayer = window.dataLayer || [];

		function gtag() {
			dataLayer.push(arguments);
		}
		gtag('js', new Date());

		gtag('config', trackingId);
	</script>
	<script type="text/javascript">
		 
		if (navigator.serviceWorker) {

			navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
				console.log('ServiceWorker registration successful with scope:', registration.scope);
			}).catch(function(error) {
				console.log('ServiceWorker registration failed:', error);
			});
		}



		let deferredPrompt;
		// document.getElementById('add').style.display='none';
		var btnAdd = document.getElementById('demo');
		var btnAdd2 = document.getElementById('demo2');

		btnAdd2.addEventListener("click", handler);

		function handler() {
			// Show the prompt
			deferredPrompt.prompt();
			// Wait for the user to respond to the prompt
			deferredPrompt.userChoice
				.then((choiceResult) => {
					if (choiceResult.outcome === 'accepted') {
						deferredPrompt = null;
						document.getElementById('add').style.display = 'show';
						$('#welcomeModal').modal('hide');
						console.log('User accepted the A2HS prompt');
						dnldVcf();
					} else {
						console.log('User dismissed the A2HS prompt');
					}

				});
		}

		window.addEventListener('beforeinstallprompt', (e) => {
			// Prevent Chrome 67 and earlier from automatically showing the prompt
			e.preventDefault();
			// Stash the event so it can be triggered later.
			deferredPrompt = e;
			// Update UI notify the user they can add to home screen
			// btnAdd.style.display = 'block';
			document.getElementById('add').style.display = 'block';

			btnAdd.addEventListener('click', (e) => {
				// alert("Hello");
				// hide our user interface that shows our A2HS button
				// btnAdd.style.display = 'none';
				// document.getElementById('add').style.display='none';
				// Show the prompt
				deferredPrompt.prompt();
				// Wait for the user to respond to the prompt
				deferredPrompt.userChoice
					.then((choiceResult) => {
						if (choiceResult.outcome === 'accepted') {
							deferredPrompt = null;
							document.getElementById('demo2').style.display = 'none';

							console.log('User accepted the A2HS prompt');
							dnldVcf();
						} else {
							console.log('User dismissed the A2HS prompt');
						}
					});
			});

			btnAdd2.addEventListener('click', (e) => {
				//    alert("Hello");
				// hide our user interface that shows our A2HS button
				// btnAdd.style.display = 'none';
				// document.getElementById('add').style.display='none';
				// Show the prompt
				deferredPrompt.prompt();
				// Wait for the user to respond to the prompt
				deferredPrompt.userChoice
					.then((choiceResult) => {
						if (choiceResult.outcome === 'accepted') {
							deferredPrompt = null;
							document.getElementById('add').style.display = 'show';
							$('#welcomeModal').modal('hide');
							console.log('User accepted the A2HS prompt');
							dnldVcf();
						} else {
							console.log('User dismissed the A2HS prompt');
						}

					});
			});
		});

		// console.clear();
		function dnldVcf() {

			var vcardEmail = '<?php echo $vcardEmail; ?>';
			var vcardPhoneNo = '<?php echo $vcardPhoneNo; ?>';

			var vcardAddress = '<?php echo $vcardAddress; ?>';
			var vcardStreet = '<?php echo $vcardAddress2; ?>';


			var vcardCity = '<?php echo $vcardCity; ?>';
			var vcardState = '<?php echo $vcardState; ?>';
			var vcardZipeCode = '<?php echo $vcardZipeCode; ?>';
			var vcardCountry = '<?php echo $vcardCountry; ?>';

			var vcardDisplayName = '<?php echo $vcardDisplayName; ?>';
			var vcardfirstName = '<?php echo $vcardfirstName; ?>';
			var vcardlastName = '<?php echo $vcardlastName; ?>';
			var vcardCompanyName = '<?php echo addslashes($vcardCompanyName); ?>';
			var vcardlogo = '<?php echo $vcardbusinessLogo; ?>';
			var vcardbusinessLogoext = '<?php echo $vcardbusinessLogoext; ?>';

			var vcfString = "BEGIN:VCARD\nVERSION:3.0\nREV:2022-12-30T05:56:13Z\nN:" + vcardlastName + ";" + vcardfirstName + ";;;\nFN:" + vcardDisplayName + "\nORG:" + vcardCompanyName + "\nPHOTO;TYPE=" + vcardbusinessLogoext + ";ENCODING=BASE64:" + vcardlogo + "\nEMAIL;INTERNET:" + vcardEmail + "\nTEL:" + vcardPhoneNo + "\nADR;TYPE=WORK:;;" + vcardAddress + ", " + vcardStreet + ";" + vcardCity + ";" + vcardState + ";" + vcardZipeCode + ";" + vcardCountry + "\nEND:VCARD";

			// return false;
			var blob = new Blob([vcfString], {
				type: 'text/x-vcard'
			});
			var downloadUrl = URL.createObjectURL(blob);
			var a = document.createElement("a");
			a.href = downloadUrl;
			a.download = vcardDisplayName + ".vcf";
			document.body.appendChild(a);
			a.click();

		}

		//If User is not logged in then hide share and add to home screen button 
		var sessionCount = '<?php echo $sessionCount; ?>';
		if (sessionCount > 0) {
			$('#login-icon-section').addClass('hide');
			$('#divider').addClass('hide');
		}

		function charactersOnly(id) {
			var element = document.getElementById(id);

			// Check if element exists
			if (!element) {
				console.error('Element with id "' + id + '" not found');
				return;
			}

			console.log('Processing field:', id, 'Current value:', element.value);

			// Check for URL patterns first - more specific pattern to avoid false positives
			var urlPattern = /https?:\/\/[^\s]+|www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\/[^\s]*/gi;
			if (urlPattern.test(element.value)) {
				console.log('URL detected, clearing field');
				element.value = "";

				// Show error message for the specific field
				var errorSpanId = id + 'Span';
				var errorSpan = $('#' + errorSpanId);

				// Check if error span exists before trying to show message
				if (errorSpan.length > 0) {
					errorSpan.fadeIn().html("URLs are not allowed in this field.");
					setTimeout(function() {
						errorSpan.fadeOut('slow');
					}, 5000);
				}

				return;
			}

			// Store original value for comparison
			var originalValue = element.value;

			// Remove non-alphabetical characters (allow spaces for names)
			element.value = element.value.replace(/[^a-zA-Z\s]/gi, "");

			console.log('Original value:', originalValue, 'Filtered value:', element.value);
		}

		// Test function to verify charactersOnly is working
		function testCharactersOnly() {
			console.log('Testing charactersOnly function...');

			// Create a test input element
			var testInput = document.createElement('input');
			testInput.id = 'testInput';
			testInput.value = 'Test123!@#';
			document.body.appendChild(testInput);

			console.log('Before filtering:', testInput.value);
			charactersOnly('testInput');
			console.log('After filtering:', testInput.value);

			// Clean up
			document.body.removeChild(testInput);
		}

		// Call test function when page loads
		$(document).ready(function() {
			console.log('Document ready, testing charactersOnly...');
			testCharactersOnly();
		});

		$(".phonea").mask("(*************");
		var customerId = $("#customerId").val();
		var businessUserId = $("#businessUserId").val();
		var currentBusinessId = $("#currentBusinessId").val();
		var path = '<?php echo BASE_PATH; ?>';
		var customerLoginPath = '<?php echo BASE_PATH; ?>' + '/customer/business.html';
		var isAuthVerified = '<?php echo $isAuthVerified; ?>';

		var displayURLTag = '<?php echo $displayURLTag; ?>';

		if (isAuthVerified == 0) {
			confirmMobileNumberPopUp1();
		}
		//onpage load view popup
		function confirmMobileNumberPopUp1() {

			$("#welcomeModal").hide();
			// return false;
			var mobileNumber = $('#hiddenMobileNumber').val();

			var mobileNumber = $('#customerMobileNumber').val();
			// alert(customerId);
			if (mobileNumber != '') {
				$.ajax({
					type: "GET",
					url: path + "/ajax/ajax_check_customer_mobile_number.html",
					data: {
						mobileNumber: mobileNumber,
						customerId: customerId
					},
					success: function(data) {
						if (data == 0) {
							const Toast = Swal.mixin({
								toast: true,
								position: 'middle',
								showConfirmButton: false,
								timer: 3000,
								timerProgressBar: true,
								didOpen: (toast) => {
									toast.addEventListener('mouseenter', Swal.stopTimer)
									toast.addEventListener('mouseleave', Swal.resumeTimer)
								}
							})

							Toast.fire({
								type: 'warning',
								icon: 'warning',
								title: 'Mobile number not exist.',
								onClose: (toast) => {
									confirmMobileNumberPopUp1();
								}
							})

						} else {

							confirmAuthCode1(data);
						}
					}
				});
			}
			// }).catch(swal.noop)
		}

		function confirmAuthCode1(data) {

			swal({
				title: 'Enter Code',
				html: '<div class="row justify-content-center"> <div class="col-md-7"><input type="text" style="text-align:center;" required class="form-control phones1 authCode" autocomplete="off" style="height: 34px;" value=""></div></div>',
				showCancelButton: false,
				confirmButtonText: 'Submit',
				showConfirmButton: true,
				allowOutsideClick: false,
				allowEscapeKey: false,
				allowEnterKey: false,
				returnInputValueOnDeny: true,
				onOpen: function(el) {
					var container = $(el);
					container.find('.phones1').mask('999999');
				},
				preConfirm: function(value) {
					return new Promise(function(resolve, reject) {
						if ($(".authCode").val() === "") {
							swal.showValidationError(
								'Please enter valid code.'
							)
						}
						resolve([$('.authCode').val(), $('.authCode').val()])
					})
				}
			}).then(function(result) {
				var authCode = $('.authCode').val();
				var newCustomerId = data;

				var probizcaUserType = $('#shareType').val();
				// alert(customerId);

				if (authCode != '') {
					$.ajax({
						type: "GET",
						url: path + "/ajax/ajax_check_customer_auth_code.html",
						data: {
							authCode: authCode,
							newCustomerId: newCustomerId
						},
						success: function(data) {
							var newCustId = data;


							if (data == 0) {
								const Toast = Swal.mixin({
									toast: true,
									position: 'middle',
									showConfirmButton: false,
									timer: 3000,
									timerProgressBar: true,
									didOpen: (toast) => {
										toast.addEventListener('mouseenter', Swal.stopTimer)
										toast.addEventListener('mouseleave', Swal.resumeTimer)
									}
								})

								Toast.fire({
									type: 'warning',
									icon: 'warning',
									title: 'Invalid Code.Please Enter Valid Code.',
									onClose: (toast) => {
										confirmAuthCode1(newCustomerId);
									}
								})


							} else {
								//go to login process here
								$.ajax({
									type: "GET",
									url: path + "/ajax/ajax_login_to_customer.html",
									data: {
										newCustId: newCustId
									},
									success: function() {
										var viewProBizCaPath = path + '/mb/' + displayURLTag + '/' + btoa(probizcaUserType) + '/' + btoa(businessUserId) + '/' + btoa(newCustId) + '/?isRedirect=' + btoa(1);
										// alert(viewProBizCaPath)
										window.location.href = viewProBizCaPath;
									}
								});

							}
						}
					});
				}
			}).catch(swal.noop)
		}

		$(".phones").mask("(*************");

		//check user exist
		function confirmCustomerMobileNumber(refId, refType, businessId) {
			$("#startupModalfsgssd").hide();

			swal({
				title: 'Enter Mobile Number',
				html: '<div class="row justify-content-center"> <div class="col-md-7"><input type="tel" style="text-align:center;" class="form-control phones nDiscount_fixed" autocomplete="off" style="height: 34px;" value=""> </div> </div>',
				showCancelButton: false,
				confirmButtonText: 'Submit',
				showConfirmButton: true,
				allowOutsideClick: false,
				allowEscapeKey: false,
				allowEnterKey: false,
				returnInputValueOnDeny: true,
				onOpen: function(el) {
					var container = $(el);
					container.find('.phones').mask('(*************');
				},
				preConfirm: function(value) {
					return new Promise(function(resolve, reject) {
						if ($(".nDiscount_fixed").val() === "") {
							swal.showValidationError(
								'Please enter mobile number.'
							)
						}
						resolve([$('.refund_type').val(), $('.nDiscount_fixed').val()])
					})
				}
			}).then(function(result) {
				var mobileNumber = $('.nDiscount_fixed').val();
				var hiddenMobileNumber = $('#hiddenMobileNumber').val(mobileNumber);

				if (mobileNumber != '') {
					$.ajax({
						type: "GET",
						url: path + "/ajax/ajax_check_customer_mobile_number_exist.html",
						data: {
							mobileNumber: mobileNumber,
							refId: refId,
							refType: refType,
							businessId: businessId
						},
						success: function(data) {
							if (data == 0) {
								const Toast = Swal.mixin({
									toast: true,
									position: 'middle',
									showConfirmButton: false,
									timer: 3000,
									timerProgressBar: true,
									didOpen: (toast) => {
										toast.addEventListener('mouseenter', Swal.stopTimer)
										toast.addEventListener('mouseleave', Swal.resumeTimer)
									}
								})

								Toast.fire({
									type: 'warning',
									icon: 'warning',
									title: 'Mobile number not exist.',
									onClose: (toast) => {
										confirmMobileNumberPopUp1();
									}
								})

							} else {

								confirmAuthCode1(data);
							}
						}
					});
				}
			}).catch(swal.noop)
		}

		function myFunction() {
			var contactPhone = $('#phonea').val();
			document.getElementById("phnamespan").textContent = contactPhone;
		}

		$(document).ready(function() {
			updateVisitorsCount();

			var currentURL = window.location.href;
			var customerId = $("#customerId").val();
			splitCurrentURL = currentURL.split('=');
			var redirectUrl = currentURL.includes("isRedirect");
			// alert(redirectUrl);
			if (redirectUrl == true && customerId > 0) {
				$('#welcomeModal').modal('show', {
					backdrop: 'static',
					keyboard: false
				});
				// $("#phonea").mask("(*************");
			}


			$('#btnQRCodeFormSave').click(function() {
				// alert("Hello");
				var slug = $('#slug').val();
				var contactFirstName = $('#qrcodeForm').find('input[name="contactFirstName"]').val();
				if (contactFirstName == '') {
					$('#qrcodeForm').find('#contactFirstNameSpan').fadeIn().html("Please Enter first name.");
					setTimeout(function() {
						$('#qrcodeForm').find('#contactFirstNameSpan').fadeOut('slow')
					}, 5000);
					return false;
				}

				var contactLastName = $('#qrcodeForm').find("input[name=contactLastName]").val();
				if (contactLastName == '') {
					$('#qrcodeForm').find('#contactLastNameSpan').fadeIn().html("Please Enter last name.");
					setTimeout(function() {
						$('#qrcodeForm').find('#contactLastNameSpan').fadeOut('slow')
					}, 5000);
					return false;
				}

				var contactemail = $('#qrcodeForm').find("input[name=contactemails]").val();
				if (contactemail == '') {
					$('#qrcodeForm').find('#contactemailSpan').fadeIn().html("Please Enter email.");
					setTimeout(function() {
						$('#qrcodeForm').find('#contactemailSpan').fadeOut('slow')
					}, 5000);
					return false;
				}

				var contactphone = $('#qrcodeForm').find("input[name=contactphone]").val();
				if (contactphone == '') {
					$('#qrcodeForm').find('#contactphoneSpan').fadeIn().html("Please Enter Mobile number.");
					setTimeout(function() {
						$('#qrcodeForm').find('#contactphoneSpan').fadeOut('slow')
					}, 5000);
					return false;
				}


				// Check if the checkbox is checked
				var checkboxChecked = $('#qrcodeForm').find("input[name=flexCheckChecked]").is(":checked");
				if (!checkboxChecked) {
					// Handle the case when the checkbox is not checked
					// For example, show an error message or perform an action
					$('#qrcodeForm').find('#checkboxSpan').fadeIn().html("You must select the checkbox to proceed.");
					setTimeout(function() {
						$('#qrcodeForm').find('#checkboxSpan').fadeOut('slow')
					}, 5000);
					return false;
				}

				var customQrFormchk = '<?php echo $customQrFormchk; ?>';
				var org_BusinessQrForms = '<?php echo count($org_BusinessQrForms); ?>';
				var org_BusinessQrFormsIsActive = '<?php echo isset($org_BusinessQrForms[0]['isActive']) ? $org_BusinessQrForms[0]['isActive'] : 0; ?>';
				var org_BusinessQrFormsIsShowonQr = '<?php echo isset($org_BusinessQrForms[0]['isShowonQr']) ?  $org_BusinessQrForms[0]['isShowonQr'] : 0; ?>';

				if (customQrFormchk == 1 && org_BusinessQrForms > 0 && org_BusinessQrFormsIsActive == 1 && org_BusinessQrFormsIsShowonQr == 1) {
					// Dynamic Fields Validation
					var dynamicFieldsValid = true;

					// Check dynamically generated fields
					$('[name^="businessSectionFieldId["]').each(function() {
						var fieldId = $(this).attr('id');
						var fieldValue = $(this).val();
						// var fieldLabel = $('#label_' + fieldId).text(); // Retrieve the field label
						// var fieldLabel = $('label[for="for_' + fieldId + '"]').text(); // Retrieve the field label using the for attribute
						// alert(fieldLabel);
						// Check if the field is required
						if ($(this).attr('required') && fieldValue.trim() === '') {
							dynamicFieldsValid = false;
							var errorMessage = "This field is required";

							// Show error message in the corresponding span
							var errorSpanId = 'error_' + fieldId;
							// alert(errorSpanId);
							$('#' + errorSpanId).fadeIn().html(errorMessage);

							// Hide error message after a delay
							setTimeout(function() {
								$('#' + errorSpanId).fadeOut('slow');
							}, 5000);

							return false; // Exit the loop early if any field is invalid
						}
					});

					if (!dynamicFieldsValid) {
						return false; // Prevent form submission if dynamic fields are not valid
					}

				}

				// return false;
				$('#btnQRCodeFormSave').html('Please wait...<i class="fa fa-circle-o-notch fa-spin" aria-hidden="true"></i>');
				$('#btnQRCodeFormSave').attr('disabled', true);

				$.ajax({
					url: '<?php echo BASE_PATH; ?>/contactqrcodedetailssubmit.html',
					type: 'POST',
					data: $('#qrcodeForm').serialize(),
					success: function(responseObj) {
						var obj = jQuery.parseJSON(responseObj);


						if (obj.status == "alreadyExistBusiness") {
							$('#btnQRCodeFormSave').html('Submit');
							$('#btnQRCodeFormSave').attr('disabled', false);
							alertify.error("This email is already registered with a business.");

						} else if (obj.status == "success") {
							var path = '<?php echo BASE_PATH; ?>';
							// 			alertify.success("Registered successfully.");
							window.location.href = path + '/' + obj.directionURL;
							// alertify.success("Registered successfully.");
							// window.location.href = '<?php echo BASE_PATH; ?>/'+obj.directionURL;;
						}
					}
				});

			});

			$('.surveyPopup').click(function() {
				$('#welcomeModal').hide();
				$('#welcomeModal').removeClass('show');
			});

			$('#closePopup').click(function() {
				$('#welcomeModal').hide();
				$('.modal-backdrop').hide();
				$('.modalRomove').removeClass('modal-open');
				$('.modalRomove').attr('style', 'overflow:auto;');
			})

			$('.home-tooltip').mouseenter(function() {
				$('.mouse-hover-home').css('visibility', 'visible');
			});
			$('.home-tooltip').mouseleave(function() {
				$('.mouse-hover-home').css('visibility', 'hidden');
			});

			$('.copy-tooltip').mouseenter(function() {
				$('.mouse-hover-copy').css('visibility', 'visible');
			});
			$('.copy-tooltip').mouseleave(function() {
				$('.mouse-hover-copy').css('visibility', 'hidden');
			});
			$('.contact-tooltip').mouseenter(function() {
				$('.mouse-hover-contact').css('visibility', 'visible');
			});
			$('.contact-tooltip').mouseleave(function() {
				$('.mouse-hover-contact').css('visibility', 'hidden');
			});

			if (screen.width < 430) {
				console.log('hey')
				$('.share-popup-tag').css({
					'visibility': 'visible',
					'display': 'block'
				});
			} else {
				// console.log('hello')
				// $('.share-popup-tag').css({ 'visibility': 'hidden', 'display': 'none' });
			}


		});

		function updateVisitorsCount() {

			var visitor_ip = '<?php echo $visitor_ip; ?>';
			var visitor_browser = '<?php echo $visitor_browser; ?>';
			var visitor_hour = '<?php echo $visitor_hour; ?>';
			var visitor_minute = '<?php echo $visitor_minute; ?>';
			var visitor_date = '<?php echo $visitor_date; ?>';
			var visitor_day = '<?php echo $visitor_day; ?>';
			var visitor_month = '<?php echo $visitor_month; ?>';
			var visitor_year = '<?php echo $visitor_year; ?>';
			var visitor_referer = '<?php echo $visitor_referer; ?>';
			var visitor_page = '<?php echo $visitor_page; ?>';

			var loggedUserId = '<?php echo $loggedUserId; ?>';
			var loggedUserTypeId = '<?php echo $scustomerId; ?>';
			var refId = '<?php echo $refId; ?>';
			var shareType = '<?php echo $shareType; ?>';
			var currentBusinessId = '<?php echo $currentBusinessId; ?>';
			// alert("visitor_ip "+visitor_ip);
			// return false;

			$.ajax({
				type: "GET",
				url: path + "/ajax/ajax_updateVisitorsCount.html",
				data: {
					visitor_ip: visitor_ip,
					visitor_browser: visitor_browser,
					visitor_hour: visitor_hour,
					visitor_minute: visitor_minute,
					visitor_date: visitor_date,
					visitor_day: visitor_day,
					visitor_month: visitor_month,
					visitor_year: visitor_year,
					visitor_referer: visitor_referer,
					visitor_page: visitor_page,
					loggedUserId: loggedUserId,
					loggedUserTypeId: loggedUserTypeId,
					refId: refId,
					shareType: shareType,
					currentBusinessId: currentBusinessId,

				},
				dataType: "json",
				success: function(data) {
					console.log(data);
				}
			});

		}

		//check email already exist
		$("#contactemails").blur(function() {
			// alert('hi');
			var email = $("#contactemails").val();
			var businessId = $("#businessId").val();
			var contactemailsSpan = $("#contactemailsSpan");
			var pattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

			if (pattern.test(email)) {

				$.ajax({
					type: "GET",
					url: "<?php echo BASE_PATH; ?>/ajax/ajax_check_duplicate_customer_email.html",
					data: {
						email: email,
						businessId: businessId
					},
					success: function(data) {
						if (data > 0) {
							Swal.fire({
								type: "danger",
								title: 'Email already exist!',
								confirmButtonClass: 'btn btn-danger',
							})
							$("#contactemails").val('').focus();
							$("#btnSave").removeAttr("disabled", true);
						}
					}
				});
			} else {
				contactemailsSpan.text('Please enter a valid email address');
				$("#contactemails").val('');

				setTimeout(function() {
					$('#contactemailsSpan').fadeOut('slow')
				}, 5000);
			}
		});
	</script>

	<!-- custom QR form Next js -->
	<script>
		$(window).on('load', function() {
			$('.loader-container1').css('display', 'none');
		});

		var customQrFormchk = '<?php echo $customQrFormchk; ?>';
		var org_BusinessQrForms = '<?php echo count($org_BusinessQrForms); ?>';
		var org_BusinessQrFormsIsActive = '<?php echo isset($org_BusinessQrForms[0]['isActive']) ? $org_BusinessQrForms[0]['isActive'] : 0; ?>';
		var org_BusinessQrFormsIsShowonQr = '<?php echo isset($org_BusinessQrForms[0]['isShowonQr']) ?  $org_BusinessQrForms[0]['isShowonQr'] : 0 ?>';

		if (customQrFormchk == 1 && org_BusinessQrForms > 0 && org_BusinessQrFormsIsActive == 1 && org_BusinessQrFormsIsShowonQr == 1) {
			document.addEventListener("DOMContentLoaded", function() {
				const tabs = document.querySelectorAll(".nav-tabs li");
				const prevBtn = document.querySelector(".prev");
				const nextBtn = document.querySelector(".next");
				const submitBtn = document.getElementById("btnQRCodeFormSave");

				// Function to update button visibility and label
				function updateButtons() {
					const activeTab = document.querySelector(".tab-pane.active");
					const activeTabIndex = Array.from(activeTab.parentNode.children).indexOf(activeTab);

					prevBtn.style.display = activeTabIndex === 0 ? "none" : "flex";

					if (activeTabIndex === tabs.length - 1) {
						// Hide the "Next" button
						nextBtn.style.display = "none";

						// Show the "Submit" button if it exists
						if (submitBtn) {
							submitBtn.style.display = "flex";
						}
					} else {
						// If not on the last tab, show the "Next" button
						nextBtn.style.display = "flex";

						// Hide the "Submit" button if it exists
						if (submitBtn) {
							submitBtn.style.display = "none";
						}
					}
				}

				// Event listener for the "Next" button
				nextBtn.addEventListener("click", function() {
					const activeTab = document.querySelector(".tab-pane.active");
					const nextTab = activeTab.nextElementSibling;

					var slug = $('#slug').val();
					var contactFirstName = $('#qrcodeForm').find('input[name="contactFirstName"]').val();
					if (contactFirstName == '') {
						$('#qrcodeForm').find('#contactFirstNameSpan').fadeIn().html("Please Enter first name.");
						setTimeout(function() {
							$('#qrcodeForm').find('#contactFirstNameSpan').fadeOut('slow')
						}, 5000);
						return false;
					}

					var contactLastName = $('#qrcodeForm').find("input[name=contactLastName]").val();
					if (contactLastName == '') {
						$('#qrcodeForm').find('#contactLastNameSpan').fadeIn().html("Please Enter last name.");
						setTimeout(function() {
							$('#qrcodeForm').find('#contactLastNameSpan').fadeOut('slow')
						}, 5000);
						return false;
					}

					var contactemail = $('#qrcodeForm').find("input[name=contactemails]").val();
					if (contactemail == '') {
						$('#qrcodeForm').find('#contactemailSpan').fadeIn().html("Please Enter email.");
						setTimeout(function() {
							$('#qrcodeForm').find('#contactemailSpan').fadeOut('slow')
						}, 5000);
						return false;
					}

					var contactphone = $('#qrcodeForm').find("input[name=contactphone]").val();
					if (contactphone == '') {
						$('#qrcodeForm').find('#contactphoneSpan').fadeIn().html("Please Enter Mobile number.");
						setTimeout(function() {
							$('#qrcodeForm').find('#contactphoneSpan').fadeOut('slow')
						}, 5000);
						return false;
					}


					// Check if the checkbox is checked
					var checkboxChecked = $('#qrcodeForm').find("input[name=flexCheckChecked]").is(":checked");
					if (!checkboxChecked) {
						// Handle the case when the checkbox is not checked
						// For example, show an error message or perform an action
						$('#qrcodeForm').find('#checkboxSpan').fadeIn().html("You must select the checkbox to proceed.");
						setTimeout(function() {
							$('#qrcodeForm').find('#checkboxSpan').fadeOut('slow')
						}, 5000);
						return false;
					}

					if (nextTab) {
						activeTab.classList.remove("active", "show");
						nextTab.classList.add("active", "show");
						updateButtons();
					}
				});

				// Event listener for the "Previous" button
				prevBtn.addEventListener("click", function() {
					const activeTab = document.querySelector(".tab-pane.active");
					const prevTab = activeTab.previousElementSibling;

					if (prevTab) {
						activeTab.classList.remove("active", "show");
						prevTab.classList.add("active", "show");
						updateButtons();
					}
				});

				// Initial button setup
				updateButtons();
			});
		}
	</script>
	<!-- custom QR form Next js End-->
</body>

</html>