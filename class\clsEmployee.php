<?php
class clsEmployee
{	
	
	var $businessId  = 0;
	var $firstName  = '';
	var $lastName  = '';
	var $phoneNo  = '';
	var $address  = '';
	var $address2  = '';
	var $city  = '';
	var $zipCode  = '';
	var $stateId  = 0;
	var $isIndependent  = 0;
	var $birthDate  = '';
	var $email  = '';
	
	function SaveEmployee($employeeId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		if($employeeId > 0)
		{
			$sql = "UPDATE employee SET 													
							firstName = '".addslashes($this->firstName)."',																
							lastName = '".addslashes($this->lastName)."',																
							phoneNo = '".addslashes($this->phoneNo)."',																
							address = '".addslashes($this->address)."',																
							address2 = '".addslashes($this->address2)."',																
							city = '".addslashes($this->city)."',																
							zipCode = '".addslashes($this->zipCode)."',																
							stateId = ".$this->stateId.",															
							isIndependent = ".$this->isIndependent.",															
							birthDate = '".$this->birthDate."',															
							email = '".$this->email."'															
							WHERE employeeId= ".$employeeId;	
										// echo $sql;exit;				
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
			$sql = "INSERT INTO employee(firstName,lastName,email,phoneNo,address,address2,city,zipCode,stateId,isIndependent,
			                    businessId) 
								VALUES(								  				
									  '".addslashes($this->firstName)."',								
									  '".addslashes($this->lastName)."',								
									  '".addslashes($this->email)."',								
									  '".addslashes($this->phoneNo)."',								
									  '".addslashes($this->address)."',								
									  '".addslashes($this->address2)."',								
									  '".addslashes($this->city)."',								
									  '".addslashes($this->zipCode)."',								
									  ".$this->stateId.",								
									  ".$this->isIndependent.",								
									  ".$this->businessId."							
									  )";	
// echo $sql;exit;									  
			$employeeId = $objDB->ExecuteInsertQuery($sql);
		}
		
		unset($objDB);
		return $employeeId;
	}
	
	function GetEmployeeDetails($employeeId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE employeeId = '".$employeeId."'";
		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetEmployeeDetailByBussiness($businessId,$email)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT employee.*,business.businessName,business.logoName FROM employee 
		inner join business on business.businessId = employee.businessId ";
		if($businessId > 0 && $email !='')
		{
			$sql.="WHERE employee.businessId = '".$businessId."' AND employee.email= '".$email."'";
		}
		else
		{
			$sql.="WHERE employee.email= '".$email."'";

		}
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function DeleteEmployee($employeeId)
	{		
		$result="";
		if($employeeId > 0)
		{			
			$objDB = new clsDB();
			$sql = 'DELETE FROM employee WHERE employeeId='.$employeeId;		
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} 
	
	function GetAllEmployee($status = -1, $businessId,$isPrimary)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee ";
        if($businessId!='')
		{
		   $sql .=" WHERE businessId='".$businessId."'";
		}  		
		if($status !=-1)
		{
		   $sql .=" AND status='".$status."'";
		} 
        if($isPrimary !='')
		{
		   $sql .=" WHERE isPrimary='".$isPrimary."'";
		} 		
		
		$sql .=" ORDER BY firstName ASC";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function SetEmployeeStatus($id, $status)
	{
		if($id > 0)
		{
			$objDB = new clsDB();
			$sql = "Update employee set status =".$status." Where employeeId = ".$id; 
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	
	function CheckDuplicateUserEmail($email,$businessId)
	{
		$employeeIds = 0;
		$objDB = new clsDB();
		$sql = "SELECT employeeId  FROM employee WHERE email = '".($email)."' AND businessId='".$businessId."'";
		$dataRow = $objDB->GetDataRow($sql);
		if($dataRow)
		{
			$employeeIds = $dataRow['employeeId'];
		}
		// echo $sql; exit;
		unset($objDB);
		return $employeeIds;
	}
	
	function CheckEmployeeLogin($email,$isBusinessOwner=0)
	{
		$employeeId = "0";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE email='".$email."' AND status=1";
		if($isBusinessOwner)
		{
			$sql.= " AND isPrimary=$isBusinessOwner";
		}
		
		// echo $sql;exit; 
		// and isPrimaryUser=1
		$employeeId = $objDB->GetDataRow($sql);
		unset($objDB);
		return $employeeId;
	}
	
	function GetBusinessOwnerDetails($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE businessId = ".$businessId." AND isPrimary='1'";
		// echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function SaveEmployeeDetails($employeeId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		if($employeeId > 0)
		{
			$sql = "UPDATE employee SET 													
							empStartDate = '".addslashes($this->empStartDate)."',																
							empTitle = '".addslashes($this->empTitle)."',																
							empNotes = '".addslashes($this->empNotes)."'														
							WHERE employeeId= ".$employeeId;	
										// echo $sql;exit;				
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
			$sql = "INSERT INTO employee(empStartDate,empTitle,empNotes) 
								VALUES(								  				
									  '".addslashes($this->empStartDate)."',								
									  '".addslashes($this->empTitle)."',								
									  '".addslashes($this->empNotes)."'					
									  )";	
// echo $sql;exit;									  
			$employeeId = $objDB->ExecuteInsertQuery($sql);
		}
		
		unset($objDB);
		return $employeeId;
	}


	function getPrimaryBusinessDetail($email)
	{
		$employeeId = "0";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee 
		inner join business on business.businessId = employee.businessId
		WHERE employee.email='".$email."' and isPrimaryUser=1";
		$employeeId = $objDB->GetDataRow($sql);
		unset($objDB);
		return $employeeId;
	}
	
}
?>