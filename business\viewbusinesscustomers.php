<?php
include('includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsBusiness.php');
include('../class/clsBusinessGroup.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsBusinessPlanMaster.php');
include('../class/clsCustomer.php');
include('../class/clsBusinessProBizCa.php');

//variables
$customerData = '';
$businessName = '';
$isFavorite = 0;
$isFavorites = 0;
$businessId = $_SESSION["loggedBusinessId"];
//objects
$objBusiness = new clsBusiness();
$objBusinessGroup = new clsBusinessGroup();
$objCustomer = new clsCustomer();

$objBusinessPlanMaster = new clsBusinessPlanMaster();
$businessPlan = $objBusinessPlanMaster->GetBusinessPlanDetails($businessId, $planId = 0);
if ($businessPlan != '') {
	$isCrmIntergration = stripslashes($businessPlan['isCrmIntergration']);
	$isCouponServices = stripslashes($businessPlan['isCouponServices']);
	$pushNotification = stripslashes($businessPlan['pushNotification']);
	$chatRoom = stripslashes($businessPlan['chatRoom']);
	$shoppingCart = stripslashes($businessPlan['shoppingCart']);
	$email = stripslashes($businessPlan['email']);
	$geolocation = stripslashes($businessPlan['geolocation']);
	$socialfeed = stripslashes($businessPlan['socialfeed']);
	$statistics = stripslashes($businessPlan['statistics']);
	$support = stripslashes($businessPlan['support']);
	$todo = stripslashes($businessPlan['todo']);
	$probizcaplus = stripslashes($businessPlan['probizcaplus']);
	$invoice = stripslashes($businessPlan['invoice']);
	$contactLimit = stripslashes($businessPlan['contactLimit']);
}

//get gorup data
$groupData = $objBusinessGroup->GetAllBusinessGroup($status = -1, $businessId);
// unset($objBusinessGroup);

if (isset($_GET['id'])) {
	$isFavorite = DecodeQueryData($_GET['id']);
	$isFavorites = ($_GET['id']);

	if ($isFavorite == 3) {
		$customerData = $objBusiness->GetBusinessCustomerList($businessId, $isFavoriteId = 1, $customerId = '');
		$customerCount = mysqli_num_rows($customerData);

	}
	if ($isFavorite == 2) {
		$customerData = $objBusiness->GetNewContacts($businessId);
		$customerCount = mysqli_num_rows($customerData);

	}
	if ($isFavorite == 1) {
		$customerData = $objBusiness->GetBusinessCustomerList($businessId, $isFavoriteId = '', $customerId = '');
		$customerCount = mysqli_num_rows($customerData);

	}

	if ($isFavorite == 1 || $isFavorite == 0) {
		$favoriteTitle = 'All Contacts';
	} elseif ($isFavorite == 2) {
		$favoriteTitle = 'New Contacts';
	} elseif ($isFavorite == 3) {
		$favoriteTitle = 'Favorite Contacts';
	} else {
		$favoriteTitle = 'All Contacts';
	}
	// echo $isFavorite;exit;
} else {
	$favoriteTitle = 'All Contacts';
	//get employee data				
	$customerData = $objBusiness->GetBusinessCustomerList($businessId, $isFavoriteId = '', $customerId = '');
	$customerCount = mysqli_num_rows($customerData);

}



//get business name 		       	
$businessName  = $objBusiness->GetBusinessName($businessId);
unset($objBusiness);
// echo   $isFavorite;exit;


?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
	<title>Contacts</title>
	<?php include('includes/headerCss.php'); ?>
	<?php include('includes/datatableCss.php'); ?>
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/animate/animate.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/extensions/sweetalert2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/pages/app-contacts.css">


</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

	<!-- include header-->
	<?php include('includes/header.php'); ?>
	<div class="app-content content">
		<div class="content-overlay"></div>
		<div class="content-wrapper">
			<div class="content-header row"></div>
			<form id="businessCustomerForm" action="exportbusinesscustomerdata.html" method="POST" enctype="multipart/form-data">
				<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
				<input type="hidden" name="isFavorite" id="isFavorite" value="<?php echo ($isFavorites); ?>">
				<input type="hidden" name="isFavorites" id="isFavorites" value="<?php echo ($isFavorite); ?>">

				<div class="row">
					<!-- Sidebar -->
					<div class="col-lg-3 col-md-4 order-2 order-md-1 mb-2 mb-md-0">
						<div class="sidebar">
							<div class="bug-list-sidebar-content">
								<!-- Predefined Views -->
								<div class="card">
									<div class="card-head">
										<div class="media p-1">
											<div class="media-left pr-1"><span class="avatar avatar-sm avatar-online rounded-circle"><img src="<?php echo ($_SESSION["loggedBusinessOwnerImage"]); ?>" alt="avatar"><i></i></span></div>
											<div class="media-body media-middle">
												<h5 class="media-heading"><?php echo ($_SESSION["loggedBusinessOwnerName"]); ?></h5>
											</div>
										</div>
									</div>
									<!-- contacts view -->
									<div class="card-body border-top-blue-grey border-top-lighten-5">
										<div class="list-group">
											<a href="javascript:void(0);" class="list-group-item allContacts <?php if ($isFavorite == 1 || $isFavorite == 0) { ?> active <?php } else { ?> inactive <?php } ?>" leftNavId="<?php echo EncodeQueryData(1); ?>">All Contacts</a>
											<a href="javascript:void(0);" class="list-group-item list-group-item-action newContacts<?php if ($isFavorite == 2) { ?> active <?php } else { ?> inactive <?php } ?>" leftNavId="<?php echo EncodeQueryData(2); ?>">New Contacts</a>
											<a href="javascript:void(0);" class="list-group-item list-group-item-action favoriteContacts <?php if ($isFavorite == 3) { ?> active <?php } ?>" leftNavId="<?php echo EncodeQueryData(3); ?>">Favorite Contacts</a>
										</div>
									</div>
									<!-- Groups-->
									<div class="card-body">
										<p class="lead">Groups <a href="changegroups.html"> <span class="badge badge-success btn-glow badge-pill float-right">Move</span></a><a href="businessgroups.html"> <span class="badge badge-success btn-glow badge-pill float-right">View/Add</span></a></p>
										<ul class="list-group" style="max-height: 310px;overflow-y: auto;">
											<?php
											$businessCustomerCount = 0;
											foreach ($groupData as $row) {
												$businessGroupId = $row['businessGroupId'];
												$title = $row['title'];
												$status = ($row['status']);
												$employeeId = ($row['employeeId']);
												$employeeName = "";
												if($employeeId)
												{
													$objBusiness = new clsBusiness();
													$employeeRows = $objBusiness->GetBusinessEmployeeName($employeeId);
													$employeeName = '- '.$employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
												}
												//get count group customers 
												$objBusinessGroup = new clsBusinessGroup();
												$businessCustomerCount = $objBusinessGroup->GetBusinessGroupContactCount($businessGroupId);
											?>
												<li class="list-group-item">
													<a href="businessgroupscontacts.html?id=<?php echo EncodeQueryData($businessGroupId); ?>"> <span class="badge badge-primary badge-pill float-right"><?php echo ($businessCustomerCount); ?></span><span style="font-size: 16px;"><?php echo ($title); ?></span></a><br>
													<span style="color: green;font-size: 13px;"><?php echo ($employeeName); ?></span>
												</li>
											<?php
											}
											?>
										</ul>
									</div>
									<!--/ Groups-->
									<!--More-->
									<?php 
										if($isCrmIntergration != 1)
										{
											$msg= "Item is not in your Subscription Plan. Please upgrade your subscription to continue";
										}
										else if($contactLimit == $customerCount) 
										{
											$msg= "Your contact limit has been reached. Please upgrade your subscription to continue";
										}
									?>
									<div class="card-body">
										<p class="lead">More</p>
										<ul class="list-group list-unstyled">
											<li class="list-group-item"><a <?php if ($isCrmIntergration == 1 && $contactLimit != $customerCount) { ?> href="viewcontactimportpopup.html" class="viewNotificationPopup media-heading name" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="<?php echo $msg; ?>" data-bg-color="warning" <?php } ?>>Import</a></li>
											<li class="list-group-item"><a href="javascript:void(0);" class="exportClass">Export</a></li>
											<li class="list-group-item"><a href="exportbusinesscustomerdatapdf.html">Print</a></li>
										</ul>
									</div>
									<!--/More-->
								</div>
								<!--/ Predefined Views -->
							</div>
						</div>
					</div>
					<!-- /Sidebar -->

					<!-- Main Content -->
					<div class="col-lg-9 col-md-8 order-1 order-md-2">
						<div class="content-body">
							<div class="content-overlay"></div>
							<!--- show status messages --->
							<?php
							if (isset($_GET["status"])) {
								if ($_GET["status"] == "added") {
							?>
									<div class="alert alert-success alert-dismissible" role="alert">
										<button type="button" class="close" data-dismiss="alert" aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
										<strong>Added.</strong>
									</div>
								<?php
								} else if ($_GET["status"] == "updated") {
								?>
									<div class="alert alert-success alert-dismissible" role="alert">
										<button type="button" class="close" data-dismiss="alert" aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
										<strong>Updated.</strong>
									</div>
								<?php
								} else if ($_GET["status"] == "limitOver") {
								?>
									<div class="alert alert-success alert-dismissible" role="alert">
										<button type="button" class="close" data-dismiss="alert" aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
										<strong>Contact Limit is Reached. Please Upgrade your Subscription to Continue.</strong>
									</div>
								<?php
								} else if ($_GET["status"] == "Imported") {
								?>
									<div class="alert alert-success alert-dismissible" role="alert">
										<button type="button" class="close" data-dismiss="alert" aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
										<strong>Imported.</strong>
									</div>
								<?php
								} else if ($_GET["status"] == "error") {
								?>
									<div class="alert alert-danger alert-dismissible" role="alert">
										<button type="button" class="close" data-dismiss="alert" aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
										<strong><?php echo $_GET["status"]; ?></strong>
									</div>
								<?php
								} else if ($_GET["status"] == "duplicatefile") {
								?>
									<div class="alert alert-danger alert-dismissible" role="alert">
										<button type="button" class="close" data-dismiss="alert" aria-label="Close">
											<span aria-hidden="true">&times;</span>
										</button>
										<strong><?php echo $_GET["status"]; ?></strong>
									</div>
							<?php
								}
							}
							?>
							<section class="row all-contacts">
								<div class="col-12">
									<div class="card">
										<div class="card-head">
											<div class="card-header">
												<h4 class="card-title"><?php echo ($favoriteTitle); ?></h4>
												<div class="heading-elements mt-0">
												<div class="modal fade" id="AddContactModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1" aria-hidden="true">
													<div class="modal-dialog" role="document">
														<div class="modal-content">
															<section class="contact-form">
																<form id="form-add-contact" class="contact-input">
																	<div class="modal-header">
																		<h5 class="modal-title" id="exampleModalLabel1">Add New Contact</h5>
																		<button type="button" class="close" data-dismiss="modal" aria-label="Close">
																			<span aria-hidden="true">&times;</span>
																		</button>
												</div>
																	<div class="modal-body">
																		<fieldset class="form-group col-12">
																			<input type="text" id="contact-name" class="contact-name form-control" placeholder="Name">
																		</fieldset>
																		<fieldset class="form-group col-12">
																			<input type="text" id="contact-email" class="contact-email form-control" placeholder="Email">
																		</fieldset>
																		<fieldset class="form-group col-12">
																			<input type="text" id="contact-phone" class="contact-phone form-control" placeholder="Phone Number">
																		</fieldset>
																		<fieldset class="form-group col-12">
																			<div class="custom-control custom-checkbox">
																				<input type="checkbox" class="custom-control-input" id="checkboxsmallallq">
																				<label class="custom-control-label" for="checkboxsmallallq">favorite</label>
																			</div>
																		</fieldset>
																		<fieldset class="form-group col-12">
																			<input type="file" class="form-control-file" id="user-image">
																		</fieldset>
																	</div>
																	<div class="modal-footer">
																		<fieldset class="form-group position-relative has-icon-left mb-0">
																			<button type="button" id="add-contact-item" class="btn btn-info add-contact-item" data-dismiss="modal"><i class="la la-paper-plane-o d-block d-lg-none"></i> <span class="d-none d-lg-block">Add New</span></button>
																		</fieldset>
																	</div>
																</form>
															</section>
														</div>
													</div>
												</div>
												<div class="modal fade" id="EditContactModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
													<div class="modal-dialog" role="document">
														<div class="modal-content">
															<section class="contact-form">
																<form id="form-edit-contact" class="contact-input">
																	<div class="modal-header">
																		<h5 class="modal-title" id="exampleModalLabel">Edit Contact</h5>
																		<button type="button" class="close" data-dismiss="modal" aria-label="Close">
																			<span aria-hidden="true">&times;</span>
																		</button>
																	</div>
																	<div class="modal-body">
																		<fieldset class="form-group col-12">
																			<input type="text" id="name" class="name form-control" placeholder="Name">
																		</fieldset>
																		<fieldset class="form-group col-12">
																			<input type="text" id="email" class="email form-control" placeholder="Email">
																		</fieldset>
																		<fieldset class="form-group col-12">
																			<input type="text" id="phone" class="phone form-control" placeholder="Phone Number">
																		</fieldset>
																		<span id="fav" class="d-none"></span>
																	</div>
																	<div class="modal-footer">
																		<fieldset class="form-group position-relative has-icon-left mb-0">
																			<button type="button" id="edit-contact-item" class="btn btn-info edit-contact-item" data-dismiss="modal"><i class="la la-paper-plane-o d-lg-none"></i> <span class="d-none d-lg-block">Edit</span></button>
																		</fieldset>
																	</div>
																</form>
															</section>
														</div>
													</div>
												</div>
												<span class="dropdown">
													<button id="btnSearchDrop1" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" class="btn btn-warning dropdown-toggle dropdown-menu-right btn-sm"><i class="ft-download-cloud white"></i></button>
													<span aria-labelledby="btnSearchDrop1" class="dropdown-menu mt-1 dropdown-menu-right">
														<!---a href="#" class="dropdown-item"><i class="ft-upload"></i> Import</a--->
														<a href="javascript:void(0);" class="exportClass dropdown-item"><i href="javascript:void(0);" class="ft-download"></i> Export</a>
														<a href="#" class="dropdown-item"><i class="ft-shuffle"></i> Find Duplicate</a>
													</span>
												</span>
												<button class="btn btn-default btn-sm"><i class="ft-settings white"></i></button>
											</div>
											</div>
										</div>
										<div class="card-content">
											<div class="card-body">
												<!-- Task List table -->
												<?php if ($_SESSION["isPrimary"] == 1 && $isFavorite == 0 || $isFavorite == 1) { ?>
													<button type="button" businessId="<?php echo EncodeQueryData($businessId); ?>" style="display:none;" class="deleteAll btn btn-sm btn-danger delete-all mb-1">Delete All</button>
												<?php } ?>
												<div class="table-responsive">
													<table id="contactTable" class="table table-white-space table-bordered row-grouping display no-wrap icheck table-middle text-center" width="100%">
														<thead>
															<tr>
																<th>Name</th>
																<th class="text-left">Contact</th>
																<th class="text-left">Referred By</th>
																<th>Favorite</th>
																<th>Actions</th>
															</tr>
														</thead>
														<tbody>
															<?php /* ... keep your table row code ... */ ?>
															<?php
															if ($customerData != '') {
															$objCountryStateMaster = new clsCountryStateMaster();
															$objBusiness = new clsBusiness();
															$obj = new clsDB();

															$businessName = '';
															$shareName = '';
															$shareTypeHeading = '';
															while ($row = mysqli_fetch_assoc($customerData)) {
																$customerId = $row['customerId'];
																$firstName = $row['firstName'];
																$lastName = ($row['lastName']);
																$email = stripslashes($row['email']);
																$address = ($row['address']);
																$status = ($row['status']);
																$phoneNo = ($row['phoneNo']);
																$city = ($row['city']);
																$zipCode = ($row['zipCode']);
																$stateId = ($row['stateId']);
																$businessId = ($row['businessId']);
																$shareType = ($row['shareType']);
																$affiliateId = ($row['affiliateId']);
																$employeeId = ($row['employeeId']);
																$createdDate = ($row['createdDate']);
																$isFavorites = ($row['isFavorite']);
																$isDisplayPaidContent = ($row['isDisplayPaidContent']);
																$refType = ($row['refType']);
																$refId = ($row['refId']);
																$formId = ($row['formId']);

																// echo "refType=" . $refType;
																// echo " ";
																// echo "refId=" . $refId;
																// echo "<br>";

																// $customQRForm = $formId ? ' / custom QR Form' : '';

																$customerBusinessGroupId = $obj->GetSingleColumnValueFromTable('businessgroupdetails', 'businessGroupId', 'customerId', $customerId);
																$type = $customerId;

																if ($affiliateId > 0) {
																	$affiliateDtls = $objBusiness->GetBusinessAffiliateName($affiliateId);
																	$affiliaateName = $affiliateDtls['firstName'] . ' ' . $affiliateDtls['lastName'];
																}

																if ($employeeId > 0) {
																	$empName = '';
																	$employeeRows = $objBusiness->GetBusinessEmployeeName($employeeId);
																	// if($employeeRows['isPrimary'] == 1)
																	// 	{
																	$empName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	// }

																}

																$style = '';
																//share by name 
																if ($refType == 0) {
																	$shareName = $_SESSION['loggedBusinessOwnerName'];
																	if ($shareType == 0) {
																		$shareTypeHeading = 'Invoice';
																	}
																	if ($shareType == 1) {
																		$shareTypeHeading = 'SMS';
																	}
																	if ($shareType == 2) {
																		$shareTypeHeading = 'QR Code';
																	}
																	if ($shareType == 3) {
																		$shareTypeHeading = 'Email';
																	}
																	if ($shareType == 4) {
																		$shareTypeHeading = 'NFC';
																	}
																	if ($shareType == 5) {
																		$shareTypeHeading = 'Import';
																	}
																	if ($shareType == 6) {
																		$shareTypeHeading = 'Import/SMS';
																	}
																	if ($shareType == 7) {
																		$shareTypeHeading = 'Import/Email';
																	}
																	if ($shareType == 8) {
																		$shareTypeHeading = 'Marketplace';
																	}
																	if ($shareType == 10) {
																		$shareTypeHeading = 'Form';
																	}

																	if ($employeeId == $refId) {
																		$shareDtls = $objBusiness->GetBusinessEmployeeName($refId);
																	} else {
																		$shareDtls = $objCustomer->GetCustomerDetails($refId);
																	}
																	$shareName = $shareDtls['firstName'] . ' ' . $shareDtls['lastName'];
																}
																if ($refType == 1) {
																	if ($shareType == 1) {
																		$shareTypeHeading = 'SMS';
																	}
																	if ($shareType == 2) {
																		$shareTypeHeading = 'QR Code';
																	}
																	if ($shareType == 3) {
																		$shareTypeHeading = 'Email';
																	}

																	$customerRows = $objCustomer->GetCustomerDetails($refId);
																	$shareName = $customerRows ? $customerRows['firstName'] . ' ' . $customerRows['lastName'] : '';
																}
																if ($refType == 2) {
																	$employeeRows = $objBusiness->GetBusinessEmployeeName($refId);
																	if ($employeeRows) {
																		$shareName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	} else {
																		$employeeRows = $objCustomer->GetCustomerDetails($refId);
																		$shareName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	}
																	$style = "color: green;font-weight: 600;";

																	if ($shareType == 0 || $shareType == 1 || $shareType == 2 || $shareType == 3 || $shareType == 4 || $shareType == 5 || $shareType == 6 || $shareType == 7 || $shareType == 8 || $shareType == 9 || $shareType == 10) {
																		if ($employeeId == $refId) {
																			$shareTypeHeading = 'Employee';
																		} else {
																			$shareTypeHeading = $empName;
																		}
																	}
																}
																if ($refType == 3) {
																	if ($affiliateId == $refId) {
																		$shareDtls = $objBusiness->GetBusinessAffiliateName($affiliateId);
																		$shareTypeHeading = 'Affiliate';
																	} else {
																		$shareDtls = $objCustomer->GetCustomerDetails($refId);
																		$shareTypeHeading = $affiliaateName;
																	}
																	$shareName = $shareDtls['firstName'] . ' ' . $shareDtls['lastName'];

																	$style = "color: red;font-weight: 600;";
																}
																if ($refType == 4) {
																	// echo "hi";
																	$objBusinessProBizCa = new clsBusinessProBizCa();
																	$affiliateRows = $objBusinessProBizCa->GetBusinessFormDetails($refId);
																	$shareName = $affiliateRows['formTitle'];
																	unset($objBusinessProBizCa);
																	if ($shareType == 10) {
																		$shareTypeHeading = 'Form';
																	}
																}

																if ($refType == 5) {
																	$employeeRows = $objBusiness->GetBusinessEmployeeName($refId);
																	if ($employeeRows) {
																		$shareName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	} else {
																		$employeeRows = $objCustomer->GetCustomerDetails($refId);
																		$shareName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	}
																	$style = "color: blue;font-weight: 600;";

																	if ($shareType == 0 || $shareType == 1 || $shareType == 2 || $shareType == 3 || $shareType == 4 || $shareType == 5 || $shareType == 6 || $shareType == 7 || $shareType == 8 || $shareType == 9 || $shareType == 10) {
																		if ($employeeId == $refId) {
																			$shareTypeHeading = 'Independent';
																		} else {
																			$shareTypeHeading = $empName;
																		}
																	}
																}

																if ($refType == 6) {
																	$employeeRows = $objBusiness->GetBusinessEmployeeName($refId);
																	if ($employeeRows) {
																		$shareName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	} else {
																		$employeeRows = $objCustomer->GetCustomerDetails($refId);
																		$shareName = $employeeRows['firstName'] . ' ' . $employeeRows['lastName'];
																	}
																	$style = " ";

																	$shareTypeHeading = 'Custom QR Form';

																	
																}

																if ($isFavorites == 1) {
																	$favoriteClass = 'active';
																}
																if ($isFavorites == 0) {
																	$favoriteClass = 'inactive';
																}

																//share type heading 

																// if($shareType==9)
																// {

																// 	$shareTypeHeading = $empName;

																// }


																// if($shareType==11)
																// {	

																// }
																//customer join date
																$startDate = str_replace("-", "/", $createdDate);
																$joinDate =  date('F d, Y', strtotime($startDate));

																//get state name
																$state  = $objCountryStateMaster->GetLocationName($stateId);
																$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
																$country = $objCountryStateMaster->GetLocationName($dbCountryId);

																$contactInfo = "";

																if ($phoneNo != '') {
																	$contactInfo .= '<a href="tel:' . $phoneNo . '" target="_blank"><b>Phone No :</b> ' . $phoneNo . '</a>';
																}
																if ($email != '') {
																	$contactInfo .= '<br><b>Email :</b> ' . $email;
																}

																$fullAddress = '';
																if ($address != '') {
																	$fullAddress .= $address . ',';
																}
																if ($city != '') {
																	$fullAddress .= '<br> ' . $city . ',';
																}
																if ($zipCode != '') {
																	$fullAddress .= '&nbsp  ' . $zipCode;
																}
																if ($state != '') {
																	$fullAddress .= '<br> ' . $state;
																}
																if ($country != '') {
																	$fullAddress .= ' - ' . $country;
																}

																//status
																if ($status == 0) {
																	$currntStatus = 'Inactive';
																	$btnClass = "btn btn-sm btn-outline-secondary btn-glow";
																} else {
																	$currntStatus = 'Active';
																	$btnClass = "btn btn-sm btn-outline-success btn-glow";
																}

																//get customer image path here
																$customerImage  = GetCustomerImagePath($customerId, $row['imageName']);
														?>
																<tr>
																	<td>
																		<div class="media">
																			<div class="media-left pr-1"><span class="avatar avatar-sm avatar-online rounded-circle"><img src="<?php echo ($customerImage); ?>" alt="avatar"><i></i></span></div>
																			<div class="media-body media-middle">
																				<a <?php if ($isCrmIntergration == 1) { ?> href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>" class="media-heading name" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" data-bg-color="warning" <?php } ?>>
																				<?php echo ($firstName . ' ' . $lastName); ?></a><br />
																				<a <?php if ($isCrmIntergration == 1) { ?> href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>" class="media-heading media-middle ml-2" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" data-bg-color="warning" <?php } ?>><?php echo ($joinDate); ?></a>
																			</div>

																		</div>
																	</td>
																	<td class="text-left">
																		<?php echo ($contactInfo); ?>
																	</td>
																	<td class="text-left">
																		<b>Name:</b> <span> <?php echo ($shareName); ?></span><br />
																		<b>Share Type:</b> <span style="<?php echo $style; ?>"> <?php echo ($shareTypeHeading); ?> </span>
																	</td>
																	<td class="text-center">
																		<div class="favorite <?php echo ($favoriteClass); ?>" id="isFavorite_<?php echo ($customerId); ?>" customerId="<?php echo EncodeQueryData($customerId); ?>" isStatus="<?php echo ($isFavorites); ?>"></div>
																	</td>
																	<td>
																		<a class="primary delete mr-1" <?php if ($isCrmIntergration == 1) { ?> href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="CRM" data-bg-color="blue" <?php } else { ?> data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" data-bg-color="warning" <?php } ?>><i class="la la-suitcase"></i></a>
																		<?php if ($isFavorite == 0 || $isFavorite == 3 || $isFavorite == 1) { ?>
																			<a href="javascript:void(0)" class="sendLoginDeatils mr-1 mb-1" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Send Email" data-bg-color="blue" customerId="<?php echo EncodeQueryData($customerId); ?>" businessLogo="<?php echo $loggedBusinessLogo; ?>"><i class="ft-mail"></i></a>
																			<a href="paidcontentpopup.html?custid=<?php echo ($customerId); ?>" class='viewNotificationPopup mr-1' customerId="<?php echo EncodeQueryData($customerId); ?>" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Paid" data-bg-color="orange" isDisplayPaidContent='<?php echo $isDisplayPaidContent; ?>' id="isDisplayPaidContent_<?php echo EncodeQueryData($customerId); ?>" businessId="<?php echo EncodeQueryData($businessId); ?>"><i class="ft-credit-card" id="icon_<?php echo EncodeQueryData($customerId); ?>" style='color:<?php if ($isDisplayPaidContent > 0) { echo  "#28d094";	} else { echo "red"; } ?>;'></i></a>
																			<a href="addcustomer.html?id=<?php echo EncodeQueryData($customerId); ?>" data-target="#EditContactModal" class="primary edit mr-1" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Edit" data-bg-color="purple"><i class="la la-pencil"></i></a>
																			<a class="deleteAjaxRow danger delete mr-1" customerId="<?php echo EncodeQueryData($customerId); ?>" businessId="<?php echo EncodeQueryData($businessId); ?>" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Delete" data-bg-color="red" businessId="<?php echo EncodeQueryData($businessId); ?>"><i class="la la-trash-o"></i></a>
																		<?php }
																		if ($isFavorite == 2) { ?> <!-- || $isFavorite==0 ||  $isFavorite==1-->
																			<select class="select select2 groupSelect" id="groupSelect" businessId="<?php echo EncodeQueryData($businessId); ?>">
																				<option value='0'>Select</option>
																				<?php
																				foreach ($groupData as $row) {
																					$businessGroupId = $row['businessGroupId'];
																					$groupTitle = $row['title'];
																					$status = ($row['status']);
																					

																				?>
																					<option value="<?php echo EncodeQueryData($businessGroupId) . '_' . EncodeQueryData($customerId); ?>" <? if ($businessGroupId == $customerBusinessGroupId) { echo 'selected'; } ?>><?php echo ($groupTitle); ?></option>
																				<?php
																				}
																				?>
																			</select>
																		<?php } ?>
																	</td>
																</tr>
														<?php
															}
															unset($objCountryStateMaster);
															unset($objBusiness);
														}
														
															?>
														</tbody>
													</table>
												</div>
											</div>
										</div>
									</div>
								</div>
							</section>
						</div>
					</div>
					<!-- /Main Content -->
				</div>
			</form>
		</div>
	</div>

	<style>
		@media (max-width: 991.98px) {
			.sidebar {
				margin-bottom: 1.5rem;
			}
			.order-1, .order-2 {
				order: unset !important;
			}
		}
	</style>




	<!-- BEGIN: Footer-->
	<?php include('includes/footer.php'); ?>
	<?php include('includes/footerJs.php'); ?>
	<?php include('includes/datatableJs.php'); ?>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/polyfill.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/tables/jquery.dataTables.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/jquery.raty.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/ui/breadcrumbs-with-stats.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/pages/app-contacts.js"></script>

	<script>
		//datatable alignment
		var current_datatable = $("#contactTable").DataTable({
			"aaSorting": [],
			// 			"order": [[ 0, "asc" ]],
			"aLengthMenu": [
				[100, 250, 500, 1000],
				[100, 250, 500, 1000]
			],
			"oLanguage": {
				"sSearch": "Filter: "
			},
			"language": {
				//"info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			"aoColumns": [{
					"sWidth": "30%",
					"bSortable": true
				},
				{
					"sWidth": "30%",
					"bSortable": true
				},
				{
					"sWidth": "15%",
					"bSortable": false
				},
				{
					"sWidth": "5%",
					"bSortable": false
				},
				{
					"sWidth": "20%",
					"bSortable": false
				}
			],
		});

		//onchange group select group		
		$('.groupSelect').on('change', function() {
		// $("#contactTable tbody").on("change", ".groupSelect", function() {

			var current_datatable_row = current_datatable.row($(this).parents('tr'));
			var groupSelectValue = $(this).val();
			var businessId = $(this).attr('businessId');
			var isFavorites = $("#isFavorite").val();
			if (groupSelectValue != 0) {
				Swal.fire({
					title: 'Confirmation',
					text: "Continue with assign the group?",
					type: 'warning',
					showCancelButton: true,
					confirmButtonColor: '#3085d6',
					cancelButtonColor: '#d33',
					confirmButtonText: 'Assign',
					confirmButtonClass: 'btn btn-outline-primary btn-glow',
					cancelButtonClass: 'btn btn-outline-danger ml-1',
					buttonsStyling: false,
				}).then(function(result) {
					if (result.value) {
						$.ajax({
							type: "GET",
							url: "../ajax/ajax_assign_cutomer_group.html",
							data: {
								groupSelectValue: groupSelectValue,
								businessId: businessId
							},
							success: function() {
								if (isFavorite == 2) {
									current_datatable.row(current_datatable_row).remove().draw(false);
								}
								Swal.fire({
									type: "success",
									title: 'Assign!',
									text: 'Assign Successfully.',
									confirmButtonClass: 'btn btn-success btn-glow',
								})
							}
						});
					}
				})
			}
		});

		//delete
		$('.deleteAll').on('click', function() {
		// $("#contactTable tbody").on("click", ".deleteAll", function() {

			var current_datatable_row = current_datatable.row($(this).parents('tr'));
			var businessId = $(this).attr('businessId');

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_customer.html",
						data: {
							businessId: businessId
						},
						success: function() {
							current_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success btn-glow',
							})
						}
					});
				}
			})
		});

		//delete
		// $('.deleteAjaxRow').on('click', function () 
		$("#contactTable tbody").on("click", ".deleteAjaxRow", function() {
			var current_datatable_row = current_datatable.row($(this).parents('tr'));
			var customerId = $(this).attr('customerId');
			var businessId = $(this).attr('businessId');

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_customer.html",
						data: {
							customerId: customerId,
							businessId: businessId
						},
						success: function() {
							current_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success btn-glow',
							})
						}
					});
				}
			})
		});


		//update status 
		// $('.isStatus').on('click', function() {
		$("#contactTable tbody").on("click", ".isStatus", function() {

			var thisanchor = $(this);
			var customerId = $(this).attr('customerId');
			var isStatus = $(this).attr('isStatus');

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with status change?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Ok',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger btn-glow ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_set_customer_status.html",
						data: {
							customerId: customerId,
							isStatus: isStatus
						},
						success: function() {
							thisanchor.attr('isStatus', isStatus == 0 ? 1 : 0);
							if (isStatus == 0) {
								thisanchor.html('Active');
								$('#isActive_' + customerId).removeClass('btn btn-sm btn-outline-secondary btn-glow');
								$('#isActive_' + customerId).addClass('btn btn-sm btn-outline-success btn-glow');
							} else {
								thisanchor.html('Inactive');
								$('#isActive_' + customerId).removeClass('btn btn-sm btn-outline-success btn-glow');
								$('#isActive_' + customerId).addClass('btn btn-sm btn-outline-secondary btn-glow');
							}
							Swal.fire({
								type: "success",
								title: 'Status updated!',
								confirmButtonClass: 'btn btn-outline-success btn-glow',
							})
						}
					});
				}
			})
		});

		//change favorite
		// $('.favorite').on('click', function() {
		$("#contactTable tbody").on("click", ".favorite", function() {
			
			var thisanchor = $(this);
			var customerId = $(this).attr('customerId');
			var isStatus = $(this).attr('isStatus');

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with status change favorite?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Ok',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger btn-glow ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_set_customer_favorite.html",
						data: {
							customerId: customerId,
							isStatus: isStatus
						},
						success: function() {
							thisanchor.attr('isStatus', isStatus == 0 ? 1 : 0);
							if (isStatus == 0) {
								thisanchor.addClass('active');
								// $('#isFavorite_'+customerId).removeClass('active');
								// $('#isFavorite_'+customerId).addClass('active');									
							} else {
								thisanchor.addClass('inactive');
								// $('#isFavorite_'+customerId).removeClass('btn btn-sm btn-outline-success btn-glow');
								// $('#isFavorite_'+customerId).addClass('btn btn-sm btn-outline-secondary btn-glow');

							}
							Swal.fire({
								type: "success",
								title: 'Status updated!',
								confirmButtonClass: 'btn btn-outline-success btn-glow',
							})
						}
					});
				} else if (result.dismiss === Swal.DismissReason.cancel) {
					// User clicked "Cancel"
					// Put your code for handling "Cancel" here
					// console.log("User clicked Cancel");
					window.location.reload();
				}
			})
		});

		//send login details
		// $('.sendLoginDeatils').on('click', function() {
		$("#contactTable tbody").on("click", ".sendLoginDeatils", function() {

			var current_datatable_row = current_datatable.row($(this).parents('tr'));
			var customerId = $(this).attr('customerId');
			var businessLogo = $(this).attr('businessLogo');
			var businessId = $('#businessId').val();


			Swal.fire({
				title: 'Confirmation',
				text: "Continue with send login details?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Ok',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_send_customer_login_details.html",
						data: {
							customerId: customerId,
							businessId: businessId
						},
						success: function() {
							Swal.fire({
								type: "success",
								title: 'Sent successfully.',
								confirmButtonClass: 'btn btn-outline-success btn-glow',
							})
						}
					});
				}
			})
		});

		//favourite contacts
		$('.favoriteContacts').on('click', function() {
			var leftNavId = $(this).attr('leftNavId');
			window.location.href = "viewbusinesscustomers.html?id=" + leftNavId;
		});
		//all contacts
		$('.allContacts').on('click', function() {
			var leftNavId = $(this).attr('leftNavId');
			window.location.href = "viewbusinesscustomers.html";
		});
		//new contacts 
		$('.newContacts').on('click', function() {
			var leftNavId = $(this).attr('leftNavId');
			window.location.href = "viewbusinesscustomers.html?id=" + leftNavId;
		});

		//new contacts
		$('.exportClass').on('click', function() {
			$("#businessCustomerForm").submit();
		});
		//Is Display Paid Content
		// $('.isDisplayPaidContent').on('click', function () 
		// { 
		// 	var current_datatable_row = current_datatable.row($(this).parents('tr'));	
		// 	var customerId = $(this).attr('customerId'); 
		// 	var businessId = $(this).attr('businessId'); 
		// 	var isDisplayPaidContent = $(this).attr('isDisplayPaidContent'); 
		// 	var message = (isDisplayPaidContent==0?"Show Paid Content to Customer?":"Hide Paid Content to Customer?")
		// 	var val= (isDisplayPaidContent==1? val =0: val=1); 
		// 	Swal.fire({
		// 	  title: 'Confirmation',
		// 	  text: message,
		// 	  type: 'warning',
		// 	  showCancelButton: true,
		// 	  confirmButtonColor: '#3085d6',
		// 	  cancelButtonColor: '#d33',
		// 	  confirmButtonText: 'Ok',
		// 	  confirmButtonClass: 'btn btn-outline-primary btn-glow',
		// 	  cancelButtonClass: 'btn btn-outline-danger ml-1',
		// 	  buttonsStyling: false,
		// 	}).then(function (result) 
		// 	{
		// 	    if (result.value) 
		// 	    {				   
		// 		    $.ajax({
		// 				type: "GET",						
		// 				url: "../ajax/ajax_set_customer_isDisplayPaidContent.html",
		// 				data: {customerId:customerId,businessId:businessId,isDisplayPaidContent:isDisplayPaidContent},
		// 				success:function()
		// 				{	 
		// 					$('#isDisplayPaidContent_'+customerId).attr('isDisplayPaidContent',val);
		// 					if(val == 1){								
		// 						$('#icon_'+customerId).css('color','#28d094');
		// 					}else{
		// 						$('#icon_'+customerId).css('color','red');
		// 					}							
		// 					Swal.fire({
		// 					type: "success",
		// 					title: 'Done.',							
		// 					confirmButtonClass: 'btn btn-outline-success btn-glow',
		// 					})
		// 				}
		// 			});
		// 	    }
		// 	})
		// });

		// $('.isDisplayPaidContent').on('click', function () 
		// { 

		// });
		// Use event delegation for dynamically loaded content
		$(document).on('click', '.viewNotificationPopup', function(e) {
			e.preventDefault();
			var customerId = $(this).attr('customerId');
			var businessId = $(this).attr('businessId');
			var isDisplayPaidContent = $(this).attr('isDisplayPaidContent');
			
			// Get the href URL for the popup
			var popupUrl = $(this).attr('href');
			
			// Open popup using your preferred method
			// If using magnificPopup:
			$.magnificPopup.open({
				items: {
					src: popupUrl
				},
				type: 'ajax',
				closeOnBgClick: false
			});
		});
	</script>

</body>
<!-- END: Body-->

</html>
