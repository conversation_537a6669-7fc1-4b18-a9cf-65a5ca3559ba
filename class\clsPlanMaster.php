<?php
class clsPlanMaster
{	

	var $quarterlyAmount = 0;
	var $semiAnnualAmount = 0;
	var $annualAmount = 0;
	var $agentId = 0;
	var $planId = 0;
	var $price = 0;
	var $contactLimit = 0;
	var $cardLimit = '0';
	var $subCardLimit = '0';
	var $imageInfographSectionLimit = 0;
	var $videoInfographSectionLimit = 0;
	var $allowThemeEditing = 0;
	var $imageCarouselLimit = 0;
	var $imageInfographLimit = 0;
	var $videoInfographLimit = 0;
	var $formLimit = 0;
	var $allowLogoInclusion = 0;
	var $allowPhotoInclusion = 0;
	var $allowVideoProfile = 0;
	var $isDedicatedURL = 0;
	var $isCrmIntergration = 0;
	var $isCouponServices = 0;
	var $isWhiteLabel = 0;
	var $isPDFSubscription = 0;
	var $is24x7Support = 0;	
	var $createdDate = 0;
	var $email = 0;
	var $defaultEmail = 0;
	var $smschk = 0;
	var $defaultSms = 0;
	var $surveychk = 0;
	var $customQrFormchk = 0;	
	var $geolocation = 0;
	var $socialfeed = 0;
	var $statistics = 0;
	var $support = 0;
	var $todo = 0;
	var $probizcaplus = 0;
	var $invoice = 0;
	var $infoContentLimit = 0;
	var $infoSectionLiimit = 0;
	var $galleryImageLimit = 0;
	var $durationDays = 0;
	
	
	function SavePlanMaster($planId)
	{
		$objDB = new clsDB();
		$sql = '';
		if($planId >0)
		{
				$sql = "UPDATE planmaster SET
							title='".$this->title."',
							price=".$this->price.",
							contactLimit='".($this->contactLimit)."',
							cardLimit='".($this->cardLimit)."',
							subCardLimit='".($this->subCardLimit)."',
							imageInfographSectionLimit='".($this->imageInfographSectionLimit)."',
							videoInfographSectionLimit='".($this->videoInfographSectionLimit)."',
							allowThemeEditing=".($this->allowThemeEditing).",
							imageCarouselLimit='".($this->imageCarouselLimit)."',
							imageInfographLimit='".($this->imageInfographLimit)."',
							videoInfographLimit='".($this->videoInfographLimit)."',
							formLimit='".($this->formLimit)."',
							allowLogoInclusion=".($this->allowLogoInclusion).",
							allowPhotoInclusion=".($this->allowPhotoInclusion).",
							allowVideoProfile=".($this->allowVideoProfile).",
							isDedicatedURL=".($this->isDedicatedURL).",
							isCrmIntergration=".($this->isCrmIntergration).",
							isCouponServices=".($this->isCouponServices).",
							isWhiteLabel=".($this->isWhiteLabel).",
							isPDFSubscription=".($this->isPDFSubscription).",
							pushNotification=".($this->pushNotification).",
							shoppingCart=".($this->shoppingCart).",
							email=".($this->email).",
							defaultEmail=".($this->defaultEmail).",
							smschk=".($this->smschk).",
							surveychk=".($this->surveychk).",
							customQrFormchk=".($this->customQrFormchk).",
							defaultSms=".($this->defaultSms).",
							geolocation=".($this->geolocation).",
							socialfeed=".($this->socialfeed).",
							statistics=".($this->statistics).",
							support=".($this->support).",
							todo=".($this->todo).",
							probizcaplus=".($this->probizcaplus).",
							invoice=".($this->invoice).",
							chatRoom=".($this->chatRoom).",
							infoSectionLiimit=".($this->infoSectionLiimit).",
							infoContentLimit=".($this->infoContentLimit).",
							annualAmount=".($this->annualAmount).",
							semiAnnualAmount=".($this->semiAnnualAmount).",
							quarterlyAmount=".($this->quarterlyAmount).",
							galleryImageLimit=".($this->galleryImageLimit).",
							durationDays=".($this->durationDays)."
							WHERE planId=".$planId;
							
							// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}
		else
		{
		    $sql = "INSERT INTO planmaster(price,title,contactLimit,cardLimit,imageInfographSectionLimit,videoInfographSectionLimit,allowThemeEditing,
										imageCarouselLimit,imageInfographLimit,videoInfographLimit,formLimit,allowLogoInclusion,allowPhotoInclusion,
										allowVideoProfile,isDedicatedURL,isCrmIntergration,isCouponServices,isWhiteLabel,isPDFSubscription,pushNotification,
										shoppingCart,email,defaultEmail,smschk,surveychk,customQrFormchk,defaultSms,geolocation,socialfeed,statistics,support,todo,probizcaplus,invoice,chatRoom,infoContentLimit,infoSectionLiimit,
										quarterlyAmount,semiAnnualAmount,annualAmount,galleryImageLimit,durationDays) 
								VALUES(								  				
									  '".addslashes($this->price)."',								
									  '".addslashes($this->title)."',								
									  '".addslashes($this->contactLimit)."',								
									  '".addslashes($this->cardLimit)."',								
									  '".addslashes($this->imageInfographSectionLimit)."',	
									  '".addslashes($this->videoInfographSectionLimit)."',								
									  '".addslashes($this->allowThemeEditing)."',								
									  '".addslashes($this->imageCarouselLimit)."',								
									  '".addslashes($this->imageInfographLimit)."',	
									  '".addslashes($this->videoInfographLimit)."',	
									  '".addslashes($this->formLimit)."',	
									  '".addslashes($this->allowLogoInclusion)."',										 	
									  '".addslashes($this->allowPhotoInclusion)."',										 	
									  '".addslashes($this->allowVideoProfile)."',	
									  '".addslashes($this->isDedicatedURL)."',	
									  '".addslashes($this->isCrmIntergration)."',	
									  '".addslashes($this->isCouponServices)."',	
									  '".addslashes($this->isWhiteLabel)."',	
									  '".addslashes($this->isPDFSubscription)."',	
									  '".addslashes($this->pushNotification)."',										
									  '".addslashes($this->shoppingCart)."',
                                      '".addslashes($this->email)."',										
                                      '".addslashes($this->defaultEmail)."',										
                                      '".addslashes($this->smschk)."',										
                                      '".addslashes($this->surveychk)."',										
                                      '".addslashes($this->customQrFormchk)."',										
                                      '".addslashes($this->defaultSms)."',										
									  '".addslashes($this->geolocation)."',										
									  '".addslashes($this->socialfeed)."',										
									  '".addslashes($this->statistics)."',										
									  '".addslashes($this->support)."',										
									  '".addslashes($this->todo)."',										
									  '".addslashes($this->probizcaplus)."',										
									  '".addslashes($this->invoice)."',										  
									  '".addslashes($this->chatRoom)."',										
									  '".addslashes($this->infoContentLimit)."',										
									  '".addslashes($this->infoSectionLiimit)."',										
									  '".addslashes($this->quarterlyAmount)."',										
									  '".addslashes($this->semiAnnualAmount)."',										
									  '".addslashes($this->annualAmount)."',										
									  '".addslashes($this->galleryImageLimit)."',										
									  '".addslashes($this->durationDays)."'										
									  )";
            // echo $sql;exit;									  
			$planId = $objDB->ExecuteInsertQuery($sql);
		   
		}
		
		unset($objDB);
		return $planId;
	}
	
	function GetPlanDetails($planId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM planmaster WHERE planId = ".$planId;
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetAllBusinessPlanDetails($businessPlanDetailId,$businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		// $sql = "SELECT * FROM businessplansubscriptiondetails WHERE businessPlanDetailId = ".$businessPlanDetailId." AND businessId=".$businessId;
		$sql = "SELECT bsp.*,adtl1.*,adtl2.defaultCount as dfSMS, adtl2.dfUsed AS dfsmsU,adtl2.purchasedCount AS smsPurCount,adtl2.purUsed AS smsPurUsed FROM businessplansubscriptiondetails AS bsp
		LEFT JOIN addondetails as adtl1 ON bsp.businessId = adtl1.businessId AND adtl1.addonNmId=1 
		LEFT JOIN addondetails as adtl2 ON bsp.businessId = adtl2.businessId AND adtl2.addonNmId=2 
		WHERE bsp.businessId='".$businessId."'";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetBusinessPlanDetails($planId,$businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM businessplansubscriptiondetails WHERE planId = ".$planId."";
		if($businessId > 0)
		{
			$sql.=" AND businessId=$businessId";
			$rows = $objDB->GetDataRow($sql);
		}
		else
		{
			$rows = $objDB->GetResultset($sql);
		}
		// echo $sql;exit;
		unset($objDB);
		return $rows;
	}
	
	function GetAllPlans()
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM planmaster";	 		 
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function UpdateDefaultCount($businessId, $addonNameId,$defaultCount)
	{
		if($businessId > 0 && $addonNameId > 0)
		{
			$objDB = new clsDB();
			$sql = "UPDATE addondetails SET defaultCount =".$defaultCount." WHERE businessId = $businessId AND addonNmId=$addonNameId"; 
			// echo $sql;
			$addondtlId = $objDB->ExecuteQuery($sql);
			unset($objDB);
			return $addondtlId;
		}
	}
	
	function SetSubscriptionPlanStatus($id, $status)
	{
		if($id > 0)
		{
			$objDB = new clsDB();
			$sql = "UPDATE planmaster SET status =".$status." WHERE planId = ".$id; 
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	
	function UpdatePlanForAllBusiness($planId)
	{
		$objDB = new clsDB();
		$sql = '';
		if($planId >0)
		{
				$sql = "UPDATE businessplansubscriptiondetails SET
							title='".$this->title."',
							price=".$this->price.",
							contactLimit='".($this->contactLimit)."',
							cardLimit='".($this->cardLimit)."',
							subCardLimit='".($this->subCardLimit)."',
							imageInfographSectionLimit='".($this->imageInfographSectionLimit)."',
							videoInfographSectionLimit='".($this->videoInfographSectionLimit)."',
							allowThemeEditing=".($this->allowThemeEditing).",
							imageCarouselLimit='".($this->imageCarouselLimit)."',
							imageInfographLimit='".($this->imageInfographLimit)."',
							videoInfographLimit='".($this->videoInfographLimit)."',
							formLimit='".($this->formLimit)."',
							allowLogoInclusion=".($this->allowLogoInclusion).",
							allowPhotoInclusion=".($this->allowPhotoInclusion).",
							allowVideoProfile=".($this->allowVideoProfile).",
							isDedicatedURL=".($this->isDedicatedURL).",
							isCrmIntergration=".($this->isCrmIntergration).",
							isCouponServices=".($this->isCouponServices).",
							isWhiteLabel=".($this->isWhiteLabel).",
							isPDFSubscription=".($this->isPDFSubscription).",
							pushNotification=".($this->pushNotification).",
							shoppingCart=".($this->shoppingCart).",
							email=".($this->email).",
							smschk=".($this->smschk).",
							surveychk=".($this->surveychk).",
							geolocation=".($this->geolocation).",
							socialfeed=".($this->socialfeed).",
							statistics=".($this->statistics).",
							support=".($this->support).",
							todo=".($this->todo).",
							probizcaplus=".($this->probizcaplus).",
							invoice=".($this->invoice).",
							chatRoom=".($this->chatRoom).",
							infoSectionLiimit=".($this->infoSectionLiimit).",
							infoContentLimit=".($this->infoContentLimit).",
							annualAmount=".($this->annualAmount).",
							semiAnnualAmount=".($this->semiAnnualAmount).",
							quarterlyAmount=".($this->quarterlyAmount).",
							galleryImageLimit=".($this->galleryImageLimit)."
							WHERE planId=".$planId;
			// echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}
		else
		{
		    $sql = "INSERT INTO businessplansubscriptiondetails(price,title,contactLimit,cardLimit,imageInfographSectionLimit,videoInfographSectionLimit,allowThemeEditing,
										imageCarouselLimit,imageInfographLimit,videoInfographLimit,formLimit,allowLogoInclusion,allowPhotoInclusion,
										allowVideoProfile,isDedicatedURL,isCrmIntergration,isCouponServices,isWhiteLabel,isPDFSubscription,pushNotification,
										shoppingCart,email,smschk,surveychk,geolocation,socialfeed,statistics,support,todo,probizcaplus,invoice,chatRoom,infoContentLimit,infoSectionLiimit,
										quarterlyAmount,semiAnnualAmount,annualAmount,galleryImageLimit) 
								VALUES(								  				
									  '".addslashes($this->price)."',								
									  '".addslashes($this->title)."',								
									  '".addslashes($this->contactLimit)."',								
									  '".addslashes($this->cardLimit)."',								
									  '".addslashes($this->imageInfographSectionLimit)."',	
									  '".addslashes($this->videoInfographSectionLimit)."',								
									  '".addslashes($this->allowThemeEditing)."',								
									  '".addslashes($this->imageCarouselLimit)."',								
									  '".addslashes($this->imageInfographLimit)."',	
									  '".addslashes($this->videoInfographLimit)."',	
									  '".addslashes($this->formLimit)."',	
									  '".addslashes($this->allowLogoInclusion)."',										 	
									  '".addslashes($this->allowPhotoInclusion)."',										 	
									  '".addslashes($this->allowVideoProfile)."',	
									  '".addslashes($this->isDedicatedURL)."',	
									  '".addslashes($this->isCrmIntergration)."',	
									  '".addslashes($this->isCouponServices)."',	
									  '".addslashes($this->isWhiteLabel)."',	
									  '".addslashes($this->isPDFSubscription)."',	
									  '".addslashes($this->pushNotification)."',										
									  '".addslashes($this->shoppingCart)."',
                                      '".addslashes($this->email)."',										
                                      '".addslashes($this->smschk)."',										
                                      '".addslashes($this->surveychk)."',										
									  '".addslashes($this->geolocation)."',										
									  '".addslashes($this->socialfeed)."',										
									  '".addslashes($this->statistics)."',										
									  '".addslashes($this->support)."',										
									  '".addslashes($this->todo)."',										
									  '".addslashes($this->probizcaplus)."',										
									  '".addslashes($this->invoice)."',										  
									  '".addslashes($this->chatRoom)."',										
									  '".addslashes($this->infoContentLimit)."',										
									  '".addslashes($this->infoSectionLiimit)."',
                                      '".addslashes($this->quarterlyAmount)."',										
									  '".addslashes($this->semiAnnualAmount)."',										
									  '".addslashes($this->annualAmount)."',										  
									  '".addslashes($this->galleryImageLimit)."'										  
									  )";
            // echo $sql;exit;									  
			$planId = $objDB->ExecuteInsertQuery($sql);
		   
		}
		
		unset($objDB);
		return $planId;
	}
	
}
?>