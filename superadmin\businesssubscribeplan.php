<?php
//include classes and config file here	
include('includes/validatesystemuserlogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsBusinessPlanMaster.php');
include('../class/clsPlanMaster.php');
include('../class/clsBusiness.php');

$planId = 0;
$title = '';
$price = '';
$contactLimit = '';
$cardLimit = '';
$subCardLimit = '';
$imageInfographSectionLimit = '';
$videoInfographSectionLimit = '';
$allowThemeEditing = '';
$imageCarouselLimit = '';
$imageInfographLimit = '';
$videoInfographLimit = '';
$formLimit = '';
$allowLogoInclusion = '';
$allowPhotoInclusion = '';
$allowVideoProfile = '';
$isDedicatedURL = '';
$isCrmIntergration = '';
$isCouponServices = '';
$isWhiteLabel = '';
$isPDFSubscription = '';
$shoppingCart = '';
$chatRoom = '';
$pushNotification = '';
$planmasters = '';
$businessName = '';
$pageTitle = 'Add';
$buttonTitle = 'Save';
$email = 0;
$smschk = 0;
$surveychk = 0;
$customQrFormchk =0;
$geolocation = 0;
$socialfeed = 0;
$statistics = 0;
$support = 0;
$todo = 0;
$probizcaplus = 0;
$invoice = 0;
$infoContentLimit = 0;
$infoSectionLiimit = 0;
$quarterlyAmount = 0;
$semiAnnualAmount = 0;
$annualAmount = 0;
$galleryImageLimit = 0;
$type = 0;
$subscriptionStartDate = 0;
$subscriptionEndDate = 0;
$paymentPlan = 0;
$appliedPlanAmt = 0;
//get all actibe plan
$objBusinessPlanMaster = new clsBusinessPlanMaster();
$planmasters = $objBusinessPlanMaster->GetAllActivePlans();
unset($objBusinessPlanMaster);

// $planId = 3; 
if (isset($_GET['id']) || isset($_GET['planId'])) {
	$pageTitle = 'Edit';
	$buttonTitle = 'Update';
	$businessId = DecodeQueryData($_GET['id']);
	$planId = DecodeQueryData($_GET['planId']);

	//get business name 
	$objBusiness = new clsBusiness();
	$businessName  = $objBusiness->GetBusinessName($businessId);

	// echo $businessId;exit;
	$objBusinessPlanMaster = new clsBusinessPlanMaster();
	$row = $objBusinessPlanMaster->GetBusinessPlanDetails($businessId, $planId);
	unset($objBusinessPlanMaster);

	if ($row == '') {
		$businessPlanDetailId = 0;
		$objPlanMaster = new clsPlanMaster();
		$row = $objPlanMaster->GetPlanDetails($planId = 3);
		unset($objPlanMaster);
	} else {
		$businessPlanDetailId = stripslashes($row['businessPlanDetailId']);
	}
	$title = stripslashes($row['title']);
	$price = $row['price'];
	$quarterlyAmount = $row['quarterlyAmount'];
	$semiAnnualAmount = $row['semiAnnualAmount'];
	$annualAmount = $row['annualAmount'];
	$planId = $row['planId'];
	$contactLimit = $row['contactLimit'];
	$cardLimit = $row['cardLimit'];
	$subCardLimit = $row['subCardLimit'] ? $row['subCardLimit'] : 0;
	$imageInfographSectionLimit = $row['imageInfographSectionLimit'];
	$videoInfographSectionLimit = $row['videoInfographSectionLimit'];
	$allowThemeEditing = $row['allowThemeEditing'];
	$imageCarouselLimit = $row['imageCarouselLimit'];
	$imageInfographLimit = $row['imageInfographLimit'];
	$videoInfographLimit = $row['videoInfographLimit'];
	$formLimit = $row['formLimit'];
	$allowLogoInclusion = $row['allowLogoInclusion'];
	$allowPhotoInclusion = $row['allowPhotoInclusion'];
	$allowVideoProfile = $row['allowVideoProfile'];
	$isDedicatedURL = $row['isDedicatedURL'];
	$isCrmIntergration = $row['isCrmIntergration'];
	$isCouponServices = $row['isCouponServices'];
	$isWhiteLabel = $row['isWhiteLabel'];
	$isPDFSubscription = $row['isPDFSubscription'];
	$pushNotification = $row['pushNotification'];
	$chatRoom = $row['chatRoom'];
	$shoppingCart = $row['shoppingCart'];
	$email = stripslashes($row['email']);
	$smschk = stripslashes($row['smschk']);
	$surveychk = stripslashes($row['surveychk']);
	$customQrFormchk = stripslashes($row['customQrFormchk']);
	$geolocation = stripslashes($row['geolocation']);
	$socialfeed = stripslashes($row['socialfeed']);
	$statistics = stripslashes($row['statistics']);
	$support = stripslashes($row['support']);
	$todo = stripslashes($row['todo']);
	$probizcaplus = stripslashes($row['probizcaplus']);
	$invoice = stripslashes($row['invoice']);
	$infoSectionLiimit = stripslashes($row['infoSectionLiimit']);
	$infoContentLimit = stripslashes($row['infoContentLimit']);
	$galleryImageLimit = stripslashes($row['galleryImageLimit']);
	$type = stripslashes($row['type']);
	$defaultCount = ($row['defaultCount']);
	$dfSMS = ($row['dfSMS']);

	// if(($planId == 1))
	// {
	// 	$startDate  = (date("m/d/Y h:i:s",strtotime($row['createdDate'])));
	// 	$endDate  = (date("m/d/Y h:i:s",strtotime($startDate.'+5 years')));
	// }
	// else  
	// {    
	$startDate  = ($row['subscriptionStartDate'] == '0000-00-00 00:00:00') ? '' : (date("m/d/Y h:i:s", strtotime($row['subscriptionStartDate'])));
	$endDate  = ($row['subscriptionEndDate'] == '0000-00-00 00:00:00') ? '' : (date("m/d/Y h:i:s", strtotime($row['subscriptionEndDate'])));
	// }			

	$paymentPlan = stripslashes($row['paymentPlan']);
	$appliedPlanAmt =isset($row['appliedPlanAmt']) ?  stripslashes($row['appliedPlanAmt']) : 0;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<title>Subscription Plan Details</title>
	<?php include("includes/headercss.php") ?>
	<?php include("includes/datatablecss.php") ?>
	<link href="<?php echo BASE_PATH; ?>/superadmin/vendors/select2/dist/css/select2.min.css" rel="stylesheet">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/superadmin/vendors/alertifyjs/css/alertify.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/superadmin/vendors/inputmask/form-extended.css">
	<link href="<?php echo BASE_PATH; ?>/superadmin/vendors/datetimepicker/css/bootstrap-datetimepicker.css" rel="stylesheet">
	<link href="<?php echo BASE_PATH; ?>/superadmin/vendors/datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet">
</head>

<body class="nav-md">
	<div class="container body">
		<div class="main_container">
			<?php include("includes/leftnav.php") ?>
			<?php include("includes/header.php") ?>
			<!-- page content -->
			<div class="right_col" role="main">
				<div class="">
					<div class="clearfix"></div>
					<div class="row">
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="x_panel">
								<div class="x_title">
									<h2><?php echo ($businessName); ?></h2>
									<ul class="nav navbar-right panel_toolbox">
										<li><a href="viewbisunesses.html"><i class="fa fa-backward"></i> Back</a></li>
									</ul>
									<div class="clearfix"></div>
									<!--- show status messages --->
									<?php
									if (isset($_GET["status"])) {
										if ($_GET["status"] == "added") {
									?>
											<div class="alert alert-success alert-dismissible fade in" role="alert">
												<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">�</span>
												</button>
												Added.
											</div>
										<?php
										} else if ($_GET["status"] == "updated") {
										?>
											<div class="alert alert-success alert-dismissible fade in" role="alert">
												<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">�</span>
												</button>
												Plan Updated Successfully.
											</div>
									<?php
										}
									}
									?>
								</div>
								<div class="x_content">
									<form id="frmUpdateScubscription" data-parsley-validate class="form-horizontal form-label-left" action="plansubscriptiondetailssubmit.html" method="POST" enctype="multipart/form-data">
										<input type="hidden" name="planId" value="<?php echo ($planId); ?>">
										<input type="hidden" name="detailId" id="detailId" value="<?php echo ($businessPlanDetailId); ?>">
										<input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
										<div class="row">
											<div class="col-md-12">
												<h2 class="ml-4">Plan Subscription Details</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="title">Plan Name</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<select id="planId" name="planId" class="form-control step1 input-md  select2_single" required>
																	<option>Select Plan</option>
																	<?php
																	if ($planmasters != "") {
																		while ($row = mysqli_fetch_assoc($planmasters)) {
																			
																			$planMasterId  = $row['planId'];
																			// Skip the plan with planId 11
																			// if ($planMasterId == 11) {
																			// 	continue;
																			// }
																			$name  = stripslashes($row['title']) . ' ($' . $row['price'] . ')';;
																	?>
																			<option value="<?php echo ($planMasterId); ?>" <?php if ($planMasterId == $planId) { ?> selected="true" class="bg-info" <?php } ?>><?php echo ($name); ?></option>
																	<?php
																		}
																	}
																	?>
																</select>
															</div>
														</div>
													</div>
												</div>

											</div>
									</form>
									<!-- <div class="ln_solid"></div> -->
									<form id="userForm" data-parsley-validate class="form-horizontal form-label-left" action="plansubscriptiondetailssubmit.html" method="POST" enctype="multipart/form-data">
										<input type="hidden" class='planId' name="planId" value="<?php echo ($planId); ?>">
										<input type="hidden" name="detailId" value="<?php echo ($businessPlanDetailId); ?>">
										<input type="hidden" name="businessIds" value="<?php echo ($businessId); ?>">
										<input type="hidden" name="appliedPlanAmt" id="appliedPlanAmt" value="<?php echo ($appliedPlanAmt); ?>">
										<div class="row">
											<div class="col-md-12">
												<h2 class="ml-4">Basic Details</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="startDate">Start Date<span class="required">*</span></label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input type="text" id="startDate" name="startDate" value="<?php echo ($startDate);  ?>" class="form-control mydatepicker" required>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="endDate">End Date </label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input type="text" id="endDate" name="endDate" value="<?php echo ($endDate); ?>" class="form-control mydatepicker"  required>
																<span id="dateNote" class="hide" style="color: red;">please select payment plan for end date</span>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<label class="control-label col-md-3 col-sm-3 col-xs-12" for="formLimit">Payment plan</label>
														<div class="form-group  mt-5 row">
															<div class="col-md-3 mt-5" style="padding:5px; text-align:center;">
																<input class="form-check-input paymentPlan" type="radio" name="paymentPlan" id="paymentPlan" value="1" duration="3" <?php if ($paymentPlan == 1 ) {
																																														echo "checked";
																																													}
																																													if ($planId == 1 || $planId == 11) {
																																														echo "disabled";
																																													} ?>>
																<label class="form-check-label" for="Quarterly">
																	Quarterly
																</label>
															</div>
															<div class="col-md-3 mt-5" style="padding:5px;">
																<input class="form-check-input paymentPlan" type="radio" name="paymentPlan" id="paymentPlan" value="2" duration="6" <?php if ($paymentPlan == 2) {
																																														echo "checked";
																																													}
																																													if ($planId == 1 || $planId == 11) {
																																														echo "disabled";
																																													} ?>>
																<label class="form-check-label" for="Semi Annual">
																	Semi Annual
																</label>
															</div>
															<div class="col-md-3 mt-5" style="padding:5px;">
																<input class="form-check-input paymentPlan" type="radio" name="paymentPlan" id="paymentPlan" value="3" duration="12" <?php if ($paymentPlan == 3 ) {
																																															echo "checked";
																																														}
																																														if ($planId == 1 || $planId == 11) {
																																															echo "disabled";
																																														} ?>>
																<label class="form-check-label" for="Annually">
																	Annually
																</label>
															</div>
														</div>
													</div>
												</div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="title">Title</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="title" name="title" type="text" placeholder="" value="<?php echo ($title); ?>" class="form-control input-md required-input" required>
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="quarterlyAmount">Setup Fee</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="price" name="price" type="text" placeholder="" value="<?php echo ($price); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="quarterlyAmount">Quarterly</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="quarterlyAmount" name="quarterlyAmount" type="text" placeholder="" value="<?php echo ($quarterlyAmount); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="semiAnnualAmount">Semi Annual</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="semiAnnualAmount" name="semiAnnualAmount" type="text" placeholder="" value="<?php echo ($semiAnnualAmount); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="annualAmount">Annually</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="annualAmount" name="annualAmount" type="text" placeholder="" value="<?php echo ($annualAmount); ?>" class="form-control input-md required-input number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="contactLimit">Contact Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="1" id="contactLimit" name="contactLimit" type="number" placeholder="" value="<?php echo ($contactLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="cardLimit">Card Limit dd</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="1" id="cardLimit" name="cardLimit" type="number" placeholder="" value="<?php echo ($cardLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="subCardLimit">Sub Card Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="subCardLimit" name="subCardLimit" type="number" placeholder="" value="<?php echo ($subCardLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="formLimit">Form Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="formLimit" name="formLimit" type="number" placeholder="" value="<?php echo ($formLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>

													<div class="col-md-6">
														<div class="form-group  mt-5 row">
															<div class="col-md-6 mt-5" style="padding:5px; text-align:center;">
																<input class="form-check-input" type="radio" name="type" id="type" value="1" <?php if ($type == 1) {
																																					echo "checked";
																																				} ?>>
																<label class="form-check-label" for="type">
																	Private
																</label>
															</div>
															<div class="col-md-6 mt-5" style="padding:5px;">
																<input class="form-check-input" type="radio" name="type" id="type" value="0" <?php if ($type == 0) {
																																					echo "checked";
																																				} ?>>
																<label class="form-check-label" for="type">
																	Public
																</label>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-12">
												<h2 class="ml-4">Gallery</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="imageCarouselLimit">Album Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="imageCarouselLimit" name="imageCarouselLimit" type="number" placeholder="" value="<?php echo ($imageCarouselLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="galleryImageLimit">Gallery Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="galleryImageLimit" name="galleryImageLimit" type="number" placeholder="" value="<?php echo ($galleryImageLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-12">
												<h2 class="ml-4">Infographic</h2>
												<div class="ln_solid"></div>
												<div class="row" style="display:none;">
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="imageInfographSectionLimit">Image Info. Sections Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" max="5" id="imageInfographSectionLimit" name="imageInfographSectionLimit" type="number" placeholder="" value="<?php echo ($imageInfographSectionLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="5" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="imageInfographLimit">Image Content Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="imageInfographLimit" name="imageInfographLimit" type="number" placeholder="" value="<?php echo ($imageInfographLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="videoInfographSectionLimit">Video Info. Sections Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="videoInfographSectionLimit" name="videoInfographSectionLimit" type="number" placeholder="" value="<?php echo ($videoInfographSectionLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="2" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-12">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="videoInfographSectionLimit">Video Content Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="videoInfographLimit" name="videoInfographLimit" type="number" placeholder="" value="<?php echo ($videoInfographLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="infoSectionLiimit">Sections Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="infoSectionLiimit" name="infoSectionLiimit" type="number" placeholder="" value="<?php echo ($infoSectionLiimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="2" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="infoContentLimit">Content Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="infoContentLimit" name="infoContentLimit" type="number" placeholder="" value="<?php echo ($infoContentLimit); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-12">
												<h2 class="ml-4">Sms/Email</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="SMSLimit">SMS Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input id="smsLimit" name="smsLimit" type="number" placeholder="" value="<?php echo ($dfSMS); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="2" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-3 col-sm-3 col-xs-12" for="emailLimit">Email Limit</label>
															<div class="col-md-9 col-sm-9 col-xs-12">
																<input min="0" id="emailLimit" name="emailLimit" type="number" placeholder="" value="<?php echo ($defaultCount); ?>" class="form-control input-md required-input" data-parsley-validation-threshold="10" data-parsley-trigger="keyup" data-parsley-type="number">
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="ln_solid"></div>
										<div class="row">
											<div class="col-md-6">
												<h2 class="ml-4">Themes/Profile Details</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="imageCarouselLimit">Dedicated URL.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isDedicatedURL" id="isDedicatedURL" <?php echo ($isDedicatedURL ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="cardLimit">Edit Theme.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowThemeEditing" id="allowThemeEditing" <?php echo ($allowThemeEditing ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="allowLogoInclusion">Logo Inclusion.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowLogoInclusion" id="allowLogoInclusion" <?php echo ($allowLogoInclusion ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="contactLimit">Photo Inclusion.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowPhotoInclusion" id="allowPhotoInclusion" <?php echo ($allowPhotoInclusion ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="subCardLimit">Video Profile.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="allowVideoProfile" id="allowVideoProfile" <?php echo ($allowVideoProfile  ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="formLimit">White Label</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isWhiteLabel" id="isWhiteLabel" <?php echo ($isWhiteLabel  ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-md-6">
												<h2 class="ml-4">Additional Addon's</h2>
												<div class="ln_solid"></div>
												<div class="row">
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="isCrmIntergration">Allow CRM.</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isCrmIntergration" id="isCrmIntergration" <?php echo ($isCrmIntergration   ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="isPDFSubscription">Allow Paid Contents</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isPDFSubscription" id="isPDFSubscription" <?php echo ($isPDFSubscription ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="isCouponServices">Coupons/Incentives</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="isCouponServices" id="isCouponServices" <?php echo ($isCouponServices ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="chatRoom">Chat Room</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="chatRoom" id="chatRoom" <?php echo ($chatRoom ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="email">Email</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="email" id="email" <?php echo ($email ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="email">SMS</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="smschk" id="smschk" <?php echo ($smschk ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="geolocation">Geolocation</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="geolocation" id="geolocation" <?php echo ($geolocation ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="invoice">Invoice</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="invoice" id="invoice" <?php echo ($invoice ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="pushNotification">Push Notification</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="pushNotification" id="pushNotification" <?php echo ($pushNotification ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="probizcaplus">ProBizCa +</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="probizcaplus" id="probizcaplus" <?php echo ($probizcaplus ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="shoppingCart">Shopping Cart</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="shoppingCart" id="shoppingCart" <?php echo ($shoppingCart ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="statistics">Statistics</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="statistics" id="statistics" <?php echo ($statistics ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="support">Support</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="support" id="support" <?php echo ($support ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="todo">To Do</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="todo" id="todo" <?php echo ($todo ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="survey">Survey</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="surveychk" id="surveychk" <?php echo ($surveychk ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group row">
															<label class="control-label col-md-8 col-sm-8 col-xs-12" for="customQrFormchk">Custom QR Form</label>
															<div class="col-md-4 col-sm-4 col-xs-12">
																<input type="checkbox" name="customQrFormchk" id="customQrFormchk" <?php echo ($customQrFormchk ? 'checked' : ''); ?> style="margin-top:10px;">
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="ln_solid"></div>
										<div class="form-group text-center">
											<div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3">
												<button type="submit" class="btn btn-success" name="btnSave"><?php echo ($buttonTitle); ?></button>
												<a type="submit" class="btn btn-primary" href="viewbisunesses.html">Cancel</a>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- /page content -->
			<?php include("includes/footer.php") ?>
		</div>
	</div>
	<?php include("includes/footerjs.php") ?>
	<?php include("includes/datatablejs.php") ?>
	<script src="<?php echo BASE_PATH; ?>/superadmin/build/js/custom.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/parsleyjs/parsley.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/select2/dist/js/select2.full.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/jquery.cascadingdropdown.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/alertifyjs/alertify.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/inputmask/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/inputmask/extended/form-inputmask.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/moment/moment.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="vendors/jquery.lazyload.js"></script>
	<script type="text/javascript">

	$(document).ready(function(){
		var curPlanId = '<?php echo $planId; ?>';
		var startDate = $('#startDate').val();
		if(curPlanId > 1 && startDate == '')
		{
			$('#dateNote').removeClass('hide');
		}	
		
		if(curPlanId == 1 || curPlanId == 11)
		{
			$('#price').prop("readonly", true);
			$('#quarterlyAmount').prop("readonly", true);
			$('#semiAnnualAmount').prop("readonly", true);
			$('#annualAmount').prop("readonly", true);
		}
	});

		//***** DATE-TIME PICKER WITH CAL-ICON ******//
		$('#startDate').datetimepicker({
    format: 'MM/DD/YYYY HH:mm:ss',
    defaultDate: moment() // Sets it to the current date and time
});

$('#endDate').datetimepicker({
    format: 'MM/DD/YYYY HH:mm:ss',
    defaultDate: moment() // Sets it to the current date and time
});



		$('#startDate').on('dp.change', function(e) {


			var subscriptionPlanId = $(".planId").val();
			// alert(subscriptionPlanId);
			if (subscriptionPlanId == 1) {
				var subscriptionEndDate = moment(e.date, "MM/DD/YYYY hh:mm").add(5, 'years').format('MM/DD/YYYY hh:mm');
				$('#endDate').val(subscriptionEndDate);
				$('input[name=paymentPlan]').attr("disabled", true);
			}
			else if (subscriptionPlanId == 11)
			{
				var subscriptionEndDate = moment(e.date, "MM/DD/YYYY hh:mm").add(15, 'days').format('MM/DD/YYYY hh:mm');
				$('#endDate').val(subscriptionEndDate);
				$('input[name=paymentPlan]').attr("disabled", true);

			}			
			else {
				$('input[name=paymentPlan]').attr("disabled", false);
			}
			// funsubscriptionEndDate(e);

		});

		$('.paymentPlan').change(function() {
			var startDate = $('#startDate').val();
			var duration = $(this).attr('duration');
			// console.log($(this).val());
			if ($(this).val() == 1) {
				var appliedPlanAmt = $('#quarterlyAmount').val();
			} else if ($(this).val() == 2) {
				var appliedPlanAmt = $('#semiAnnualAmount').val();
			} else {
				var appliedPlanAmt = $('#annualAmount').val();
			}
			$('#appliedPlanAmt').val(appliedPlanAmt);
			var subscriptionEndDate = moment(startDate, "MM/DD/YYYY hh:mm").add(duration, 'month').format('MM/DD/YYYY hh:mm');
			$('#endDate').val(subscriptionEndDate);
			$('#dateNote').addClass('hide');
			// console.log(subscriptionEndDate);
		});


		function funsubscriptionEndDate(e) {


			// alert('Subscription End Date');
			var startDate = $('#startDate').val();
			var duration = $('input[name="paymentPlan"]:checked').attr("duration");
			// alert(duration)
			var appliedPlanAmt;

			if (e.target && e.target.value) {
				var planValue = e.target.value;
				
				if (planValue == 1) {
					appliedPlanAmt = $('#quarterlyAmount').val();
				} else if (planValue == 2) {
					appliedPlanAmt = $('#semiAnnualAmount').val();
				} else {
					appliedPlanAmt = $('#annualAmount').val();
				}
			} else {
				// Handle the case where e.target or e.target.value is undefined or null.
				// You might want to provide a default value or handle this differently.
			}

			$('#appliedPlanAmt').val(appliedPlanAmt);
			var subscriptionEndDate = moment(startDate, "MM/DD/YYYY hh:mm").add(duration, 'month').format('MM/DD/YYYY hh:mm');
			$('#endDate').val(subscriptionEndDate);
			$('#dateNote').addClass('hide');
			
		}

		//validate input number with decimal
		$('.number').keypress(function(event) {
			var $this = $(this);
			if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
				((event.which < 48 || event.which > 57) &&
					(event.which != 0 && event.which != 8))) {
				event.preventDefault();
			}

			var text = $(this).val();
			if ((event.which == 46) && (text.indexOf('.') == -1)) {
				setTimeout(function() {
					if ($this.val().substring($this.val().indexOf('.')).length > 3) {
						$this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
					}
				}, 1);
			}

			if ((text.indexOf('.') != -1) &&
				(text.substring(text.indexOf('.')).length > 2) &&
				(event.which != 0 && event.which != 8) &&
				($(this)[0].selectionStart >= text.length - 2)) {
				event.preventDefault();
			}
		});

		$('.number').bind("paste", function(e) {
			var text = e.originalEvent.clipboardData.getData('Text');
			if ($.isNumeric(text)) {
				if ((text.substring(text.indexOf('.')).length > 3) && (text.indexOf('.') > -1)) {
					e.preventDefault();
					$(this).val(text.substring(0, text.indexOf('.') + 3));
				}
			} else {
				e.preventDefault();
			}
		});

		//form validation
		$('#userForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});
		//number validation 
		$('#mobileNo').keypress(function(event) {
			var keycode = event.which;
			if (!(event.shiftKey == false && (keycode == 46 || keycode == 8 || keycode == 37 || keycode == 39 || (keycode >= 48 && keycode <= 57)))) {
				event.preventDefault();
			}
		});

		//--------------------------------------------------------
		$("#planId").change(function(e) {
			var curPlanId = '<?php echo $planId; ?>';
			var newPlanId = $("#planId").val();
			var detailId = '0';
			var businessId = $('#businessId').val();
			// console.log(detailId);
			console.log("curPlanId "+curPlanId);
			console.log("newPlanId "+newPlanId);
			var galleryImageLimit = $("#galleryImageLimit").val();
			if(newPlanId == 1 || newPlanId == 11)
			{
				$('#price').prop("readonly", true);
				$('#quarterlyAmount').prop("readonly", true);
				$('#semiAnnualAmount').prop("readonly", true);
				$('#annualAmount').prop("readonly", true);
				
			}
			else 
			{
				$('#price').prop("readonly", false);
				$('#quarterlyAmount').prop("readonly", false);
				$('#semiAnnualAmount').prop("readonly", false);
				$('#annualAmount').prop("readonly", false);	
			}

			// if (curPlanId != $(this).val()) {
				
				alertify.confirm('Confirmation', 'Continue with update plan?', function(e) {
					// console.log('newPlanId' + newPlanId);
					$('#planId').val(newPlanId);
					if (newPlanId == 1) {
						$('#appliedPlanAmt').val(0);
					}
					// alert(newPlanId);
					// $('#frmUpdateScubscription').submit();
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_get_plan_details.html",
						data: {
							businessId: businessId,
							businessPlanId: newPlanId,
							businessPlanDetailId: detailId
						},
						dataType: "json",
						success: function(data) {
							// alert(data);								
							if (data == 0) {
								Swal.fire({
									type: "warning",
									title: 'Insufficient Balance!',
									confirmButtonClass: 'btn btn-warning',
								})
							} else {
								
								if(data[0].planId != 1 || data[0].planId != 11)
								{
									
									$('#dateNote').removeClass('hide');
								}
								if(data[0].planId ==1 || data[0].planId ==11)
								{
									$('#dateNote').addClass('hide');

									$('input[name=paymentPlan]').prop('checked', false);
									$('input[name=paymentPlan]').attr("disabled", true);
								}

								
								$('#startDate').datetimepicker('clear');
								$('#endDate').datetimepicker('clear');
								$('#paymentPlan').prop('checked', false);
								

								$(".planId").val(data[0].planId);
								$("#title").val(data[0].title);
								$("#price").val(data[0].price);
								$("#quarterlyAmount").val(data[0].quarterlyAmount);
								$("#semiAnnualAmount").val(data[0].semiAnnualAmount);
								$("#annualAmount").val(data[0].annualAmount);
								$("#contactLimit").val(data[0].contactLimit);
								$("#galleryImageLimit").val(data[0].galleryImageLimit);
								$("#infoSectionLiimit").val(data[0].infoSectionLiimit);
								$("#infoContentLimit").val(data[0].infoContentLimit);

								$("#imageInfographLimit").val(data[0].imageInfographLimit);
								$("#videoInfographLimit").val(data[0].videoInfographLimit);
								$("#formLimit").val(data[0].formLimit);
								$("#cardLimit").val(data[0].cardLimit);
								$("#imageCarouselLimit").val(data[0].imageCarouselLimit);
								$("#subCardLimit").val(data[0].subCardLimit);
								$("#imageInfographSectionLimit").val(data[0].imageInfographSectionLimit);;
								$("#videoInfographSectionLimit").val(data[0].videoInfographSectionLimit);

								$("#smsLimit").val(data[0].defaultEmail);
								$("#emailLimit").val(data[0].defaultSms);

								// CheckBoxes
								if (data[0].isDedicatedURL == 'Yes')
									$('#allowVideoProfile').prop('checked', true);
								else
									$('#allowVideoProfile').prop('checked', false);

								if (data[0].allowThemeEditing == 'Yes')
									$("#allowThemeEditing").prop('checked', true);
								else
									$("#allowThemeEditing").prop('checked', false);

								if (data[0].allowLogoInclusion == 'Yes')
									$("#allowLogoInclusion").prop('checked', true);
								else
									$("#allowLogoInclusion").prop('checked', false);

								if (data[0].allowPhotoInclusion == 'Yes')
									$("#allowPhotoInclusion").prop('checked', true);
								else
									$("#allowPhotoInclusion").prop('checked', false);

								if (data[0].allowVideoProfile == 'Yes')
									$("#allowVideoProfile").prop('checked', true);
								else
									$("#allowVideoProfile").prop('checked', false);

								if (data[0].isWhiteLabel == 'Yes')
									$("#isWhiteLabel").prop('checked', true);
								else
									$("#isWhiteLabel").prop('checked', false);

								if (data[0].isCrmIntergration == 'Yes')
									$("#isCrmIntergration").prop('checked', true);
								else
									$("#isCrmIntergration").prop('checked', false);

								if (data[0].isPDFSubscription == 'Yes')
									$("#isPDFSubscription").prop('checked', true);
								else
									$("#isPDFSubscription").prop('checked', false);

								if (data[0].isCouponServices == 'Yes')
									$("#isCouponServices").prop('checked', true);
								else
									$("#isCouponServices").prop('checked', false);

								if (data[0].chatRoom == 'Yes')
									$("#chatRoom").prop('checked', true);
								else
									$("#chatRoom").prop('checked', false);

								if (data[0].chatemail == 'Yes')
									$("#email").prop('checked', true);
								else
									$("#email").prop('checked', false);

								if (data[0].smschk == 'Yes')
									$("#smschk").prop('checked', true);
								else
									$("#smschk").prop('checked', false);

								if (data[0].geolocation == 'Yes')
									$("#geolocation").prop('checked', true);
								else
									$("#geolocation").prop('checked', false);

								if (data[0].invoice == 'Yes')
									$("#invoice").prop('checked', true);
								else
									$("#invoice").prop('checked', false);

								if (data[0].pushNotification == 'Yes')
									$("#pushNotification").prop('checked', true);
								else
									$("#pushNotification").prop('checked', false);

								if (data[0].probizcaplus == 'Yes')
									$("#probizcaplus").prop('checked', true);
								else
									$("#probizcaplus").prop('checked', false);

								if (data[0].shoppingCart == 'Yes')
									$("#shoppingCart").prop('checked', true);
								else
									$("#shoppingCart").prop('checked', false);

								if (data[0].statistics == 'Yes')
									$("#statistics").prop('checked', true);
								else
									$("#statistics").prop('checked', false);

								if (data[0].support == 'Yes')
									$("#support").prop('checked', true);
								else
									$("#support").prop('checked', false);

								if (data[0].todo == 'Yes')
									$("#todo").prop('checked', true);
								else
									$("#todo").prop('checked', false);

								if (data[0].surveychk == 'Yes')
									$("#surveychk").prop('checked', true);
								else
									$("#surveychk").prop('checked', false);


								if (data[0].customQrFormchk == 'Yes')
									$("#customQrFormchk").prop('checked', true);
								else
									$("#customQrFormchk").prop('checked', false);



								$("#socialfeed").val(data[0].socialfeed);




								// Swal.fire({
								// type: "success",
								// title: 'Plan updated successfully.',
								// text: 'Upgraded.',
								// confirmButtonClass: 'btn btn-success',
								// });

								// $('#frmUpdateScubscription').submit();
							}
						}
					});
				}, function() {});

				// $('#planId').val(curPlanId).trigger('change');
			// }
		});
	</script>
</body>

</html>