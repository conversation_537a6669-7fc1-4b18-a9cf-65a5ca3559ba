<?php
include('../includes/commonfun.php');
include("../includes/config.php");
include("../class/clsDB.php");
include('../class/clsSystemUser.php');
include('../class/clsBusiness.php');
include('../class/clsEmployee.php');
include('../class/clsCustomer.php');
include('../class/clsAffiliate.php');

if (isset($_GET['loggedUserEmail']) && isset($_GET['role'])) {

    $loggedUserEmail = $_GET['loggedUserEmail'];
    $parent_id = isset($_GET['parent_id']) ? DecodeQueryData($_GET['parent_id']) : 0;
    $loggedUserId = isset($_GET['loggedUserId']) ? DecodeQueryData($_GET['loggedUserId']) : 0;
    $role = $_GET['role'];

    $localDomain = 'http://192.168.1.26:4003';
    $liveDomain = CHAT_NOTIFICATION_SERVICE;

    $API_KEY = CHAT_APIKEY; // or use CHAT_APIKEY for live

    $url = $liveDomain . "/api/v1/notifications/get";

    $params = [
        'email' => $loggedUserEmail,
        'role' => $role
    ];

    $queryString = http_build_query($params);
    $fullUrl = $url . '?' . $queryString;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $fullUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Optional timeout

    $headers = [
        'Content-Type: application/json',
        'x-api-key: ' . $API_KEY,
    ];

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        $responseArray = [
            'status' => 'Apierror',
            'message' => curl_error($ch)
        ];
    } else {
        $responseArray = [
            'status' => 'Apisuccess',
            'response' => json_decode($response, true)
        ];
    }

    curl_close($ch);

    header('Content-Type: application/json');
    echo json_encode($responseArray);
    exit;
}
exit;
