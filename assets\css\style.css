p.parsley-success {
    color: #468847;
    background-color: #DFF0D8;
    border: 1px solid #D6E9C6;
}

p.parsley-error {
    color: #B94A48;
    background-color: #F2DEDE;
    border: 1px solid #EED3D7;
}

ul.parsley-errors-list {
    list-style: none;
    color: #E74C3C;
    padding-left: 0;
}

input.parsley-error,
textarea.parsley-error,
select.parsley-error {
    background: #FAEDEC;
    border: 1px solid #E85445;
}


.validate-field {
    color: red;
    margin-left: 2px;
}

.titlefont {
    font-size: 14px;
}

.classified-link {
    position: absolute;
    left: 0;
}


/*------------------------------ End parsley -------------------------------------------*/

.validate-field {
    color: red;
    margin-left: 2px;
}

.hide {
    display: none;
}

#loading-div-background {
    display: none;
    position: fixed;
    z-index: 1000;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: rgba(255, 255, 255, .8) url('http://i.stack.imgur.com/FhHRx.gif') 50% 50% no-repeat;
}

#loading-div {
    width: 300px;
    height: 150px;
    background-color: #fff;
    border: 4px solid #40C2A6;
    text-align: center;
    color: #202020;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -150px;
    margin-top: -100px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}


/* single line trim css */

.single-line-trim {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* / body scrollbar / */

*::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

*::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

*::-webkit-scrollbar-thumb {
    background: rgb(153, 148, 148);
    border-radius: 10px;
}


/* page Loader Start */
.overflow-hidden {
    overflow: hidden;
}

#bg-loader {
    min-height: 100vh;
    min-width: 100vw;
    background: #ffffff;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    overflow: hidden;
}

.ping {
    --uib-size: 50px;
    --uib-speed: 1s;
    --uib-color: #3950cf;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%);
    height: var(--uib-size);
    width: var(--uib-size);
    z-index: 99999;
}

.ping::before,
.ping::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    border-radius: 50%;
    background-color: var(--uib-color);
    animation: pulse7132 var(--uib-speed) linear infinite;
    transform: scale(0);
    opacity: 0;
}

.ping::after {
    animation-delay: calc(var(--uib-speed) / -2);
}

@keyframes pulse7132 {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* page Loader End */

/* Add Task Form */
.assign-employee>.parsley-errors-list {
    margin-left: 14rem;
    margin-top: 5px;

}