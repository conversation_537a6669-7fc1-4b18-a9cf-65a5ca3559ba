<?php
class clsBusiness
{	
	var $businessId  = 0;	
	var $retEmployeeId  = 0;	
    var $planId = 0;
    var $businessName = '';
    var $email = '';
    var $phoneNo = '';
    var $address = '';
    var $address2 = '';
    var $city = '';
    var $stateId = 0;
    var $countryId = 0;
    var $zipCode ='';
    var $logoName = '';
    var $website = '';
    var $businessCode = '';
	var $industeryId = 0;
    var $slug = '';
    var $link_domain_name = '';
    var $slogan = '';
    var $aboutusTitle = '';
    var $designation = '';
    var $whats_app_number = '';
    var $map_address = '';
    var $tracking_id = '';
    var $google_plus = '';
    var $facebook = '';
    var $twitter = '';
    var $linked_in = '';
    var $youtube = '';
    var $instagram = '';
    var $google_play_app_url = '';
    var $itune_app_url = '';
    var $approvedStatus = '';
    var $businessLogo = '';
    var $firstName = '';
    var $lastName = '';
    var $isPrimary = '1';
  
	function SaveBusiness($businessId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		if($businessId > 0)
		{
			$sql = "UPDATE business SET 
						industeryId = '".addslashes($this->industeryId)."',												
						businessName = '".addslashes($this->businessName)."',																
						email = '".addslashes($this->email)."',																
						phoneNo = '".addslashes($this->phoneNo)."',																
						address = '".addslashes($this->address)."',																
						address2 = '".addslashes($this->address2)."',																
						city = '".addslashes($this->city)."',																
						zipCode = '".addslashes($this->zipCode)."',																
						slug = '".addslashes($this->slug)."',																
						website = '".addslashes($this->website)."',					
						link_domain_name = '".addslashes($this->link_domain_name)."',					
						slogan = '".addslashes($this->slogan)."',					
						description = '".addslashes($this->description)."',																
						map_address = '".addslashes($this->map_address)."',					
						tracking_id = '".addslashes($this->tracking_id)."',					
						google_plus = '".addslashes($this->google_plus)."',					
						facebook = '".addslashes($this->facebook)."',					
						twitter = '".addslashes($this->twitter)."',					
						linked_in = '".addslashes($this->linked_in)."',					
						youtube = '".addslashes($this->youtube)."',					
						instagram = '".addslashes($this->instagram)."',					
						google_play_app_url = '".addslashes($this->google_play_app_url)."',					
						itune_app_url = '".addslashes($this->itune_app_url)."',					
						businessCode = '".addslashes($this->businessCode)."',					
						aboutusTitle = '".addslashes($this->aboutusTitle)."',					
						stateId = ".$this->stateId.",						
						planId = ".$this->planId."						
						WHERE businessId= ".$businessId;	
			// echo $sql;exit;											
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
			$sql = "INSERT INTO business(industeryId, businessName,email,phoneNo,address,address2,city,zipCode,
										slug,website,link_domain_name,slogan,aboutusTitle,description,
										map_address,tracking_id,google_plus,facebook,twitter,linked_in,youtube,
										instagram,google_play_app_url,itune_app_url,businessCode,stateId,planId) 
								VALUES(		
									 '".addslashes($this->industeryId)."',												 						  				
									  '".addslashes($this->businessName)."',								
									  '".addslashes($this->email)."',								
									  '".addslashes($this->phoneNo)."',																	 								
									  '".addslashes($this->address)."',								
									  '".addslashes($this->address2)."',								
									  '".addslashes($this->city)."',								
									  '".addslashes($this->zipCode)."',								
									  '".addslashes($this->slug)."',								
									  '".addslashes($this->website)."',	
									  '".addslashes($this->link_domain_name)."',	
									  '".addslashes($this->slogan)."',	
									  '".addslashes($this->aboutusTitle)."',										 	
									  '".addslashes($this->description)."',										 	
									  '".addslashes($this->map_address)."',	
									  '".addslashes($this->tracking_id)."',	
									  '".addslashes($this->google_plus)."',	
									  '".addslashes($this->facebook)."',	
									  '".addslashes($this->twitter)."',	
									  '".addslashes($this->linked_in)."',	
									  '".addslashes($this->youtube)."',	
									  '".addslashes($this->instagram)."',	
									  '".addslashes($this->google_play_app_url)."',	
									  '".addslashes($this->itune_app_url)."',	
									  '".addslashes($this->businessCode)."',	
									  ".$this->stateId.",															
									  ".$this->planId."															
									  )";
            // echo $sql;exit;									  
			$businessId = $objDB->ExecuteInsertQuery($sql);
		}
		
		unset($objDB);
		return $businessId;
	}
	
	function GetBusinessDetails($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM business WHERE businessId = ".$businessId;
		// echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function DeleteBusiness($businessId)
	{		
		$result="";
		if($businessId > 0)
		{			
			$objDB = new clsDB();
			$sql = 'DELETE FROM business WHERE businessId='.$businessId;		
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} 

	function DeleteBusinessEmployee($businessId)
	{		
		$result="";
		if($businessId > 0)
		{			
			$objDB = new clsDB();
			$sql = 'DELETE FROM employee WHERE businessId='.$businessId;		
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} 

	function DeleteBusinessContact($businessId)
	{		
		$result="";
		if($businessId > 0)
		{			
			$objDB = new clsDB();
			$sql = 'DELETE FROM businesscustomer WHERE businessId='.$businessId;		
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} 
	function DeleteBusinessAffiliate($businessId)
	{		
		$result="";
		if($businessId > 0)
		{			
			$objDB = new clsDB();
			$sql = 'DELETE FROM businessaffiliate WHERE businessId='.$businessId;		
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} 
	function GetAllBusiness($status = -1,$email='', $industeryType=0, $businessType = 0)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.*,business.createdDate AS BusinessCreatedAt, business.phoneNo as businessPhoneNumber,employee.*,employee.status AS empStatus,business.email AS businessEmail,business.address As businessAddress,
		        business.city As businessCity,business.stateId As businessStateId,business.zipCode As businessZipCode,adtl1.*,adtl2.defaultCount as dfSMS, adtl2.dfUsed AS dfsmsU,adtl2.purchasedCount AS smsPurCount,adtl2.purUsed AS smsPurUsed,businessplansubscriptiondetails.subscriptionStartDate,businessplansubscriptiondetails.subscriptionEndDate
				FROM employee
				INNER JOIN business ON employee.businessId = business.businessId 
				INNER JOIN businessplansubscriptiondetails ON business.businessId=businessplansubscriptiondetails.businessId
				INNER JOIN addondetails as adtl1 ON business.businessId = adtl1.businessId AND adtl1.addonNmId=1 
				INNER JOIN addondetails as adtl2 ON business.businessId = adtl2.businessId AND adtl2.addonNmId=2  WHERE employee.isPrimary=1";	 
		if($status != -1)
		{
		   $sql .=" AND business.status='".$status."'";
		}
		if($email)
		{
		   $sql .=" AND employee.email='".$email."'";
		}  

		if($businessType)
		{
		   $sql .=" AND  business.industeryId > 0";
		} 

		if($industeryType)
		{
			$sql .=" AND  business.industeryId ='".$industeryType."'";

		} 
        $sql .=" GROUP BY businessName ASC";		
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	function CheckLogin($email)
	{
		$employeeId = "0";
		$objDB = new clsDB();
		$sql = "SELECT employeeId FROM employee WHERE email='".$email."'";
		// echo $sql;exit;	s
		$employeeId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $employeeId;
	}

	// sumit added 09OCt23
	function CheckBusinessEmailForPrimaryUser($email)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE email='".$email."' AND isPrimary=1";
		// echo $sql;exit;	
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function CheckBusinessEmailForSecondaryBusiness($email)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE email='".$email."' AND isPrimary=1 AND isPrimaryUser=0 ORDER BY createdDate ASC LIMIT 1";
		// echo $sql;exit;	
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	function SetBusinessStatus($id, $status)
	{
		if($id > 0)
		{
			$objDB = new clsDB();
			$sql = "Update business set status =".$status." Where businessId = ".$id; 
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	
	function GetBusinessName($businessId)
	{
		$businessName = "";
		$objDB = new clsDB();
		$sql = "SELECT businessName FROM business WHERE businessId='".$businessId."'";
		// echo $sql;exit;	
		$businessName = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $businessName;
	}
	
	function GetBusinessPlanName($businessId,$planId)
	{
		$title = "";
		$objDB = new clsDB();
		$sql = "SELECT title FROM businessplansubscriptiondetails WHERE businessId='".$businessId."' AND planId='".$planId."'";
		// echo $sql;
		$title = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $title;
	}
	
	function GetBusinessPlanId($businessId)
	{
		$planIds = "0";
		$objDB = new clsDB();
		$sql = "SELECT planId FROM businessplansubscriptiondetails WHERE businessId='".$businessId."'";
		// echo $sql;exit;	
		$planIds = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $planIds;
	}
	
	function GetAllBusinessEmployees($status = -1, $businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee ";
        if($businessId!='')
		{
		   $sql .=" WHERE businessId='".$businessId."' AND isPrimary='0' ";
		}  		
		if($status !=-1)
		{
		   $sql .=" AND status='".$status."'";
		}    
		// echo $sql;exit;
		$sql .=" ORDER BY firstName ASC";		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetBusinessEmployeeCount($businessId)
	{
		$employeeCount = "0";
		$objDB = new clsDB();
		$sql = "SELECT COUNT(*) FROM employee WHERE businessId='".$businessId."' AND isPrimary='0' ";
		// echo $sql;exit;	
		$employeeCount = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $employeeCount;
	}
	
	function GetBusinessCustomerCount($businessId,$status)
	{
		$employeeCount = "0";
		$objDB = new clsDB();
		$sql = "SELECT COUNT(*) FROM businesscustomer 
		        INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
		        WHERE businesscustomer.businessId='".$businessId."'";
		if($status!='')
		{
		    $sql .=" AND customer.status='".$status."' ";
		}
		// echo $sql;exit;	
		$employeeCount = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $employeeCount;
	}
	
	function GetBusinessCustomerList($businessId,$isFavorite,$customerId=0)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,customer.*,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE business.businessId='".$businessId."' AND customer.status=1";
		if($isFavorite >0)
        {
		    $sql .=" AND customer.isFavorite='1'";
        }
        if($customerId >0)
        {
		    $sql .=" AND customer.customerId='".$customerId."'";
        }	
			$sql	.=" ORDER BY  businesscustomer.createdDate desc";			
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetBusinessContactReportData($businessId,$fromDate,$toDate)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,customer.*,businesscustomer.*,businesscustomer.createdDate AS registeredDate,contactnotes.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				LEFT JOIN contactnotes ON contactnotes.customerId = customer.customerId
				WHERE business.businessId='".$businessId."' AND customer.status=1";

		if($fromDate !='' && $toDate !='')
		{
			$sql .=" AND STR_TO_DATE(businesscustomer.createdDate , '%Y-%m-%d' ) >= '$fromDate' AND STR_TO_DATE(businesscustomer.createdDate , '%Y-%m-%d' ) <= '$toDate' ";	
		}		
		
		$sql	.=" ORDER BY  businesscustomer.createdDate desc";			
		
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	
	function GetBusinessCustomerListForVboutAPI($businessId,$isFavorite,$customerId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,customer.*,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE business.businessId='".$businessId."' AND businesscustomer.isTransferedToVbout=0";
		if($isFavorite >0)
        {
		    $sql .=" AND customer.isFavorite='1'";
        }
        if($customerId >0)
        {
		    $sql .=" AND customer.customerId='".$customerId."'";
        }	
			$sql	.=" ORDER BY  businesscustomer.createdDate desc";			
// 		echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	
	function GetGroupWiseBusinessCustomerList($businessId,$isFavorite,$customerId,$businessGroupId)
	{		
		$rows = "";
		$objDB = new clsDB();
		if($businessGroupId)
		{	

			$sql = "SELECT business.businessId,customer.*,businesscustomer.*,businessgroupdetails.businessGroupId as businessGroupId ,businessgroup.title as title
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				left JOIN businessgroupdetails ON businessgroupdetails.customerId = customer.customerId 
				left JOIN businessgroup ON businessgroup.businessGroupId = businessgroupdetails.businessGroupId
				WHERE business.businessId='".$businessId."'";

		}
		else
		{
			$sql = "SELECT business.businessId,customer.*,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE business.businessId='".$businessId."' AND customer.status=1";
		}
		
		if($isFavorite >0)
        {
		    $sql .=" AND customer.isFavorite='1'";
        }
        if($customerId >0)
        {
		    $sql .=" AND customer.customerId='".$customerId."'";
        }	
		if($businessGroupId >0)
        {
		    $sql .=" AND businessgroupdetails.businessGroupId='".$businessGroupId."'";
        }
		
		$sql	.=" ORDER BY CONCAT(customer.firstName, ' ', customer.lastName) ASC";
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);  
		return $rows;
	}
	
	function GetBusinessAffiliateCount($businessId,$status)
	{
		$employeeCount = "0";
		$objDB = new clsDB();
		$sql = "SELECT COUNT(*) FROM businessaffiliate 
		        INNER JOIN affiliate ON affiliate.affiliateId = businessaffiliate.affiliateId 
		        WHERE businessaffiliate.businessId='".$businessId."'";
		if($status!='')
		{
		    $sql .=" AND affiliate.status='".$status."' ";
		}
		// echo $sql;exit;	
		$employeeCount = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $employeeCount;
	}
	
	function GetBusinessAffiliateName($affiliateId)
	{
		$affiliateName = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM affiliate WHERE affiliateId='".$affiliateId."'";
		// echo $sql;exit;	
		$affiliateName = $objDB->GetDataRow($sql);
		unset($objDB);
		return $affiliateName;
	}
	
	function GetBusinessEmployeeName($employeeId)
	{
		$employeeName = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE employeeId='".$employeeId."'";
		// echo $sql;exit;	
		$employeeName = $objDB->GetDataRow($sql);
		unset($objDB);
		return $employeeName;
	}

	function GetBusinessCustomerName($customerId)
	{
		$employeeName = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM `businesscustomer`
		INNER JOIN customer on businesscustomer.customerId=customer.customerId
		WHERE businesscustomer.customerId='".$customerId."'";
		// echo $sql;exit;	
		$employeeName = $objDB->GetDataRow($sql);
		unset($objDB);
		return $employeeName;
	}
	 
	function GetNewContacts($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT customer.*,businesscustomer.* FROM customer 
				INNER JOIN businesscustomer ON customer.customerId = businesscustomer.customerId 
				WHERE  businesscustomer.customerId NOT IN (SELECT customerId FROM businessgroupdetails)
				AND businesscustomer.businessId='".$businessId."'";
				
			$sql	.=" ORDER BY  businesscustomer.createdDate desc";			
				
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetEmployeeNewContacts($businessId,$employeeId,$refType)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT customer.*,businesscustomer.* FROM customer 
				INNER JOIN businesscustomer ON customer.customerId = businesscustomer.customerId 
				WHERE  businesscustomer.customerId NOT IN (SELECT customerId FROM businessgroupdetails)
				AND businesscustomer.businessId='".$businessId."' AND businesscustomer.refType= '".$refType."' AND businesscustomer.refId='".$employeeId."'";
		
		$sql	.=" ORDER BY  businesscustomer.createdDate desc";			

		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}	
	
	function GetMaxBusinessId()
	{
		$maxId = "0";
		$objDB = new clsDB();
		$sql = "SELECT MAX(businessId) FROM business";		
		$maxId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $maxId;
	}
	
	function SaveBusinessAsEmployee($retEmployeeId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		if($retEmployeeId > 0)
		{
			$sql = "UPDATE employee SET 													
						firstName = '".addslashes($this->firstName)."',																
						lastName = '".addslashes($this->lastName)."',		
						businessId = '".addslashes($this->businessId)."',		
						email = '".addslashes($this->email)."',		
						isPrimaryUser = '".addslashes($this->isPrimaryUser)."'	
						WHERE employeeId= ".$retEmployeeId;	
			// echo $sql;exit;											
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
		$sql = "INSERT INTO employee(firstName,lastName,isPrimary,businessId,stateId,email,isPrimaryUser) 
							VALUES(								  				
								  '".addslashes($this->firstName)."',								
								  '".addslashes($this->lastName)."',								
								  '".addslashes($this->isPrimary)."',																	 								
								  '".addslashes($this->businessId)."',															
								  '".addslashes($this->stateId)."',															
								  '".addslashes($this->email)."',															
								  '".addslashes($this->isPrimaryUser)."'														
								  )";
            // echo $sql;exit;									  
			$retEmployeeId =$objDB->ExecuteInsertQuery($sql);
		}
		unset($objDB);
		return $retEmployeeId;
	}
	
	function CheckBusinesAsAnEmployee($businessId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT employeeId FROM employee 		       
		        WHERE businessId='".$businessId."' AND isPrimary='1'";		
		// echo $sql;exit;	
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function CheckIsBusinessOwner($businessId,$employeeId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee 		       
		        WHERE businessId='".$businessId."' AND employeeId=$employeeId AND isPrimary='1'";		
		// echo $sql;exit;	
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	
	
	function GetBusinessEmployeeDetails($businessId)
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM employee WHERE businessId='".$businessId."' AND isPrimary='1'";
		// echo $sql;exit;	
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	
	function GetBusinessDetailsBySlug($slug)
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM business WHERE displayURLTag='".$slug."'";
		// echo $sql;exit;	
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	
	function SaveBusinessSocialMedia($businessId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		if($businessId > 0)
		{
			$sql = "UPDATE business SET 																							
						facebook = '".addslashes($this->facebook)."',					
						twitter = '".addslashes($this->twitter)."',					
						linked_in = '".addslashes($this->linked_in)."',					
						youtube = '".addslashes($this->youtube)."',					
						instagram = '".addslashes($this->instagram)."'
						WHERE businessId= ".$businessId;	
			// echo $sql;exit;											
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
			$sql = "INSERT INTO business(facebook,twitter,linked_in,youtube,
										instagram) 
								VALUES(								  				
									  '".addslashes($this->facebook)."',	
									  '".addslashes($this->twitter)."',	
									  '".addslashes($this->linked_in)."',	
									  '".addslashes($this->youtube)."',	
									  '".addslashes($this->instagram)."'										
									  )";
            // echo $sql;exit;									  
			$businessId = $objDB->ExecuteInsertQuery($sql);
		}
		
		unset($objDB);
		return $businessId;
	}
	
	
	function CheckDuplicateDisplayUrlTag($displayURLTag)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT businessId FROM business WHERE displayURLTag='".$displayURLTag."'";
		// echo $sql;exit;	
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}
	function GetReferredCustomer($customerId,$affiliateId,$employeeId,$businessId,$fromDate,$toDate)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT customer.customerId,customer.firstName,customer.lastName,customer.email,customer.phoneNo,businesscustomer.*
				FROM businesscustomer 
				left JOIN customer ON customer.customerId = businesscustomer.customerId 
				WHERE businesscustomer.businessId='".$businessId."'";	
		
		if($customerId >0)
        {
		    // $sql .="AND businesscustomer.refType= 1 AND businesscustomer.refId='".$customerId."'";
		    $sql .="AND businesscustomer.refId='".$customerId."'";
        }		
		if($employeeId >0)
        {
		    $sql .="AND businesscustomer.refType= 2 AND businesscustomer.refId='".$employeeId."'";
        }
		if($affiliateId >0)
        {
		    $sql .="AND businesscustomer.refType= 3 AND businesscustomer.refId='".$affiliateId."'";
        }
		if($fromDate !="" && $toDate !="")
		{
			$sql .="AND businesscustomer.createdDate between '$fromDate' AND '$toDate'";
		}
		// echo $sql;exit;	
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row; 
	} 
	function SetIsDisplayPaidContentToCustomer($customerId,$businessId,$isDisplayPaidContent)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "UPDATE businesscustomer SET 																							
		isDisplayPaidContent = '".$isDisplayPaidContent."'
		WHERE businessId= '".$businessId."' AND customerId = '".$customerId."'";
		// echo $sql;exit;	
		$objDB->ExecuteQuery($sql);	
		unset($objDB);
		return $row;
	}
	
	//Referred by report for referred by customer.
	function GetReferredByCustomersList($businessId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.refId,businesscustomer.refType, customer.*
				FROM businesscustomer
				join customer on customer.customerId = businesscustomer.refId
				WHERE businesscustomer.businessId='".$businessId."'
				GROUP BY businesscustomer.refId";		
		// echo $sql;exit;	
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row; 
	} 
	
	//Referred To Customer ID's to delete customer.
	function GetReferredToCustomersList($customerId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.*
				FROM businesscustomer
				WHERE businesscustomer.refId='".$customerId."'";
		
		//echo $sql;exit;	
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row; 
	} 
	function GetEmployeeCustomerList($businessId,$employeeId,$refType, $isFavorite = 0)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,customer.*,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE business.businessId='".$businessId."' AND businesscustomer.refType= '".$refType."' AND businesscustomer.refId='".$employeeId."'  ";
		if($isFavorite >0)
        {
		    $sql .=" AND customer.isFavorite='1'";
        }
		else
		{
			$sql .=" OR businesscustomer.employeeId='".$employeeId."'";
		}
		// $sql	.="ORDER BY  firstName asc, lastName asc";		
		$sql	.=" ORDER BY  businesscustomer.createdDate desc";			

		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetContactCustomerList($businessId,$employeeId,$refType,$fromDate,$toDate)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,customer.*,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE business.businessId='".$businessId."'  AND businesscustomer.refId='".$employeeId."' ";
		if($refType == 2)
		{
			$sql.=" AND businesscustomer.refType= 2";
		}	

		if($fromDate !="" && $toDate !="")
		{
			$sql .=" AND businesscustomer.createdDate between '$fromDate' AND '$toDate'";
		}
		
		$sql.=" AND businesscustomer.shareType IN(0,5,10) ";

		$sql	.="ORDER BY  firstName asc, lastName asc";			
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}


	function GetAffiliateCustomerList($businessId,$affiliateId,$refType, $isFavorite = 0)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,customer.*,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE business.businessId='".$businessId."' AND businesscustomer.refType= '".$refType."' AND businesscustomer.refId='".$affiliateId."'  ";
		if($isFavorite >0)
        {
		    $sql .=" AND customer.isFavorite='1'";
        }
		else
		{
			$sql .=" AND businesscustomer.affiliateId='".$affiliateId."'";
		}
		// $sql	.="ORDER BY  firstName asc, lastName asc";		
		$sql	.=" ORDER BY  businesscustomer.createdDate desc";			

		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetCustomerShareType($customerId,$businessId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT shareType FROM businesscustomer WHERE businessId='".$businessId."' AND customerId='".$customerId."'";
		// echo $sql;exit;	
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

	function GetProbizcaPlusBusiness()
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.*,employee.firstName,employee.lastName,employee.isPrimary FROM `business` INNER JOIN employee ON employee.businessId=business.businessId WHERE business.`planId` = 7 AND business.vboutApiKey !='' AND business.status=1 AND employee.isPrimary=1";		
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function UpdateisTransferedToVbout($customerId,$businessId,$response)
	{

		$row = "0";
		$objDB = new clsDB();
		$sql = "UPDATE businesscustomer SET 																							
		isTransferedToVbout = '1',vboutContactIdresp='$response'
		WHERE businessId= '".$businessId."' AND customerId = '".$customerId."'";
		//echo $sql;exit;	
		$objDB->ExecuteQuery($sql);	
		unset($objDB);
		return $row;

	}


	function GetAllBusinessContactList($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.customerId,customer.firstName,customer.lastName FROM businesscustomer
		        INNER JOIN business ON businesscustomer.businessId = business.businessId		        
		        RIGHT JOIN customer ON businesscustomer.customerId = customer.customerId		        
				WHERE business.businessId='".$businessId."' GROUP BY businesscustomer.customerId";
        // echo $sql;exit;				
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
}
?>