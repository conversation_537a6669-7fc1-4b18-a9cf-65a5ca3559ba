<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-develop',
        'version' => 'dev-develop',
        'reference' => 'bd40c70feda3609a7bff26abd16ccb54383d6bca',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'reference' => 'bd40c70feda3609a7bff26abd16ccb54383d6bca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clicksend/clicksend-php' => array(
            'pretty_version' => 'v5.0.81',
            'version' => '5.0.81.0',
            'reference' => 'd642c1cbde5e7e9e247fb65adc2eeec4019a4f82',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clicksend/clicksend-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clue/stream-filter' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'reference' => '049509fef80032cb3f051595029ab75b49a3c2f7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/stream-filter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.16.0',
            'version' => '4.16.0.0',
            'reference' => '523407fb06eb9e5f3d59889b3978d5bfe94299c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'reference' => '4dd1e007f22a927ac77da5a3fbb067b42d3bc224',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.26.0',
            'version' => '1.26.0.0',
            'reference' => 'f1f0d0319e2e7750ebfaa523c78819792a9ed9f7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/cloud-core' => array(
            'pretty_version' => 'v1.49.4',
            'version' => '1.49.4.0',
            'reference' => '6723a3fde6cc7a307a21ddbf7fce9cf6fab61833',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/cloud-storage' => array(
            'pretty_version' => 'v1.30.2',
            'version' => '1.30.2.0',
            'reference' => 'b7f74ec1b701d56945cbc6c20345e2d21b1b3545',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-storage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/crc32' => array(
            'pretty_version' => 'v0.1.0',
            'version' => '0.1.0.0',
            'reference' => 'a8525f0dea6fca1893e1bae2f6e804c5f7d007fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/crc32',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.1',
            'version' => '7.8.1.0',
            'reference' => '41042bc7ab002487b876a0683fc8dce04ddce104',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'reference' => '67ab6e18aaa14d753cc148911d273f6e6cb6721e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'e4490cabc77465aaee90b20cfc9a770f8c04be6b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'http-interop/http-factory-guzzle' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => '8f06e92b95405216b237521cc64c804dd44c4a81',
            'type' => 'library',
            'install_path' => __DIR__ . '/../http-interop/http-factory-guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/clock' => array(
            'pretty_version' => '1.2',
            'version' => '1.2.0.0',
            'reference' => '49e103382ca36cb2bc2e86ff3b8d11d44d0e0b31',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/firebase-php' => array(
            'pretty_version' => '5.19.0',
            'version' => '5.19.0.0',
            'reference' => '3e056f77c3499e86bfbf9e5af8776c2fb070453d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/firebase-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/firebase-tokens' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => 'b39d7c3a78d0912c9a617cd42d4bd356209b1b91',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/firebase-tokens',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lcobucci/jwt' => array(
            'pretty_version' => '3.4.6',
            'version' => '*******',
            'reference' => '3ef8657a78278dfeae7707d51747251db4176240',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '2.1.0',
            'version' => '*******',
            'reference' => 'c4c5803cc1f93df3d2448478ef79394a5981cc58',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mailgun/mailgun-php' => array(
            'pretty_version' => 'v4.2.0',
            'version' => '*******',
            'reference' => '6e31693370a254522118b9961ca675823c76c3c0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mailgun/mailgun-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.9.3',
            'version' => '2.9.3.0',
            'reference' => 'a30bfe2e142720dfa990d0a7e573997f5d884215',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v8.2.0',
            'version' => '8.2.0.0',
            'reference' => '170a236a588d177c2aa7447ce490a030ca68e6f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-http-message-shim' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => '3206e6b80b6d2479e148ee497e9f2bebadc919db',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-http-message-shim',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-log-aware-trait' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'reference' => '7a077416e8f39eb626dee4246e0af99dd9ace275',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-log-aware-trait',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'bbb69a935c2cbb0c03d7f481a238027430f6440b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.11.1',
            'version' => '1.11.1.0',
            'reference' => '7284c22080590fb39f2ffa3e9057f10a4ddd0e0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.8.4',
            'version' => '1.8.4.0',
            'reference' => 'a867478eae49c9f59ece437ae7f9506bfaa27483',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
                1 => '1.0',
            ),
        ),
        'php-http/client-common' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '0cfe9858ab9d3b213041b947c881d5b19ceeca46',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/client-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
                1 => '1.0',
            ),
        ),
        'php-http/curl-client' => array(
            'pretty_version' => '2.3.1',
            'version' => '2.3.1.0',
            'reference' => '085570be588f7cbdc4601e78886eea5b7051ad71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/curl-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.20.0',
            'version' => '1.20.0.0',
            'reference' => '82fe4c73ef3363caed49ff8dd1539ba06044910d',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/httplug' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'reference' => '5cad731844891a4c282f3f3e1b582c46839d22f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message' => array(
            'pretty_version' => '1.16.2',
            'version' => '********',
            'reference' => '06dd5e8562f84e641bf929bfe699ee0f5ce8080a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/multipart-stream-builder' => array(
            'pretty_version' => '1.4.2',
            'version' => '*******',
            'reference' => '10086e6de6f53489cca5ecc45b6f468604d3460e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/multipart-stream-builder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/promise' => array(
            'pretty_version' => '1.3.1',
            'version' => '*******',
            'reference' => 'fc85b1fba37c169a69a07ef0d5a8075770cc1f83',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.25.2',
            'version' => '********',
            'reference' => 'a317a09e7def49852400a4b3eca4a4b0790ceeb5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '*******',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
                1 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
                1 => '^1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
                1 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '*******',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'riverline/multipart-parser' => array(
            'pretty_version' => '2.1.2',
            'version' => '*******',
            'reference' => '7a9f4646db5181516c61b8e0225a343189beedcd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../riverline/multipart-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rize/uri-template' => array(
            'pretty_version' => '0.3.6',
            'version' => '*******',
            'reference' => '34efe65c79710eed0883884f2285ae6d4a0aad19',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rize/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/php-http-client' => array(
            'pretty_version' => '3.14.4',
            'version' => '********',
            'reference' => '6d589564522be290c7d7c18e51bcd8b03aeaf0b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/php-http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid' => array(
            'pretty_version' => '8.0.1',
            'version' => '*******',
            'reference' => '285381257100b73aa50d8d70f0bcfb1f48b63747',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/sendgrid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid-php' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '2.5.0.0',
            'reference' => 'ecf0459643ec963febfb9a5d529dcd93656006a4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'starkbank/ecdsa' => array(
            'pretty_version' => '0.0.5',
            'version' => '0.0.5.0',
            'reference' => '484bedac47bac4012dc73df91da221f0a66845cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../starkbank/ecdsa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stella-maris/clock' => array(
            'pretty_version' => '0.1.7',
            'version' => '*******',
            'reference' => 'fa23ce16019289a18bb3446fdecd45befcdd94f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stella-maris/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v12.5.0',
            'version' => '********',
            'reference' => 'a4249b4a90437844f6c35e8701f8c68acd206f56',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v2.5.3',
            'version' => '*******',
            'reference' => '80d075412b557d41002320b96a096ca65aa2c98d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '********',
            'reference' => '74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '********',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '42292d99c55abe617799667f454222c54c60e229',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '********',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '********',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
