<?php
// phpinfo();

//include classes and config file here
include('./includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsBusinessDashboard.php');
include('../class/clsBusinessContactOpportunity.php');
include('../class/clsNotification.php');
include('../class/clsAddonMaster.php');
include('../class/clsSurvey.php');
include('../class/clsVisitorCount.php');
include('../class/clsBusiness.php');




//variables
$employeeCount = 0;
$affiliateCount = 0;
$serviceCount = 0;
$contactCount = 0;
$rowNewContact = '';
$businessId = $_SESSION["loggedBusinessId"];

//object
$objBusinessDashboard = new clsBusinessDashboard();

//get data count
$employeeCount = $objBusinessDashboard->GetBusinessEmployeeCount($businessId);
$affiliateCount = $objBusinessDashboard->GetBusinessAffiliateCount($businessId);
$serviceCount = $objBusinessDashboard->GetBusinessServiceCount($businessId);
$rowNewContact = $objBusinessDashboard->GetNewContacts($businessId);
$contactCount = mysqli_num_rows($rowNewContact);

//Hit Rate
$objBusinessContactOpportunity = new clsBusinessContactOpportunity();
$totalOpportunities = $objBusinessContactOpportunity->GetAllOpportunities($businessId, 0);
$totalOpportunities = mysqli_num_rows($totalOpportunities);

$openOpportunities = $objBusinessContactOpportunity->GetAllOpportunities($businessId, 0, 1);
$openOpportunities = mysqli_num_rows($openOpportunities);
$openOpportunitiesPer = get_percentage($totalOpportunities, $openOpportunities);

$closedWonOpportunities = $objBusinessContactOpportunity->GetAllOpportunities($businessId, 0, 2);
$closedWonOpportunities = mysqli_num_rows($closedWonOpportunities);
$closedLostOpportunities = $objBusinessContactOpportunity->GetAllOpportunities($businessId, 0, 3);
$closedLostOpportunities = mysqli_num_rows($closedLostOpportunities);
$inactiveOpportunities = $objBusinessContactOpportunity->GetAllOpportunities($businessId, 0, 4);
$inactiveOpportunities = mysqli_num_rows($inactiveOpportunities);

// closed Won And Lost And Inactive Opportunities Per
$closedWonAndLostAndInactiveOpportunitiesTotal = $closedLostOpportunities + $closedWonOpportunities;
$closedWonAndLostAndInactiveOpportunitiesPer = get_percentage($totalOpportunities, $closedWonAndLostAndInactiveOpportunitiesTotal);

//closed Won And Lost Opportunities Per
$closedWonAndLostOpportunitiesTotal = $closedLostOpportunities + $closedWonOpportunities;
$closedWonAndLostOpportunitiesPer = get_percentage($totalOpportunities, $closedWonAndLostOpportunitiesTotal);

//Current Week Revenue
$totalAmountOfCurrentWeekRevenue = $objBusinessContactOpportunity->GetAllOpportunitiesCountByClosedWon($businessId, 1, 0);
$totalEarning = $objBusinessContactOpportunity->GetTotalEarnings($businessId);

//check the current day
$currentWeekStartdate = date('Y-m-d', strtotime('last Sunday')); //take the last monday   

//Get Current Week saturday
$currentWeekEnddate = date('Y-m-d', strtotime('next Saturday'));

$currentDays = new DatePeriod(
    new DateTime($currentWeekStartdate),
    new DateInterval('P1D'),
    new DateTime($currentWeekEnddate . '+1 day')
);

//Current Week Revenue To graph
$currentWeekAmounts = [];
foreach ($currentDays as $key => $value) {
    $currentWeekdate = $value->format('Y-m-d');
    $currentWeekRevenuesByDateAmount = $objBusinessContactOpportunity->GetAllOpportunitiesCountByClosedWon($businessId, 0, 0, $currentWeekdate);
    $currentWeekAmounts[] = $currentWeekRevenuesByDateAmount ? $currentWeekRevenuesByDateAmount : 0;
}

//Get Last Week dates
$lastWeekStartdate = date('Y-m-d', strtotime('last sunday -7 days'));
$lastWeekEnddate = date('Y-m-d', strtotime($lastWeekStartdate . '+6 days'));
$previousDays = new DatePeriod(
    new DateTime($lastWeekStartdate),
    new DateInterval('P1D'),
    new DateTime($lastWeekEnddate . '+1 day')
);

// Prevous Week Revenue To graph
$previousWeekAmounts = [];
foreach ($previousDays as $key => $value) {
    $previousWeekdate = $value->format('Y-m-d');
    $prevousWeekRevenuesByDateAmount = $objBusinessContactOpportunity->GetAllOpportunitiesCountByClosedWon($businessId, 0, 0, $previousWeekdate);
    $previousWeekAmounts[] = $prevousWeekRevenuesByDateAmount;
}

//Previous Week Revenue
$totalAmountOfPreviousWeekRevenue = $objBusinessContactOpportunity->GetAllOpportunitiesCountByClosedWon($businessId, 0, 1);

// ------------------------------------------------------------------------------------------------
// New changes to show MTD/YTD for Email/SMS
$objBusiness = new clsBusiness();
//get business details
$rows = $objBusiness->GetBusinessDetails($businessId);
$EmailpurchasedCount = $rows['totalPurchaseEmail'];
$smsPurCount = $rows['totalPurchaseSms'];

$objAddonMaster = new clsAddonMaster();

$dfCounts = $objAddonMaster->GetAllfreeAddonsdtls($businessId);
$defaultEmail = stripslashes($dfCounts['totaldfEmail']);
$defaultSms = stripslashes($dfCounts['totaldfSms']);

$EmailDtls = $objAddonMaster->GetAddonpurchaseUsedDtls($businessId, 1);
$dfEmailUsed = $EmailDtls['dfUsed'];
$EmailpurUsed = $EmailDtls['purUsed'];

$SMSDtls = $objAddonMaster->GetAddonpurchaseUsedDtls($businessId, 2);
$dfsmsU = $SMSDtls['dfUsed'];
$smsPurUsed = $SMSDtls['purUsed'];

$TotalemailCount = $defaultEmail + $EmailpurchasedCount;
$emailUsedCount = $dfEmailUsed + $EmailpurUsed;

$TotalsmsCount = $defaultSms + $smsPurCount;
$smsUsedCount = $dfsmsU + $smsPurUsed;

//Get MTD Total
$MTDdtls = $objAddonMaster->GetMTDAddonDtls($businessId);
$Email_MTD_Total = $MTDdtls['Email_MTD_Total'];
$SMS_MTD_Total = $MTDdtls['SMS_MTD_Total'];

//Get YTD Total
$MTDdtls = $objAddonMaster->GetYTDAddonDtls($businessId);
$Email_YTD_Total = $MTDdtls['Email_YTD_Total'];
$SMS_YTD_Total = $MTDdtls['SMS_YTD_Total'];
// ------------------------------------------------------------------------------------------------

// // Email Count Date(08/02/2023)
// $Addondata=$objAddonMaster->GetAddonpurchaseDtls($businessId,1);
// $TotalemailCount = $Addondata['defaultCount'] + $Addondata['purchasedCount'];
// $emailUsedCount = $Addondata['dfUsed'] + $Addondata['purUsed'];

// // SMS Count Date(08/02/2023)
// $Addondata=$objAddonMaster->GetAddonpurchaseDtls($businessId,2);
// $TotalsmsCount = $Addondata['defaultCount'] + $Addondata['purchasedCount'];
// $smsUsedCount = $Addondata['dfUsed'] + $Addondata['purUsed'];

//Send SMS Count
// $objNotification = new clsNotification();
// $smsCountOfThisYear = $objNotification->GetCountForSMSOrEmail($businessId,1,1,0);
// $smsCountOfThisMonth = $objNotification->GetCountForSMSOrEmail($businessId,1,0,1);

//Send Email Count
// $emailCountOfThisYear = $objNotification->GetCountForSMSOrEmail($businessId,0,1,0);
// $emailCountOfThisMonth = $objNotification->GetCountForSMSOrEmail($businessId,0,0,1);

//highest Order Value
$highestOrder = $objBusinessContactOpportunity->GetHighestAmountOfClosedWonByWeek($businessId);
$highestOrderValue = $highestOrder['highestOrderValue'];
$firstName = $highestOrder['firstName'];
$lastName = $highestOrder['lastName'];
$fullName = $firstName . ' ' . $lastName;

//Get Top Products
$topProducts = $objBusinessContactOpportunity->GetAllTopProducts($businessId, 3);
$topProductSales = $objBusinessContactOpportunity->GetAllTopProducts($businessId);
$topProductsCount = mysqli_num_rows($topProducts);
//Avg Deal Size
$totalAmountOfClosedWon = $objBusinessContactOpportunity->GetAllOpportunitiesCountByClosedWon($businessId);
$avgDealSizePerRep =  $totalAmountOfClosedWon ? $totalAmountOfClosedWon / $closedWonOpportunities : 0.00;
unset($objNotification);

$objsurvey = new clsSurvey();
$TotalSurveyCount = $objsurvey->GetTotalSurveysCount($businessId);

// $surveyTypeData = $objsurvey->GetAllSurveyType($businessId);

$surveysData = $objsurvey->GetSuveyCounts($businessId, $surveyId = 0);


// Total Visitors Count
$objVisitorCount = new clsVisitorCount();
$visitDetails = $objVisitorCount->GetTotalVisitsByBusinessId($businessId);
$TotalVisits = isset($visitDetails['TotalVisits']) ? $visitDetails['TotalVisits'] : 0;



?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- include header css-->
    <?php include('includes/headerCss.php'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/fonts/simple-line-icons/style.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/core/colors/palette-gradient.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/charts/jquery-jvectormap-2.0.3.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/charts/morris.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/fonts/simple-line-icons/style.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/core/colors/palette-gradient.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/selects/select2.min.css">
    <title>Dashboard</title>
    <style>
        .smsRotateArrow {
            transform: rotate(180deg);
        }

        .addonHeaderBottomBorder {
            border-bottom-right-radius: 0px !important;
            border-bottom-left-radius: 0px !important;
        }

        .accordion .card-header {

            border-radius: 8px !important;
        }

        .surveyHeader {

            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: .8rem 1.5rem;
            border-radius: 8px !important;
            box-shadow: 0 2px 18px 1px rgba(49, 53, 72, 0.1);

        }

        .borderRadius {
            border-radius: 0px !important;
            box-shadow: 0 2px 18px 1px rgba(49, 53, 72, 0.1);
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            margin-bottom: 5px;
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            padding-bottom: 0 !important;
        }

        .modal-body {
            font-family: "Quicksand", Georgia, "Times New Roman", Times, serif;
        }

        button:focus {
            outline: none;
        }
        .canva{    position: relative;
    right: 120px;
    padding: 10px;
    display: block;
    /* margin-top: 124px; */
    width: 277px;
    height: 138px;}
    .survey-drop{
       position: absolute; top: 10px; right:10px; min-width: 275px;max-width: 275px;
    }
    /* .info{
        color: #464855 !important;
    }
    .warning{
        color: #464855 !important;
    } */
    
    </style>
    <style>
                    @media (max-width: 1025px) {
                        .responsive > [class^='col-'] {
                            flex: 0 0 100%;
                            max-width: 100%;
                        }
                    }
                    @media (max-width: 767.98px) {
                        /* .responsive > [class^='col-'] {
                            margin-bottom: 1rem;
                        } */
                    }
                    /* @media (max-width: 1024px) {
                        .canva{position: relative; right: 120px; margin-top: 80px !important;}
                    } */

                    @media (max-width: 991px) {
                        .canva{           position: relative;
        right: 0px;
        padding: 10px;
        display: block;
        margin-top: 124px;
        width: 277px !important;
        height: 138px !important;}
                    } 
                      @media screen and (max-width: 1025px) {
                        .canva{           position: relative;
        right: 0px;
        padding: 10px;
        display: block;
        margin-top: 124px;
        width: 377px !important;
        height: 200px !important;}
                 .survey-drop{
       position: absolute; top: 10px; right:160px !important; min-width:auto !important; max-width: fit-content !important;
    }
                    } 

                    @media screen and (max-width: 400px) {
                         .canva{           position: relative;
        right: 70px;
        padding: 10px;
        display: block;
        margin-top: 124px;
        width: 377px !important;
        height: 200px !important;}
                    } 
                    .card-body {
    flex: 1 1 auto;
    padding: 1.0rem;
}
                     .survey-drop{
       position: absolute; top: 10px; right:20px !important; min-width:auto !important; max-width: fit-content !important;
    }

              @media (min-width: 768px) and (max-width: 991px) {
        .row >.tab-res {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }         
                </style>
  
</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">
    <input type="hidden" name="" id="openOpportunitiesPer" value="<?php echo $openOpportunitiesPer; ?> ">
    <input type="hidden" name="" id="closedWonAndLostAndInactiveOpportunitiesPer" value="<?php echo $closedWonAndLostAndInactiveOpportunitiesPer; ?> ">
    <input type="hidden" name="" id="closedWonAndLostOpportunitiesPer" value="<?php echo $closedWonAndLostOpportunitiesPer; ?> ">
    <input type="hidden" name="" id="currentWeekAmounts" value="<?php echo implode(",", $currentWeekAmounts); ?> ">
    <input type="hidden" name="" id="previousWeekAmounts" value="<?php echo implode(",", $previousWeekAmounts); ?> ">
    <input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">

    <!-- include header menus-->
    <?php include('includes/header.php'); ?>
    <input type="hidden" name="planId" id="planId" value="<?php echo ($planId); ?>">


    <!-- BEGIN: Content-->
    <div class="app-content content">
        <div class="content-header row">
        </div>
        <div class="content-overlay"></div>
        <div class="content-wrapper">
            <div class="content-body">
                <!-- eCommerce statistic -->
                <div class="row">
                    <?php if ($_SESSION["isPrimary"] == 1) { ?>
                        <div class="col-xl-4 col-lg-6 col-12 tab-res">
                            <?php if ($employeeCount > 0) { ?>
                                <a href="employee.html"><?php } ?>
                                <div class="card pull-up">
                                    <div class="card-content">
                                        <div class="card-body">
                                            <div class="media d-flex">
                                                <div class="media-body text-left">
                                                    <h3 class="info text-center"><?php echo ($employeeCount); ?></h3>
                                                    <h4 class="mt-2 text-center">Employees</h4>
                                                </div>
                                                <div>
                                                    <i class="fas fa-users info font-large-2 float-right "></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php if ($employeeCount > 0) { ?>
                                </a><?php } ?>
                        </div>

                        <div class="col-xl-4 col-lg-6 col-12  tab-res ">
                            <?php if ($affiliateCount > 0) { ?>
                                <a href="viewbusinessaffiliate.html"><?php } ?>
                                <div class="card pull-up">
                                    <div class="card-content">
                                        <div class="card-body">
                                            <div class="media d-flex">
                                                <div class="media-body text-left">
                                                    <h3 class="warning text-center"><?php echo ($affiliateCount); ?></h3>
                                                    <h4 class="mt-2 text-center">Affiliate</h4>
                                                </div>
                                                <div>
                                                    <i class="fas fa-handshake warning font-large-2 float-right"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php if ($affiliateCount > 0) { ?>
                                </a><?php } ?>
                        </div>
                    <?php } ?>
                    <div class="col-xl-4 col-lg-6 col-12 tab-res">
                        <?php if ($serviceCount > 0) { ?>
                            <a href="services.html"><?php } ?>
                            <div class="card pull-up">
                                <div class="card-content">
                                    <div class="card-body">
                                        <div class="media d-flex">
                                            <div class="media-body text-left">
                                                <h3 class="danger text-center"><?php echo ($serviceCount); ?></h3>
                                                <h4 class="mt-2 text-center">Incentives</h4>
                                            </div>
                                            <div>
                                                <i class="fas fa-gift danger font-large-2 float-right"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php if ($serviceCount > 0) { ?>
                            </a><?php } ?>
                    </div>
                    <div class="col-xl-4 col-lg-6 col-12 tab-res">
                        <a href="totalVisitsAnalytics.html?id=<?php echo EncodeQueryData(2); ?>">

                            <div class="card pull-up">
                                <div class="card-content">
                                    <div class="card-body">
                                        <div class="media d-flex">
                                            <div class="media-body text-left">
                                                <h3 class="danger text-center"><?php echo $TotalVisits; ?></h3>
                                                <h4 class="mt-2 text-center">Total Visits</h4>
                                            </div>
                                            <div>
                                                <i class="fas fa-chart-line danger font-large-2 float-right"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-4 col-lg-6 col-12 tab-res">
                        <div class="card pull-up">
                            <div class="card-content">
                                <div class="card-body">
                                    <div class="media d-flex">
                                        <div class="media-body text-left">
                                            <h3 class=" text-center">0</h3>
                                            <h4 class="mt-2 text-center">New Comments</h4>
                                        </div>
                                        <div>
                                            <i class="fas fa-comments danger font-large-2 float-right" style="color: #464855 !important;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-lg-6 col-12 tab-res">
                        <?php if ($_SESSION["isPrimary"] == 1) { ?>
                            <?php if ($contactCount > 0) { ?>
                                <a href="viewbusinesscustomers.html?id=<?php echo EncodeQueryData(2); ?>"><?php } ?>
                                <div class="card pull-up">
                                    <div class="card-content">
                                        <div class="card-body">
                                            <div class="media d-flex">
                                                <div class="media-body text-left">
                                                    <h3 class="info text-center"><?php echo ($contactCount); ?></h3>
                                                    <h4 class="mt-2 text-center">New Contacts</h4>
                                                </div>
                                                <div>
                                                    <i class="fas fa-address-book info font-large-2 float-right"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php if ($contactCount > 0) { ?>
                                </a><?php } ?>
                        <?php } ?>
                    </div>

                </div>
                <!--/ eCommerce statistic -->
                <hr style="background-color:red;" class="m-0 mb-2">
                <?php if ($surveychk == 1) { ?>
                    <!-- new -->
                    <div class="accordion" style="margin-top: 10px; margin-bottom: 18px; border-radius: 10px">
                        <div class="subs-card" style="padding-bottom: 0px;">
                            <div class="sms-collapse-div sms-link" type="button"
                                data-toggle="collapse"
                                data-target="#collapseOne"
                                aria-expanded="true"
                                aria-controls="collapseOne"
                                id="accordionExample1">
                                <div class="card-header card-heading surveyHeader borderRadius" id="headingOne" style="">
                                    <h2 class="mb-0 btn btn-link" style="color: #000000; font-size: 1.32rem;">Survey Analytics
                                    </h2>
                                    <i class="fa-solid fa-angle-down smsAddonDownArrow" style="font-size: 20px;"></i>

                                </div>
                            </div>
                            <div id="collapseOne" class="collapse " aria-labelledby="headingOne" data-parent="#accordionExample1">
                                <div class="card-body py-0 pb-1 " style="background-color: #ffffff; border-top: 1px solid #80808033;">
                                    <div class="row">
                                        <div class="col-md-6" style="position: relative;">
                                            <div id="chartContainer">
                                                <!-- <canvas id="myChart" style="position: relative; right: 120px; padding: 10px;"></canvas> -->
                                            </div>

                                            <div class="survey-drop">
                                                <label class=" label-control" for="surveyId">Select Survey</label>
                                                <select id="surveyId" name="surveyId" class="select2 form-control" data-parsley-errors-container="#error-surveyType">
                                                    <!-- <option value="">All</option> -->
                                                    <?php
                                                    if ($surveysData != "") {
                                                        while ($row = mysqli_fetch_assoc($surveysData)) {
                                                            $surveyId = stripslashes($row['surveyId']);
                                                            $surveyTitle = stripslashes($row['surveyTitle']);
                                                    ?>
                                                            <option value="<?php echo ($surveyId); ?>" selected><?php echo ($surveyTitle); ?></option>
                                                    <?php
                                                        }
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div style=" border-bottom: 4px solid #126F9B ; min-height: 100px; margin: 20px; padding: 20px; border-radius: 10px; background: #FFF; box-shadow: 0 2px 18px 1px rgba(49, 53, 72, 0.1); ">
                                                <h2 style="text-align: center; margin-bottom: 20px;">Total Assigned Users</h2>
                                                <a href="surveyReport.html">
                                                    <h2 style="text-align: center;" id="TotalSurveyCount"></h2>
                                                </a>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div style=" min-height: 100px; margin: 20px; margin-right: 10px; padding-bottom: 20px; padding-top: 0; border-radius: 10px; background: #FFF; box-shadow: 0 2px 18px 1px rgba(49, 53, 72, 0.1); overflow: hidden;">
                                                        <h3 style="text-align: center; margin-bottom: 20px; background-color: #40BE20;padding:9px; color: white;">Completed</h3>
                                                        <h3 style="text-align: center;" id="TotalCompletCount"></h3>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div style=" min-height: 100px; margin: 20px; margin-left: 10px; padding-bottom: 20px; border-radius: 10px; background: #FFF; box-shadow: 0 2px 18px 1px rgba(49, 53, 72, 0.1); overflow: hidden;">
                                                        <h3 style="text-align: center; margin-bottom: 20px; background-color: #ff2a46;padding:9px; color: white;">Pending</h3>
                                                        <h3 style="text-align: center; " id="TotalPendingCount"></h3>
                                                    </div>
                                                </div>
                                            </div>
                                            <br>

                                        </div>
                                        <div id="Note">
                                            <h5 style="margin-left: 30px;color: red;">NOTE : <span>Showing survey analytics for the latest survey. Select a survey from the filter to explore survey-specific details.</span></h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php }  ?>
                <!-- Revenue, Hit Rate & Deals -->
                <div class="row">
                    <div class="col-xl-6 col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Revenue</h4>
                                <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                <div class="heading-elements">
                                    <ul class="list-inline mb-0">
                                        <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="card-content collapse show">
                                <div class="card-body pt-0">
                                    <div class="row mb-1">
                                        <div class="col-6 col-md-4">
                                            <h5>Current week</h5>
                                            <h2 class="danger"><?php if ($totalAmountOfCurrentWeekRevenue > 0) {
                                                                    echo '$' . $totalAmountOfCurrentWeekRevenue;
                                                                } else {
                                                                    echo '$0.00';
                                                                } ?></h2>
                                        </div>
                                        <div class="col-6 col-md-4">
                                            <h5>Previous week</h5>
                                            <h2 class="text-muted"> <?php if ($totalAmountOfPreviousWeekRevenue > 0) {
                                                                        echo '$' . $totalAmountOfPreviousWeekRevenue;
                                                                    } else {
                                                                        echo '$0.00';
                                                                    } ?></h2>
                                        </div>
                                    </div>
                                    <div class="chartjs">
                                        <canvas id="thisYearRevenue" width="400" class="position-absolute"></canvas>
                                        <canvas id="lastYearRevenue" width="400"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6 col-12">
                        <div class="row">
                            <div class="col-md-6 col-12">
                                <div class="card pull-up">
                                    <div class="card-header bg-hexagons">
                                        <h4 class="card-title">Hit Rate <span class="danger"> -<?php echo $closedWonAndLostOpportunitiesPer; ?>%</span></h4>
                                        <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                        <div class="heading-elements">
                                            <ul class="list-inline mb-0">
                                                <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="card-content collapse show bg-hexagons">
                                        <div class="card-body pt-0">
                                            <div class="chartjs">
                                                <canvas id="hit-rate-doughnut" height="275"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="card pull-up">
                                    <div class="card-content collapse show bg-gradient-directional-danger ">
                                        <div class="card-body bg-hexagons-danger">
                                            <h4 class="card-title white">Deals <span class="white">-<?php echo $openOpportunitiesPer; ?>%</span> <span class="float-right"><span class="white"><?php echo $openOpportunities; ?></span><span class="red lighten-4">/<?php echo $totalOpportunities; ?></span></span>
                                            </h4>
                                            <div class="chartjs">
                                                <canvas id="deals-doughnut" height="275"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-12 tab-res">
                                <div class="card pull-up">
                                    <div class="card-content">
                                        <div class="card-body">
                                            <div class="media d-flex">
                                                <div class="media-body text-left">
                                                    <h6 class="text-muted">Order Value </h6>
                                                    <h3> <?php if ($highestOrderValue) {
                                                                echo '$' . $highestOrderValue;
                                                            } else {
                                                                echo '$0.00';
                                                            }  ?></h3>
                                                    <h5><?php echo $fullName; ?></h5>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="icon-trophy success font-large-2 float-right"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 tab-res">
                                <div class="card pull-up">
                                    <div class="card-content">
                                        <div class="card-body">
                                            <div class="media d-flex">
                                                <div class="media-body text-left">


                                                    <h6 class="text-muted">SMS - <span name="smsdfUsed" class="" data-toggle="tooltip" data-placement="top" title="Total Used SMS Count" style="color:black;cursor:pointer;" id="smsdfUsed"><?php echo $smsUsedCount; ?></span>/<?php echo $TotalsmsCount; ?></h6>

                                                    <h5>MTD/YTD <span class="ml-5"><?php echo $SMS_MTD_Total; ?>/<?php echo $SMS_YTD_Total; ?></span> </h5>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="icon-bubble warning font-large-2 float-right"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--/ Revenue, Hit Rate & Deals -->

                <!-- Emails Products & Avg Deals -->
                <div class="row responsive">
                    <div class="col-12 col-md-3 mb-2 tab-res">
                        <div class="card pull-up ">
                            <div class="card-content">
                                <div class="card-body">
                                    <div class="media d-flex">
                                        <div class="media-body text-left">
                                            <h6 class="text-muted">Email - <span name="smsdfUsed" class="" data-toggle="tooltip" data-placement="top" title="Total Used Email Count" style="color:black;cursor:pointer;" id="smsdfUsed"><?php echo $emailUsedCount; ?></span>/<?php echo $TotalemailCount; ?></h6>
                                            <h5>MTD/YTD <span class="ml-5"><?php echo $Email_MTD_Total; ?>/<?php echo $Email_YTD_Total; ?> </span> </h5>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="icon-envelope primary font-large-2 float-right"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-md-3 mb-2 tab-res">
                        <div class="card ">
                            <div class="card-header">
                                <h4 class="card-title">Top Products/Opportunities</h4>
                                  <!-- <div class="heading-elements">
                                    <ul class="list-inline mb-0">
                                        <li><a href="#">Show all</a></li>
                                    </ul>
                                </div> -->
                            </div>
                            <div class="card-content collapse show">
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table mb-0">
                                            <tbody>
                                                <?php if ($topProductsCount > 0) {
                                                    while ($row = mysqli_fetch_assoc($topProducts)) {
                                                        $total = $row['total'];
                                                        $productTitle = $row['productTitle'];
                                                ?>
                                                        <tr>
                                                            <th scope="row" class="border-top-0"><?php echo $productTitle; ?></th>
                                                            <td class="border-top-0 text-right"><?php echo $total; ?></td>
                                                        </tr>
                                                    <?php
                                                    }
                                                } else { ?>
                                                    <tr>
                                                        <th scope="row" class="border-top-0 text-center">No data found.</th>
                                                    </tr>
                                                <?php }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 col-md-6 mb-2">
                        <div class="card ">
                            <div class="card-header">
                                <h4 class="card-title text-center">Average Deal Size</h4>
                            </div>
                            <div class="card-content collapse show">
                                <div class="card-body pt-0">
                                    <div class="row">
                                        <div class="col-md-6 col-12 border-right-blue-grey border-right-lighten-5 text-center">
                                            <h4 class="font-large-2 text-bold-400"><?php echo '$' . round($avgDealSizePerRep, 2); ?></h4>
                                            <p class="blue-grey lighten-2 mb-0">Per rep</p>
                                        </div>
                                        <div class="col-md-6 col-12 text-center">
                                            <!-- Reserved for future content -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!--/ Emails Products & Avg Deals -->

                <!-- Total earning & Recent Sales  -->
                <div class="row responsive">

                    <div class="col-12 col-md-4">
                        <div class="card">
                            <div class="card-content">
                                <div class="earning-chart position-relative">
                                    <div class="chart-title position-absolute mt-2 ml-2">
                                        <h1 class="display-4"><?php if ($totalEarning) {
                                                                    echo '$' . $totalEarning;
                                                                } else {
                                                                    echo '$0.00';
                                                                }  ?></h1>
                                        <span class="text-muted">Total Earning</span>
                                    </div>
                                    <canvas id="earning-chart" class="height-450"></canvas>
                                    <div class="chart-stats position-absolute position-bottom-0 position-right-0 mb-2 mr-3">
                                        <a href="#" class="btn round btn-danger mr-1 btn-glow">Statistics <i class="ft-bar-chart"></i></a> <span class="text-muted">for the <a href="#" class="danger darken-2">last year.</a></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="recent-sales" class="col-12 col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Recent Sales</h4>
                                <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                <div class="heading-elements">
                                    <ul class="list-inline mb-0">
                                        <!-- <li><a class="btn btn-sm btn-danger box-shadow-2 round btn-min-width pull-right" href="invoice-summary.html" target="_blank">View all</a></li> -->
                                    </ul>
                                </div>
                            </div>
                            <div class="card-content mt-1">
                                <div class="table-responsive">
                                    <table id="recent-orders" class="table table-hover table-xl mb-0">
                                        <thead>
                                            <tr>
                                                <th class="border-top-0">Product</th>
                                                <th class="border-top-0">Customers</th>
                                                <th class="border-top-0">Categories</th>
                                                <th class="border-top-0">Popularity</th>
                                                <th class="border-top-0">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            if ($topProductsCount > 0) {
                                                while ($row = mysqli_fetch_assoc($topProductSales)) {
                                                    $total = $row['total'];
                                                    $productTitle = $row['productTitle'];
                                                    $categoryTitle = $row['categoryTitle'];
                                                    $categoryTitle = $row['categoryTitle'];
                                                    $productId = $row['productId'];

                                                    $productCustomers = $objBusinessContactOpportunity->GetAllTopProductsCustomers($businessId, $productId);
                                                    $productCustomerCount = mysqli_num_rows($productCustomers);

                                            ?>
                                                    <tr>
                                                        <td class="text-truncate"><?php echo $productTitle; ?></td>
                                                        <td class="text-truncate p-1">
                                                            <?php
                                                            if ($productCustomerCount > 0) {
                                                            ?>
                                                                <ul class="list-unstyled users-list m-0">
                                                                    <?php

                                                                    while ($row = mysqli_fetch_assoc($productCustomers)) {
                                                                        $firstName = $row['firstName'];
                                                                        $customerId = $row['customerId'];
                                                                        $lastName = $row['lastName'];
                                                                        $fullName = $firstName . ' ' . $lastName;
                                                                        $customerImage  = GetCustomerImagePath($customerId, $row['imageName']);
                                                                    ?>

                                                                        <li data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="<?php echo $fullName; ?>" class="avatar avatar-sm pull-up">
                                                                            <img class="media-object rounded-circle" src="<?php echo $customerImage; ?>" alt="Avatar">
                                                                        </li>
                                                                    <?php
                                                                    } ?>
                                                                </ul>
                                                            <?php
                                                            } ?>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-danger round"><?php echo $categoryTitle; ?></button>
                                                        </td>
                                                        <td>
                                                            <div class="progress progress-sm mt-1 mb-0 box-shadow-2">
                                                                <div class="progress-bar bg-gradient-x-danger" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                        </td>
                                                        <td class="text-truncate"><?php if ($total > 0) {
                                                                                        echo '$' . $total;
                                                                                    } else {
                                                                                        echo '$0.00';
                                                                                    }  ?></td>
                                                    </tr>

                                            <?php
                                                }
                                            }
                                            ?>


                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="message" style="min-height: 80px"></div> -->
                <!-- <div>Device Token: <span id="deviceToken"></span></div> -->

                <!--/ Total earning & Recent Sales  -->

                <!-- Analytics map based session -->
                <!-- <div class="row">
                    <div class="col-12">
                        <div class="card box-shadow-0">
                            <div class="card-content">
                                <div class="row">
                                    <div class="col-md-9 col-12">
                                        <div id="world-map-markers" class="height-450"></div>
                                    </div>
                                    <div class="col-md-3 col-12">
                                        <div class="card-body text-center">
                                            <h4 class="card-title mb-0">Visitors Sessions</h4>
                                            <div class="row">
                                                <div class="col-12">
                                                    <p class="pb-1">Sessions by Browser</p>
                                                    <div id="sessions-browser-donut-chart" class="height-200"></div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="sales pr-2 pt-2">
                                                        <div class="sales-today mb-2">
                                                            <p class="m-0">Today's <span class="success float-right"><i class="ft-arrow-up success"></i>
                                                                    6.89%</span></p>
                                                            <div class="progress progress-sm mt-1 mb-0">
                                                                <div class="progress-bar bg-success" role="progressbar" style="width: 70%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                        </div>
                                                        <div class="sales-yesterday">
                                                            <p class="m-0">Yesterday's <span class="danger float-right"><i class="ft-arrow-down danger"></i>
                                                                    4.18%</span></p>
                                                            <div class="progress progress-sm mt-1 mb-0">
                                                                <div class="progress-bar bg-danger" role="progressbar" style="width: 65%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <!-- Analytics map based session -->
            </div>
        </div>

        <!-- Button trigger modal -->
        <!-- <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter">
        Launch demo modal
        </button> -->

        <!-- Modal -->
        <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="border-radius: 0;">
                    <!-- <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">Upgrade for More!</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
                </button>
            </div> -->
                    <div class="modal-body">
                        <button style="position: absolute;right: 10px;top: 3px;font-size: 27px;" type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <div style="display: flex;justify-content: center;margin:10px 0 30px">
                            <img style="width: 200px;" src="../assets/images/offer-modal/img-3.svg" alt="" srcset="">
                        </div>
                        <div style="font-size: 17px;text-align: center;line-height: 1.3;margin: 0 20px;font-weight: 500;">
                            You are running on a Freemium plan. Subscribe to our premium plans and unlock a range of advanced features and exclusive benefits.
                        </div>
                        <div style="display: flex;justify-content: center;margin: 25px 20px 10px;">
                            <a style="width: 100%;display: flex;justify-content: center;" href="accountsetting.html#account-vertical-connections/"><button style="width: 200px;border-radius: 0;font-size: 14px;font-weight: 600;" type="button" class="btn btn-primary">Upgrade Now</button></a>
                        </div>
                    </div>
                    <!-- <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a href="accountsetting.html#account-vertical-connections/"><button type="button" class="btn btn-primary">Upgrade Now</button></a>
            </div> -->
                </div>
            </div>
        </div>

    </div>
    <?php unset($objBusinessContactOpportunity); ?>
    <!-- END: Content-->

    <!-- BEGIN: Footer-->
    <?php include('includes/footer.php'); ?>
    <?php include('includes/footerJs.php'); ?>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/chart.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/raphael-min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/morris.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/jvector/jquery-jvectormap-2.0.3.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/jvector/jquery-jvectormap-world-mill.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/data/jvector/visitor-data.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/pages/dashboard-sales.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/select/form-select2.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/multiselect-dropdown.js"></script>

    <!-- <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> -->

    <!-- END: Footer-->
    <script>
        $(document).ready(function() {
            // GetSurveyCounts();
            var planId = $('#planId').val();
            if (planId == 1) {
                $('#exampleModalCenter').modal('show', {
                    backdrop: 'static',
                    keyboard: false
                });
            }

            $('.sms-link').click(function() {
                setCSStemporary();
                $('.smsAddonDownArrow').toggleClass('smsRotateArrow');
            })
        });

        function setCSStemporary() {
            var hasShow = $("#collapseOne").hasClass('show');
            console.log('hey' + hasShow);
            if (hasShow == true) {
                $('#headingOne').addClass('addonHeaderBottomBorder');
                $('#headingOne').removeClass('borderRadius');
            } else {
                $('#headingOne').removeClass('addonHeaderBottomBorder');
                $('#headingOne').addClass('borderRadius');
            }
        }

        $('#surveyId').change(function() {
            var surveyId = $('#surveyId').val();
            GetSurveyCounts(surveyId);
        });

        $('#headingOne').click(function() {
            GetSurveyCounts();
        });

        function GetSurveyCounts(surveyId = 0) {

            // After retrieving the data, call the function to update the chart
            var businessId = $('#businessId').val();

            if (surveyId > 0) {
                $('#Note').addClass('hide');
            }

            $.ajax({
                type: "GET",
                url: "../ajax/ajax_get_surveyCounts.html",
                data: {
                    businessId: businessId,
                    surveyId: surveyId
                },
                dataType: "json",
                success: function(data) {
                    // console.log("Data");

                    var surveyId = data[0]['surveyId'];
                    var TotalSurveyCount = data[0]['TotalSurveyCount'];
                    var TotalPendingCount = data[0]['TotalPendingCount'];
                    var TotalCompletCount = data[0]['TotalCompletCount'];
                    $('#TotalSurveyCount').text(TotalSurveyCount);
                    $('#TotalCompletCount').text(TotalCompletCount);
                    $('#TotalPendingCount').text(TotalPendingCount);
                    if (data[0]['TotalPendingCount'] != 0 || data[0]['TotalCompletCount'] != 0) {

                        SurveyGraph(TotalPendingCount, TotalCompletCount)
                    } else if (TotalSurveyCount == 0 && surveyId > 0) {


                        Swal.fire({
                            title: 'Confirmation',
                            text: "Survey is not assigned yet, would you like to assign ?",
                            type: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Yes',
                            confirmButtonClass: 'btn btn-outline-primary',
                            cancelButtonClass: 'btn btn-outline-danger ml-1',
                            buttonsStyling: false,
                        }).then(function(result) {
                            if (result.value) {
                                window.location.href = "<?php echo BASE_PATH; ?>" + "/business/assignsurvey.html?surveyId=" + btoa(surveyId);
                            }
                            // else
                            // {
                            //     location.reload();
                            // }

                        });
                    } else {
                        SurveyGraphEmpty(100, 0)
                    }

                }
            });

        }

        function SurveyGraph(TotalPendingCount, TotalCompletCount) {
            $('#myChart').remove();
            $('#chartContainer').append('<canvas id="myChart" class="canva"></canvas>');

            const ctx = document.getElementById('myChart');

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'Pending'],
                    color: "#fff",
                    datasets: [{
                        label: '# of Votes',
                        backgroundColor: ["#28D094", "#ff7b8c"],
                        data: [TotalCompletCount, TotalPendingCount],
                        borderWidth: 1,
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }



        function SurveyGraphEmpty(TotalPendingCount, TotalCompletCount) {
            $('#myChart').remove();
            $('#chartContainer').append('<canvas id="myChart" class="canva"></canvas>');

            const ctx = document.getElementById('myChart');

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Empty'],
                    color: "#fff",
                    datasets: [{
                        label: '# of Votes',
                        backgroundColor: ['#c1b6b7', "#c1b6b7"],
                        data: [TotalCompletCount, TotalPendingCount],
                        borderWidth: 1,
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false // Hide the chart legend (optional)
                        }
                    },
                    events: [] // Disable mouse events on the chart
                }
            });
        }

        // {
        //     $('#myChart').remove();
        //     $('#chartContainer').append('<canvas id="myChart" style="position: relative; right: 120px; padding: 10px;"></canvas>');

        //     const ctx = document.getElementById('myChart');

        //     new Chart(ctx, {
        //     type: 'doughnut',
        //     data: {
        //         labels: ['Completed', 'Pending'],
        //         color: "#fff",
        //         datasets: [{
        //         // label: '# of Votes',
        //         backgroundColor: ["#28D094", "#c1b6b7"],
        //         data: [0, 100],
        //         borderWidth: 1,
        //         }]
        //     },
        //     options: {
        //             scales: {
        //                 y: {
        //                     beginAtZero: true
        //                 }
        //             },
        //             plugins: {
        //                 legend: {
        //                     display: false // Hide the chart legend (optional)
        //                 },
        //                 tooltip: {
        //                     enabled: false // Disable the hover tooltip
        //                 }
        //             }
        //         }
        //     });
        // }

        $(window).on('resize', function() {
            // Redraw charts on window resize
            if (typeof revenueChart !== 'undefined') {
                revenueChart.resize();
            }

            // Adjust content height for responsive layout
            var setContentHeight = function() {
                // Reset height
                $('.right_col').css('min-height', $(window).height());

                var bodyHeight = $('body').outerHeight(),
                    footerHeight = $('body').hasClass('footer_fixed') ? -10 : $('footer').height(),
                    contentHeight = bodyHeight < leftColHeight ? leftColHeight : bodyHeight;

                // Normalize content
                contentHeight -= $('.nav_menu').height() + footerHeight;

                $('.right_col').css('min-height', contentHeight);
            };

            setContentHeight();
        });

        // Add this to your existing JavaScript for better tab handling
        function opentabs(evt, tabName) {
            // Declare variables
            var i, tabcontent, tablinks;

            // Get all elements with class="tabcontent" and hide them
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // Get all elements with class="tablinks" and remove the class "active"
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }

            // Show the current tab, and add an "active" class to the button that opened the tab
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";

            // Adjust height for mobile
            if (window.innerWidth < 768) {
                // Scroll to the tab content
                document.getElementById(tabName).scrollIntoView({
                    behavior: "smooth"
                });
            }
        }

        // Fix tab height issues on window resize
        $(window).on('resize', function() {
            $('.tab_content').css('height', 'auto');

            // Recalculate heights for organic tabs if they exist
            if (typeof $.organicTabs !== 'undefined') {
                var activeTab = $('.tab_content').find('.current').attr('href');
                if (activeTab) {
                    var newHeight = $(activeTab).height();
                    $('.tab_content').height(newHeight);
                }
            }
        });
    </script>


</body>
<!-- END: Body-->

</html>
