<?php
//include classes and config file here	
include('./includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsCustomer.php');
include('../class/clsBusiness.php');
include('../class/clsBusinessContactNotes.php');
include('../class/clsBusinessContactToDo.php');
include('../class/clsBusinessContactStages.php');
include('../class/clsBusinessContactProcess.php');
include('../class/clsBusinessContactCategory.php');
include('../class/clsBusinessSecondaryContacts.php');
include('../class/clsBusinessContactOpportunity.php');
include('../class/clsBusinessContactPersonalContacts.php');
include('../class/clsBusinessContactRelationship.php');
include('../class/clsBusinessProduct.php');
include('../class/clsToDo.php');
include('../class/clsBusinessContactActivity.php');
include('../class/clsBusinessModulePermissions.php');



//variables
$countries = '';
$dbCountryId = '224';
$todoId = 0;
$stateId = 0;
$customerId = 0;
$businessId = $_SESSION["loggedBusinessId"];
$employeeId = $_SESSION["loggedEmployeeId"];
$firstName = '';
$lastName = '';
$email = '';
$phoneNo = '';
$address = '';
$city = '';
$zipCode = '';
$businessRows = '';
$todoRow = '';
$imageName = '';
$allNotes = '';
$allToDoList = '';
$customerImagePath = '';
$pageTitle = 'Add';
$buttonTitle = 'Save';
$customerAnniversary  = date("m/d/Y h:i A");

//***** SECONDARY CONTACT VARIABLES ******//
$contactSecondaryId = 0;
$companyName = '';
$secondaryEmail = '';
$secondaryPhoneNo = '';
$secondaryRow = '';
$secondaries = '';
$secondaryTitle = '';
$comments = '';
$secondaryFirstName = '';
$secondaryLastName = '';
$secondaryAddress = '';
$secondaryAddress2 = '';
$secondaryCity = '';
$secondaryZipCode = '';
$secondaryStateId = 0;
$secondaryCountryId = 224;
$secondaryMobileNo = '';
$isSameAddress = 0;

//***** PERSONAL CONTACT VARIABLES ******//
$personalContactDetailId = 0;
$personalFirstName = '';
$personalLastName = '';
$personalEmail = '';
$personalPhoneNo = '';
$personalRow = '';
$personalcontacts = '';
$personalAddress = '';
$personalAddress2 = '';
$personalCity = '';
$personalZipCode = '';
$personalStateId = 0;
$personalCountryId = 224;
$dbcontactRelationId = 0;
$birthDate  = "";
$anniversaryDate  = "";

//***** OPPORTUNITY VARIABLES ******//
$oppourtunityId = 0;
$oppourtunityName = '';
$totals = 0;
$status = '';
$openDate  = date("m/d/Y h:i A");
$noofOpenDays  = 0;
$estCloseDate  = "";
$actCloseDate  = "";
$dbprocessId  = 0;
$processId  = 0;
$dbprocessId  = 0;
$stageId  = 0;
$dbstageId  = 0;
$dbcategoryId  = 0;
$amount  = 0;
$opportunities  = '';
$opportunitieRow  = '';
$dbProductId = 0;

//***** TODO VARIABLES ******//
$taskname = '';
$taskdate = '';
$tododescription = '';
$tododescription = '';
$todonotes = '';
$taskdate  = date("m/d/Y h:i A");

//***** NOTES VARIABLES ******//
$notesId = 0;
$notestitle = '';
$relations = '';
$notesRow = '';
$notesDescription = '';
$notesdate  = date("m/d/Y h:i A");


$employeeList = '';
$dbtaskActivityId = 0;
$viewSelectedAssignee = array();


//Get Location
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
unset($objCountryStateMaster);

//***** STAGES *****//
$objBusinessContactStages = new clsBusinessContactStages();
$stages = $objBusinessContactStages->GetAllStages($businessId);
unset($objBusinessContactStages);

//***** PROCESS *****//
$objBusinessContactProcess = new clsBusinessContactProcess();
$processes = $objBusinessContactProcess->GetAllProcess($businessId);
unset($objBusinessContactProcess);

//***** CATEGORIES *****//
$objBusinessContactCategory = new clsBusinessContactCategory();
$categories = $objBusinessContactCategory->GetAllCategory($businessId);
unset($objBusinessContactCategory);

//***** RELATIONSHIP *****//
$objBusinessContactRelationship = new clsBusinessContactRelationship();
$relations = $objBusinessContactRelationship->GetAllRelationships($businessId);
unset($objBusinessContactRelationship);


//***** ACTIVITIES *****//
$objBusinessContactActivity = new clsBusinessContactActivity();
$taskActivities = $objBusinessContactActivity->GetAllTaskActivity($businessId);
unset($objBusinessContactActivity);

//***** GET ALL BUSINESS EMPLOYEE LIST *****//
$objToDo = new clsToDo();
$employeeList = $objToDo->GetAllBusinessEmployeeLst($businessId);
unset($objToDo);

if (isset($_GET['id'])) {
	$customerId = DecodeQueryData($_GET['id']);


	//***** Product *****//
	$objBusinessProduct = new clsBusinessProduct();
	$products = $objBusinessProduct->GetAllProduct($businessId);
	unset($objBusinessProduct);

	//object
	$objCustomer = new clsCustomer();

	//***** CUSTOMER DETAILS *****//
	$row = $objCustomer->GetCustomerDetails($customerId, $businessId);
	unset($objCustomer);

	//***** PERSONAL CONTACTS *****//
	$objBusinessContactPersonalContacts = new clsBusinessContactPersonalContacts();
	$personalcontacts = $objBusinessContactPersonalContacts->GetAllPersonalContacts($businessId, $customerId);
	unset($objBusinessContactPersonalContacts);

	//***** CUSTOMER DETAILS ******//
	$firstName  = stripslashes($row['firstName']);
	$lastName  = stripslashes($row['lastName']);
	$email  = stripslashes($row['email']);
	$phoneNo  = stripslashes($row['phoneNo']);
	$address  = stripslashes($row['address']);
	$city  = stripslashes($row['city']);
	$zipCode  = stripslashes($row['zipCode']);
	$stateId  = stripslashes($row['stateId']);
	$imageName  = stripslashes($row['imageName']);
	$contactemployeeId  = stripslashes($row['employeeId']);
	if ($contactemployeeId) {
		array_push($viewSelectedAssignee, $contactemployeeId);
	}

	//***** GET CUSTOMER IMAGE PATH *****//
	$customerImagePath = GetCustomerImagePath($customerId, $imageName);

	//****** GET COUNTRY FROM STATE *****//
	$objCountryStateMaster = new clsCountryStateMaster();
	$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
	unset($objCountryStateMaster);

	//***** GET ALL TODO LIST *****// 

	// $objBusinessContactToDo = new clsBusinessContactToDo();
	// $allToDoList = $objBusinessContactToDo->GetAllToDos(0 , $customerId);
	// unset($objBusinessContactToDo);

	$objToDo = new clsToDo();
	$allToDoList = $objToDo->GetAllToDoListByCustomer($businessId, $customerId);
	unset($objToDo);


	//**** EDIT MODE PERSONAL CONTACTS *****//
	if (isset($_GET['personalContactDetailId'])) {
		$pageTitle = 'Edit';
		$buttonTitle = 'Update';
		$personalContactDetailId = DecodeQueryData($_GET['personalContactDetailId']);

		$objBusinessContactPersonalContacts = new clsBusinessContactPersonalContacts();
		$personalRow = $objBusinessContactPersonalContacts->GetPersonalContactDetails($personalContactDetailId);
		unset($objBusinessContactPersonalContacts);

		if ($personalRow != '') {
			$personalFirstName  = stripslashes($personalRow['firstName']);
			$personalLastName  = stripslashes($personalRow['lastName']);
			$personalEmail  = stripslashes($personalRow['email']);
			$personalPhoneNo  = stripslashes($personalRow['phoneNo']);
			$personalAddress  = stripslashes($personalRow['address']);
			$personalAddress2  = stripslashes($personalRow['address2']);
			$personalCity  = stripslashes($personalRow['city']);
			$personalZipCode  = stripslashes($personalRow['zipCode']);
			$personalStateId  = stripslashes($personalRow['stateId']);
			$dbcontactRelationId  = stripslashes($personalRow['relationId']);
			$birthDate = (date("m/d/Y h:i A", strtotime($personalRow['birthDate'])));
			$anniversaryDate = (date("m/d/Y h:i A", strtotime($personalRow['anniversaryDate'])));

			//get Country from State
			$objCountryStateMaster = new clsCountryStateMaster();
			$personalCountryId = $objCountryStateMaster->GetParentIdFromChildId($personalStateId);
			unset($objCountryStateMaster);
		}
	}

	//**** EDIT MODE TODO *****//
	if (isset($_GET['todoId'])) {
		$pageTitle = 'Edit';
		$buttonTitle = 'Update';
		$todoId = DecodeQueryData($_GET['todoId']);
		$objToDo = new clsToDo();
		$todoRow = $objToDo->GetToDoDetails($todoId);



		// $objBusinessContactToDo = new clsBusinessContactToDo();
		// $todoRow = $objBusinessContactToDo->GetToDoDetails($todoId);
		// unset($objBusinessContactToDo);
		if ($todoRow != '') {
			// $taskname = stripslashes($todoRow['activityName']);
			$dbtaskActivityId = stripslashes($todoRow['taskActivityId']);
			$taskdate = (date("m/d/Y h:i A", strtotime($todoRow['assignDate'])));
			$tododescription = stripslashes($todoRow['description']);
			$todonotes = stripslashes($todoRow['comments']);

			$selectedemployeeList = $objToDo->GetTodoAssigneeList($todoId);
			if ($selectedemployeeList != "") {
				while ($cos = mysqli_fetch_assoc($selectedemployeeList)) {
					$employeeId  = $cos['employeeId'];

					$assignDtls = $objToDo->GetToDoAssignDetails($todoId, $employeeId);
					$isCompleted = stripslashes($assignDtls['isCompleted']);
					array_push($viewSelectedAssignee, $employeeId);
				}
			}
		}
	}

	//***** GET ALL NOTES *****//
	$objBusinessContactNotes = new clsBusinessContactNotes();
	$allNotes = $objBusinessContactNotes->GetAllNotes(0, $customerId);
	unset($objBusinessContactNotes);

	//**** EDIT MODE TODO *****//
	if (isset($_GET['notesId'])) {
		$pageTitle = 'Edit';
		$buttonTitle = 'Update';
		$notesId = DecodeQueryData($_GET['notesId']);

		$objBusinessContactNotes = new clsBusinessContactNotes();
		$notesRow = $objBusinessContactNotes->GetNotesDetails($notesId);
		unset($objBusinessContactNotes);

		if ($notesRow != '') {
			$notestitle = stripslashes($notesRow['notestitle']);
			$notesDescription = stripslashes($notesRow['notesDescription']);
			$notesdate = (date("m/d/Y h:i A", strtotime($notesRow['notesdate'])));
		}
	}

	//***** GET ALL SECONDARY CONTACTS *****//
	$objBusinessSecondaryContacts = new clsBusinessSecondaryContacts();
	$secondaries = $objBusinessSecondaryContacts->GetAllSecondaryContacts($businessId, $customerId);
	unset($objBusinessSecondaryContacts);

	//**** EDIT MODE SECONDARY CONTACTS *****//
	if (isset($_GET['contactSecondaryId'])) {
		$pageTitle = 'Edit';
		$buttonTitle = 'Update';
		$contactSecondaryId = DecodeQueryData($_GET['contactSecondaryId']);

		$objBusinessSecondaryContacts = new clsBusinessSecondaryContacts();
		$secondaryRow = $objBusinessSecondaryContacts->GetSecondaryContactDetails($contactSecondaryId);
		unset($objBusinessSecondaryContacts);

		if ($secondaryRow != '') {
			$companyName = stripslashes($secondaryRow['companyName']);
			$secondaryEmail = stripslashes($secondaryRow['email']);
			$secondaryPhoneNo = stripslashes($secondaryRow['phoneNo']);
			$comments = stripslashes($secondaryRow['comments']);
			$secondaryTitle = stripslashes($secondaryRow['secondaryTitle']);
			$secondaryLastName = stripslashes($secondaryRow['secondaryLastName']);
			$secondaryFirstName = stripslashes($secondaryRow['secondaryFirstName']);
			$secondaryAddress = stripslashes($secondaryRow['address']);
			$secondaryAddress2 = stripslashes($secondaryRow['address2']);
			$secondaryCity = stripslashes($secondaryRow['city']);
			$secondaryZipCode = stripslashes($secondaryRow['zipCode']);
			$secondaryStateId = stripslashes($secondaryRow['stateId']);
			$secondaryMobileNo = stripslashes($secondaryRow['mobileNo']);
			$isSameAddress = stripslashes($secondaryRow['isSameAddress']);

			//****** GET COUNTRY FROM STATE *****//
			$objCountryStateMaster = new clsCountryStateMaster();
			$secondaryCountryId = $objCountryStateMaster->GetParentIdFromChildId($secondaryStateId);
			unset($objCountryStateMaster);
		}
	}

	//***** GET ALL OPPORTUNITIES *****// 
	$objBusinessContactOpportunity = new clsBusinessContactOpportunity();
	$opportunities = $objBusinessContactOpportunity->GetAllOpportunities($businessId, $customerId);
	unset($objBusinessContactOpportunity);

	//**** EDIT MODE OPPORTUNITIES *****//
	if (isset($_GET['oppourtunityId'])) {
		$pageTitle = 'Edit';
		$buttonTitle = 'Update';
		$oppourtunityId = DecodeQueryData($_GET['oppourtunityId']);

		$objBusinessContactOpportunity = new clsBusinessContactOpportunity();
		$opportunitieRow = $objBusinessContactOpportunity->GetToDoOpportunityDetails($oppourtunityId);
		unset($objBusinessContactOpportunity);

		if ($opportunitieRow != '') {
			$oppourtunityName = stripslashes($opportunitieRow['oppourtunityName']);
			$totals = stripslashes($opportunitieRow['totals']);
			$status = stripslashes($opportunitieRow['status']);
			$openDate = (date("m/d/Y h:i A", strtotime($opportunitieRow['openDate'])));

			$estCloseDate  = isset($opportunitieRow['estCloseDate']) ? $opportunitieRow['estCloseDate'] : '';
			$estCloseDate = ($estCloseDate != '0000-00-00 00:00:00') ? (date("m/d/Y h:i A", strtotime($estCloseDate))) : '';
			$actCloseDate  = isset($opportunitieRow['actCloseDate']) ? $opportunitieRow['actCloseDate'] : '';
			$actCloseDate = ($actCloseDate != '0000-00-00 00:00:00') ? (date("m/d/Y h:i A", strtotime($actCloseDate))) : '';
			$noofOpenDays = stripslashes($opportunitieRow['noofOpenDays']);
			$dbprocessId = stripslashes($opportunitieRow['processId']);
			$dbstageId = stripslashes($opportunitieRow['stageId']);
			$dbcategoryId = stripslashes($opportunitieRow['categoryId']);
			$dbProductId = stripslashes($opportunitieRow['productId']);

			$amount = stripslashes($opportunitieRow['amount']);
			$invoiceDetailId = stripslashes($opportunitieRow['invoiceDetailId']);
		}
	}
}
$objBusiness = new clsBusiness();
$businessOwnerData = $objBusiness->CheckIsBusinessOwner($businessId, $employeeId);
if ($businessOwnerData == '') {
	// employee Permissions Only
	$objBusinessModulePermissions = new clsBusinessModulePermissions();
	$permissionData = $objBusinessModulePermissions->getPermissionArray($businessId, $employeeId);
	// echo "<pre>";
	// print_r($permissionData);exit;
}



?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
	<title>View Contact Info</title>
	<?php include('includes/headerCss.php'); ?>
	<?php include('includes/datatableCss.php'); ?>
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/selects/select2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/animate/animate.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/extensions/sweetalert2.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/forms/checkboxes-radios.css">
	<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

	<style>
		.status-btn {
			height: 31px;
			/* margin-bottom: 5px; */
			font-size: 13px;
			font-weight: 600;
			padding: 5px 15px;
			border-radius: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto;
			width: 50%;
		}

		.status-complated {
			border: 1px solid rgb(52, 196, 52);
			background: rgb(211 236 211 / 26%);
			color: rgb(0 212 0);
			font-weight: 600;
		}

		.pending-status {
			border: 1px solid #f845456e;
			background: rgb(255 0 0 / 8%);
			color: #ff3939;
			font-weight: 600;
		}

		/* @media screen and (max-width: 800px) {
			.status-btn{
				width:auto;
			}
			.table.dataTable {
				display: table !important;
				width: 100% !important;
				table-layout: fixed !important;
				border-bottom: 1px solid lightgray !important;
			}
		}
		 */


.overAuto{
		overflow: hidden !important;
	}
		@media screen and (max-width:768px) {
			.mob-wrap{
		white-space: nowrap;
		/* display: flex; */
	}
	.overAuto{
		overflow: auto !important;
	}
		}
		
	</style>
</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

	<!-- include header-->
	<?php include('includes/header.php'); ?>

	<!-- BEGIN: Content-->
	<div class="app-content content">
		<div class="content-overlay"></div>
		<div class="content-wrapper">
			<div class="content-header row">
				<div class="content-header-left col-md-6 col-12 mb-2">
					<h3 class="content-header-title"></h3>
					<div class="row breadcrumbs-top">
						<div class="breadcrumb-wrapper col-12">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="dashboard.html">Dashboard</a>
								</li>
								<li class="breadcrumb-item active"><a href="javascript:void(0);">View Contact Info</a>
								</li>
							</ol>
						</div>
					</div>
				</div>
				<div class="content-header-right col-md-6 col-12">
					<div class="text-right">
						<a href="viewsettings.html?id=<?php echo EncodeQueryData($customerId); ?>">
							<button type="button" class="btn btn-outline-warning btn-min-width btn-glow mb-1">Settings</button>
						</a>
						<a href="<?php if ($_SESSION['isPrimary'] == 1) {
										echo "viewbusinesscustomers.html";
									} else {
										echo "viewemployeecontacts.html";
									} ?>">
							<button type="button" class="btn btn-outline-primary btn-min-width btn-glow mb-1">Back</button>
						</a>
					</div>
				</div>
			</div>
			<!--- show status messages --->
			<?php
			if (isset($_GET["status"])) {
				if ($_GET["status"] == "added") {
			?>
					<div class="alert alert-success alert-dismissible mt-2" role="alert">
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
						<strong>Added.</strong>
					</div>

				<?php
				} else if ($_GET["status"] == "updated") {
				?>
					<div class="alert alert-success alert-dismissible mt-2" role="alert">
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
						<strong>Updated.</strong>
					</div>
			<?php
				}
			}
			?>
			<div class="content-body">
				<!-- Default ordering table -->
				<section id="ordering">
					<div class="row">
						<div class="col-12">
							<div class="card">
								<div class="row col-sm-12 mt-2 ml-1 mb-2">
									<span>
										<h4>Contact Name: <b><?php echo ($firstName . ' ' . $lastName); ?></b></h4>
									</span>
								</div>
								<div class="card-header">
						           	<div class="nav-tabs-responsive-wrapper">

									<ul class="nav nav-tabs nav-top-border btn-glow no-hover-bg nav-justified flex-nowrap overAuto" id="myTab">

										<li class="nav-item">
											<a class="nav-link active" style="white-space: nowrap;" id="todo-tab" data-toggle="tab" href="#todo" aria-controls="todo" aria-expanded="true">To DO</a>
										</li>
										<li class="nav-item">
											<a class="nav-link" id="opportunities-tab" data-toggle="tab" href="#opportunities" aria-controls="opportunities" aria-expanded="true">Opportunities</a>
										</li>
										<li class="nav-item">
											<a class="nav-link" id="notes-tab" data-toggle="tab" href="#notes" aria-controls="notes" aria-expanded="false">Notes</a>
										</li>                                                                                                                                                                                                                                                                          
										<li class="nav-item">
											<a class="nav-link" style="white-space: nowrap;" id="secondary-tab" data-toggle="tab" href="#secondary" aria-controls="secondary" aria-expanded="false">Secondary Contacts</a>
										</li>
										<li class="nav-item">
											<a class="nav-link" style="white-space: nowrap;" id="personalinfo-tab" data-toggle="tab" href="#personalinfo" aria-controls="personalinfo" aria-expanded="false">Personal Contact</a>
										</li>

									</ul>
									</div>
								</div>
								<div class="col-sm-12 tab-content px-1 pt-1">
									<!--- TO-DO LIST ---->
									<div role="tabpanel" class="tab-pane active" id="todo" aria-labelledby="todo-tab" aria-expanded="true">
										<section id="horizontal-form-layouts">
											<div class="row">
												<div class="col-md-12">
													<div class="card-header d-flex justify-content-between">
														<h4 class="form-section mb-2"><i class="ft-user-check"></i>&nbsp; View Tasks</h4>
														<?php 
														$href =  BASE_PATH.'/business/addtask.html?customerId='.EncodeQueryData($customerId).'&isCRM='.EncodeQueryData(1);
														if($chatRoom == 0) {
															$href ='javascript:void(0);';
														} 
														?>

														<a href="<?php echo $href; ?>" class=" btn btn-outline-primary  btn-glow mr-1 mb-1" <?php if($chatRoom == 0) { ?> data-toggle="tooltip" data-popup="tooltip-custom"
															data-original-title="Item is not in your Subscription Plan. Please upgrade your subscription to continue" <?php } ?> >Add Task </a>
													</div>
													<!-- <form class="form form-horizontal" id="todoForm" method="post" action="addtodosubmit.html" enctype="multipart/form-data">																
														<input type="hidden" name="todoId" id="todoId" value="<?php echo ($todoId); ?>">
														<input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId); ?>">
														<input type="hidden" name="businessId" id="businessId" value="<?php echo ($_SESSION["loggedBusinessId"]); ?>">
														<div class="form-body">
															<h4 class="form-section"><i class="ft-mail"></i>New Task</h4>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="taskname">Task Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="taskname" class="form-control" name="taskname" value="<?php echo ($taskname); ?>" required>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="taskActivityId">Task Activity<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<select id="taskActivityId" name="taskActivityId" class="select2 form-control" required>	
																				<option value="0">Select Task Activity</option>																
																				<?php
																				if ($taskActivities != '') {
																					while ($row = mysqli_fetch_assoc($taskActivities)) {
																						$activityId = $row['activityId'];
																						$activityName = $row['activityName'];

																				?>
																						<option value="<?php echo ($activityId); ?>" <?php if ($dbtaskActivityId == $activityId) { ?> selected <?php } ?>><?php echo ($activityName); ?></option>
																				<?php
																					}
																				}
																				?>	
																			</select>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="assignEmployeeId">Assign To<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<?php if ($_SESSION["isPrimary"] == 0) { ?>
																				<input type="hidden" name="assignEmployeeId" value="<?php echo ($assignEmployeeId); ?>">
																			<?php } ?>
																			<select id="assignEmployeeId" name="assignEmployeeId[]" class="form-control" multiple multiselect-search="true" multiselect-select-all="true"  required <?php if ($_SESSION["isPrimary"] == 0) { ?> disabled <?php } ?>>																	
																				<?php
																				if ($employeeList != '') {
																					$empFullName = '';
																					while ($row = mysqli_fetch_assoc($employeeList)) {
																						$employeeId = $row['employeeId'];
																						$firstName = $row['firstName'];
																						$lastName = $row['lastName'];
																						$isPrimary = $row['isPrimary'];
																						if ($isPrimary == 1)
																							$empFullName = $firstName . ' ' . $lastName . '- Business Owner';
																						else
																							$empFullName = $firstName . ' ' . $lastName;

																				?>
																						<option class="form-control" value="<?php echo ($employeeId); ?>" <?php if (in_array($employeeId, $viewSelectedAssignee)) { ?> selected="true" <?php } ?>><?php echo ($empFullName); ?></option>
																				<?php
																					}
																				}
																				?>	
																			</select>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="tododescription">Description</label>
																		<div class="col-md-9 mx-auto">
																			<textarea class="form-control" id="tododescription" name="tododescription" rows="3"><?php echo ($tododescription); ?></textarea>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="taskdate">Task Date</label>
																		<div class="col-md-9 mx-auto">                                                                
																			<div class="input-group date-picker-class">
																				<input type="text" class="form-control dateValidation" id="taskdate" name="taskdate" value="<?php echo ($taskdate); ?>">
																				<div class="input-group-append">
																					<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="isCompleted">Task Status<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<select id="isCompleted" name="isCompleted" class="select2 form-control" required>																	
																				<option value="">Select</option>
																				<option value="0">Incomplete</option>
																				<option value="1">Completed</option>																					
																			</select>
																		</div>
																	</div>
																</div>	
																
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="todonotes">Notes</label>
																		<div class="col-md-9 mx-auto">
																			<textarea class="form-control" id="todonotes" name="todonotes" rows="2"><?php echo ($todonotes); ?></textarea>
																		</div>
																	</div>
																</div>
															</div>
															<div class="form-actions center">
																<a href="<?php if ($_SESSION['isPrimary'] == 1) {
																				echo "viewbusinesscustomers.html";
																			} else {
																				echo "viewemployeecontacts.html";
																			} ?>">
																	<button type="button" class="btn btn-outline-warning btn-glow mr-1 cancelClass">
																		<i class="ft-x"></i> Cancel
																	</button>
																</a>
																<button type="submit" id="btnSave1" class="btn btn-outline-primary btn-glow cancelClass">
																	<i class="la la-check-square-o"></i> <?php echo ($buttonTitle); ?>
																</button>
															</div>
														</div>
													</form>	 -->

													<div class="form-body">
														<!-- <h4 class="form-section mb-2"><i class="ft-user-check"></i>View Tasks</h4> -->
														<div class="table-responsive">
															<table class="table table-striped table-bordered default-ordering" width="100%" id="todoTable">
																<thead>
																	<tr>
																		<th>Task Activity</th>
																		<th>Description</th>
																		<th class="text-center">Task Date</th>
																		<th class="text-center">Status</th>
																		<!-- <th class="text-center">Action</th>  -->
																	</tr>
																</thead>
																<tbody>
																	<?php
																	if ($allToDoList != '') {
																		while ($row = mysqli_fetch_assoc($allToDoList)) {
																			$todoId = $row['todoId'];
																			$businessId = $row['businessId'];
																			$customerIds = $row['customerId'];
																			$taskname = ($row['activityName']);
																			$taskdate = (date("m/d/Y h:i A", strtotime($row['assignDate'])));
																			$tododescription = ($row['description']);
																			$todonotes = ($row['comments']);
																			$status = ($row['isCompleted']); //0-IMCOMPLETE , 1-COMPLETED

																			$objToDo = new clsToDo();
																			$assigneeDtls = $objToDo->GetTodoAssigneeDtls($todoId);
																			$assigneeCount = mysqli_num_rows($assigneeDtls);

																			$totalAssignees = 0;
																			$completedAssignees = 0;

																			while ($assigneeDtlsrow = mysqli_fetch_assoc($assigneeDtls)) {
																				$todoAssignId = $assigneeDtlsrow['todoAssignId'];
																				$employeeId = $assigneeDtlsrow['employeeId'];
																				$isCompleted = $assigneeDtlsrow['isCompleted'];
																				$noOfOpenDays = $assigneeDtlsrow['noOfOpenDays'];
																				$employeeName = $assigneeDtlsrow['employeeFname'] . ' ' . $assigneeDtlsrow['employeeLname'];
																				// $customerId = $assigneeDtlsrow['customerId'];
																				// $customerName = $assigneeDtlsrow['customerFname'] . ' ' . $assigneeDtlsrow['customerLname'];
																				$createdDate = (date("m/d/Y", strtotime($assigneeDtlsrow['createdDate'])));

																				// Increment total assignees
																				$totalAssignees++;

																				// Check if the current assignee is completed
																				if ($isCompleted == 1) {
																					$completedAssignees++;
																				}
																			}

																			$semployeeId = 0;
																			if ($_SESSION["isPrimary"] == 1) {
																				// Determine the status based on assignees' completion
																				if ($completedAssignees == $totalAssignees) {
																					$status = 'Completed';
																					$statusClass = 'status-complated';
																				} elseif ($completedAssignees == 0) {
																					$status = 'Pending';
																					$statusClass = 'pending-status';
																				} else {
																					$status = 'Pending';
																					$statusClass = 'pending-status';
																				}
																			} else {
																				$semployeeId = $row['employeeId'];
																				$isCompleted = $row['isCompleted'];

																				if ($semployeeId == $loggedEmployeeId && $isCompleted == 1) {
																					$status = 'Completed';
																					$statusClass = 'status-complated';
																				} else {
																					$status = 'Pending';
																					$statusClass = 'pending-status';
																				}
																			}

																			//status
																			if ($status == 0) {
																				$currntStatus = 'Incomplete';
																				$btnClass = "btn btn-sm btn-outline-secondary btn-glow";
																			} else {
																				$currntStatus = 'Complete';
																				$btnClass = "btn btn-sm btn-outline-success btn-glow";
																			}
																	?>
																			<tr>
																				<td><?php echo ($taskname); ?></td>
																				<td>
																					<div class="desc"><?php echo ($tododescription); ?></div>
																				</td>
																				<td class="text-center"><?php echo ($taskdate); ?></td>
																				<td class="text-center">
																					<div class="status-btn <?php echo $statusClass; ?>">
																						<?php echo $status; ?>
																					</div>

																				</td>
																			</tr>
																	<?php
																		}
																	}
																	?>
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>
										</section>
									</div>

									<!--- OPPORTUNITIES ---->
									<div role="tabpanel" class="tab-pane" id="opportunities" aria-labelledby="opportunities-tab" aria-expanded="true">
										<section id="horizontal-form-layouts">
											<div class="row">
												<div class="col-md-12">
													<form class="form form-horizontal" id="oppFprm" method="post" action="addoppourtunitysubmit.html" enctype="multipart/form-data">
														<input type="hidden" name="oppourtunityId" id="oppourtunityId" value="<?php echo ($oppourtunityId); ?>">
														<input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId); ?>">
														<input type="hidden" name="businessId" id="businessId" value="<?php echo ($_SESSION["loggedBusinessId"]); ?>">
														<div class="form-body">
															<h4 class="form-section"><i class="ft-mail"></i>Opportunities</h4>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="oppourtunityName">Opportunity Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="oppourtunityName" class="form-control" name="oppourtunityName" value="<?php echo ($oppourtunityName); ?>" required>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="totals">Totals</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="totals" class="form-control" name="totals" value="<?php echo ($totals); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="status">Status</label>
																		<div class="col-md-9 mx-auto">
																			<fieldset>
																				<div class="d-inline-block custom-control custom-radio mr-1">
																					<input type="radio" class="custom-control-input bg-warning" name="status" value="1" <?php if ($status == 1) { ?> checked <?php } ?> id="colorRadio1">
																					<label class="custom-control-label" for="colorRadio1">Open</label>
																				</div>
																				<div class="d-inline-block custom-control custom-radio mr-1">
																					<input type="radio" class="custom-control-input bg-success" name="status" value="2" <?php if ($status == 2) { ?> checked <?php } ?> id="colorRadio2">
																					<label class="custom-control-label" for="colorRadio2">Closed Won</label>
																				</div>
																				<div class="d-inline-block custom-control custom-radio mr-1">
																					<input type="radio" class="custom-control-input bg-danger" name="status" value="3" <?php if ($status == 3) { ?> checked <?php } ?> id="colorRadio3">
																					<label class="custom-control-label" for="colorRadio3">Closed Lost</label>
																				</div>
																				<div class="d-inline-block custom-control custom-radio mr-1">
																					<input type="radio" class="custom-control-input bg-secondary" name="status" value="4" <?php if ($status == 4) { ?> checked <?php } ?> id="colorRadio3">
																					<label class="custom-control-label" for="colorRadio3">Inactive</label>
																				</div>
																			</fieldset>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="amount">Amount</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="amount" class="form-control" name="amount" value="<?php echo ($amount); ?>" <?php echo $addReadOnly = isset($_GET['oppourtunityId'])  ? 'readonly' : ''; ?>>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="openDate">Open Date</label>
																		<div class="col-md-9 mx-auto">
																			<div class="input-group date-picker-class">
																				<input type="text" class="form-control dateValidation" id="openDate" name="openDate" value="<?php echo ($openDate); ?>">
																				<div class="input-group-append">
																					<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="noofOpenDays">Days Open</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="noofOpenDays" class="form-control" name="noofOpenDays" value="<?php echo ($noofOpenDays); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="estCloseDate">Est. Close Date</label>

																		<div class="col-md-9 mx-auto">
																			<div class="input-group date-picker-class">
																				<input type="text" class="form-control dateValidation" id="estCloseDate" name="estCloseDate" value="<?php echo ($estCloseDate); ?>">
																				<div class="input-group-append">
																					<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="actCloseDate">Act. Close Date</label>
																		<div class="col-md-9 mx-auto">
																			<div class="input-group date-picker-class">
																				<input type="text" class="form-control dateValidation" id="actCloseDate" name="actCloseDate" value="<?php echo ($actCloseDate); ?>">
																				<div class="input-group-append">
																					<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="processId">Process<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<select id="processId" name="processId" class="select2 form-control" required data-parsley-errors-container="#error-processId">
																				<option value="" selected>Select</option>
																				<?php
																				if ($processes != "") {
																					while ($row = mysqli_fetch_assoc($processes)) {
																						$processId = $row['processId'];
																						$processName = $row['processName'];
																						$sortOrder = $row['sortOrder'];

																				?>
																						<option value="<?php echo ($processId); ?>" <?php if ($dbprocessId == $processId) { ?> selected <?php } ?>><?php echo ($processName); ?></option>
																				<?php

																					}
																				}
																				?>
																			</select>
																			<div id="error-processId"></div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="productId">Product<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<select id="productId" name="productId" class="select2 form-control" required data-parsley-errors-container="#error-productId">
																				<option value="" selected>Select</option>
																				<?php
																				if ($products != "") {
																					while ($row = mysqli_fetch_assoc($products)) {
																						$productId = $row['productId'];
																						$productTitle = $row['productTitle'];
																						$productSortOrder = $row['productSortOrder'];

																				?>
																						<option value="<?php echo ($productId); ?>" <?php if ($dbProductId == $productId) { ?> selected="true" <?php } ?>><?php echo ($productTitle); ?></option>
																				<?php

																					}
																				}
																				?>
																			</select>
																			<div id="error-productId"></div>
																		</div>
																	</div>
																</div>

																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="stageId">Stages<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<select id="stageId" name="stageId" class="select2 form-control" required data-parsley-errors-container="#error-stageId">
																				<option value="" selected>Select</option>
																				<?php
																				if ($stages != "") {
																					while ($row = mysqli_fetch_assoc($stages)) {
																						$stageId = $row['stageId'];
																						$stageName = $row['stageName'];
																						$sortOrder = $row['sortOrder'];

																				?>
																						<option value="<?php echo ($stageId); ?>" <?php if ($dbstageId == $stageId) { ?> selected="true" <?php } ?>><?php echo ($stageName); ?></option>
																				<?php

																					}
																				}
																				?>
																			</select>
																			<div id="error-stageId"></div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="categoryId">Category<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<select id="categoryId" name="categoryId" class="select2 form-control" required data-parsley-errors-container="#error-categoryId">
																				<option value="" selected>Select</option>

																			</select>
																			<div id="error-categoryId"></div>
																		</div>
																	</div>
																</div>
															</div>
															<div class="form-actions center">
																<a href="<?php if ($_SESSION['isPrimary'] == 1) {
																				echo "viewbusinesscustomers.html";
																			} else {
																				echo "viewemployeecontacts.html";
																			} ?>">
																	<button type="button" class="btn btn-outline-warning btn-glow mr-1 cancelClass">
																		<i class="ft-x"></i> Cancel
																	</button>
																</a>
																<button type="submit" id="btnSave1" class="btn btn-outline-primary btn-glow cancelClass">
																	<i class="la la-check-square-o"></i> <?php echo ($buttonTitle); ?>
																</button>
															</div>
														</div>
													</form> <br /><br /><br />
													<div class="form-body">
														<h4 class="form-section mb-2"><i class="ft-user-check"></i>View Opportunities</h4>
														<div class="table-responsive">
															<table class="table table-striped table-bordered default-ordering" width="100%" id="oppTable">
																<thead>
																	<tr>
																		<th>Opportunity Name</th>
																		<th>Product
																		</th>
																		<th class="text-center">Status</th>
																		<th class="text-center">Open Date</th>
																		<th class="text-center">Close Date</th>
																		<th class="text-center">Amount</th>
																		<th class="text-center">Action</th>
																	</tr>
																</thead>
																<tbody>
																	<?php
																	if ($opportunities != '') {
																		$statusName = '';
																		$fontColor = '';
																		while ($row = mysqli_fetch_assoc($opportunities)) {
																			$oppourtunityId = stripslashes($row['oppourtunityId']);
																			$oppourtunityName = stripslashes($row['oppourtunityName']);
																			$totals = stripslashes($row['totals']);
																			$status = stripslashes($row['status']);
																			$openDate = (date("m/d/Y h:i A", strtotime($row['openDate'])));

																			$estCloseDate  = isset($row['estCloseDate']) ? $row['estCloseDate'] : '';
																			$estCloseDate = ($estCloseDate != '0000-00-00 00:00:00') ? (date("m/d/Y h:i A", strtotime($estCloseDate))) : '-';
																			$actCloseDate  = isset($row['actCloseDate']) ? $row['actCloseDate'] : '';
																			$actCloseDate = ($actCloseDate != '0000-00-00 00:00:00') ? (date("m/d/Y h:i A", strtotime($actCloseDate))) : '-';

																			$noofOpenDays = stripslashes($row['noofOpenDays']);
																			$dbprocessId = stripslashes($row['processId']);
																			$dbstageId = stripslashes($row['stageId']);
																			$categoryId = stripslashes($row['categoryId']);

																			$amount = stripslashes($row['amount']);
																			$invoiceDetailId = stripslashes($row['invoiceDetailId']);

																			if ($status == 1) {
																				$statusName = 'Open';
																				$fontColor = 'black';
																			}
																			if ($status == 2) {
																				$statusName = 'Closed Won';
																				$fontColor = 'green';
																			}
																			if ($status == 3) {
																				$statusName = 'Closed Lost';
																				$fontColor = 'red';
																			}
																			if ($status == 4) {
																				$statusName = 'Inactive';
																				$fontColor = 'gray';
																			}
																			$productId = stripslashes($row['productId']);
																			$objBusinessProduct = new clsBusinessProduct();
																			$productDetail = $objBusinessProduct->GetProductDetails($productId);
																			$productName = isset($productDetail['productTitle']) ? $productDetail['productTitle'] : '';
																	?>
																			<tr>
																				<td><?php echo ($oppourtunityName); ?></td>
																				<td><?php echo ($productName); ?></td>
																				<td class="text-center" style="color:<?php echo ($fontColor); ?>"><?php echo ($statusName); ?></td>
																				<td class="text-center"><?php echo ($openDate); ?></td>
																				<td class="text-center"><?php echo ($estCloseDate); ?></td>
																				<td class="text-center"><?php echo '$' . ($amount); ?></td>
																				<td class="text-center">

																					<?php $url = 'viewcontactinfo.html?id=' . EncodeQueryData($customerId) . '&oppourtunityId=' . EncodeQueryData($oppourtunityId) . '#opportunities'; ?>
																					<?php $addUrl = $invoiceDetailId > '0' ? 'javascript:void(0);' : $url; ?>
																					<a href="<?php echo $url;  ?>" class="btn btn-sm btn-outline-success btn-glow mr-1 mb-1">Edit</a>
																					<!-- <a href="javascript:void(0)" class="<?php echo  $addClass = $invoiceDetailId > '0' ? '' : 'deleteOppAjaxRow'; ?>  btn btn-sm btn-outline-danger  btn-glow mr-1 mb-1" style="<?php echo  $addCss = $invoiceDetailId > '0' ? 'cursor: no-drop;' : ''; ?>" oppourtunityId="<?php echo EncodeQueryData($oppourtunityId); ?>">Delete</a>																						    -->
																				</td>
																			</tr>
																	<?php
																		}
																	}
																	?>
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>
										</section>
									</div>

									<!--- notes list ---->
									<div role="tabpanel" class="tab-pane" id="notes" aria-labelledby="notes-tab" aria-expanded="true">
										<section id="horizontal-form-layouts">
											<div class="row">
												<div class="col-md-12">
													<div class="form-body">
														<form class="form form-horizontal" id="NotesForm" method="post" action="addnotessubmit.html" enctype="multipart/form-data">
															<input type="hidden" name="notesId" id="notesId" value="<?php echo ($notesId); ?>">
															<input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId); ?>">
															<input type="hidden" name="businessId" id="businessId" value="<?php echo ($_SESSION["loggedBusinessId"]); ?>">
															<div class="form-body">
																<h4 class="form-section"><i class="ft-mail"></i>Notes</h4>
																<div class="row">
																	<div class="col-md-6">
																		<div class="form-group row">
																			<label class="col-md-3 label-control" for="notestitle">Title<span class="validate-field">*</span></label>
																			<div class="col-md-9 mx-auto">
																				<input type="text" id="notestitle" class="form-control" name="notestitle" value="<?php echo ($notestitle); ?>" required>
																			</div>
																		</div>
																	</div>
																	<div class="col-md-6">
																		<div class="form-group row">
																			<label class="col-md-3 label-control" for="notesdate">Date</label>
																			<div class="col-md-9 mx-auto">
																				<div class="input-group date-picker-class">
																					<input type="text" class="form-control dateValidation" id="notesdate" name="notesdate" value="<?php echo ($notesdate); ?>">
																					<div class="input-group-append">
																						<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																	<div class="col-md-6">
																		<div class="form-group row">
																			<label class="col-md-3 label-control" for="notesDescription">Description</label>
																			<div class="col-md-9 mx-auto">
																				<textarea class="form-control" id="notesDescription" name="notesDescription" rows="2"><?php echo ($notesDescription); ?></textarea>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="form-actions center">
																	<a href="<?php if ($_SESSION['isPrimary'] == 1) {
																					echo "viewbusinesscustomers.html";
																				} else {
																					echo "viewemployeecontacts.html";
																				} ?>">
																		<button type="button" class="btn btn-outline-warning btn-glow mr-1 cancelClass">
																			<i class="ft-x"></i> Cancel
																		</button>
																	</a>
																	<button type="submit" id="btnSaveNotes" class="btn btn-outline-primary btn-glow cancelClass">
																		<i class="la la-check-square-o"></i> Add
																	</button>
																</div>
															</div>
														</form> <br /><br /><br />
														<div class="form-body">
															<h4 class="form-section mb-2"><i class="ft-user-check"></i>View Notes</h4>
															<div class="table-responsive">
																<table class="table table-striped table-bordered default-ordering" width="100%" id="notesTable">
																	<thead>
																		<tr>
																			<th>Title</th>
																			<th class="text-center">Description</th>
																			<th class="text-center">Date</th>
																			<th class="text-center">Action</th>
																		</tr>
																	</thead>
																	<tbody>
																		<?php
																		if ($allNotes != '') {
																			while ($row = mysqli_fetch_assoc($allNotes)) {
																				$notesId = $row['notesId'];
																				$businessId = $row['businessId'];
																				$customerIds = $row['customerId'];
																				$notestitle = ($row['notestitle']);
																				$notesDescription = ($row['notesDescription']);
																				$notesdate = (date("m/d/Y h:i A", strtotime($row['notesdate'])));
																		?>
																				<tr>
																					<td><?php echo ($notestitle); ?></td>
																					<td class="desc"><?php echo ($notesDescription); ?></td>
																					<td class="text-center"><?php echo ($notesdate); ?></td>
																					<td class="text-center mob-wrap">
																						<a href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>&notesId=<?php echo EncodeQueryData($notesId); ?>#notes" class="btn btn-sm btn-outline-success btn-glow mr-1 mb-1">Edit</a>
																						<a href="javascript:void(0)" class="deleteNotesAjaxRow btn btn-sm btn-outline-danger  btn-glow mr-1 mb-1" notesId="<?php echo EncodeQueryData($notesId); ?>">Delete</a>
																					</td>
																				</tr>
																		<?php
																			}
																		}
																		?>
																	</tbody>
																</table>
															</div>
														</div>
													</div>
												</div>
											</div>
										</section>
									</div>

									<!--- SECONDARY CONTACTS ---->
									<div role="tabpanel" class="tab-pane" id="secondary" aria-labelledby="secondary-tab" aria-expanded="true">
										<section id="horizontal-form-layouts">
											<div class="row">
												<div class="col-md-12">
													<form class="form form-horizontal" id="secondaryContactForm" method="post" action="addsecondarycontactsubmit.html" enctype="multipart/form-data">
														<input type="hidden" name="contactSecondaryId" id="contactSecondaryId" value="<?php echo ($contactSecondaryId); ?>">
														<input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId); ?>">
														<input type="hidden" name="businessId" id="businessId" value="<?php echo ($_SESSION["loggedBusinessId"]); ?>">
														<div class="form-body">
															<h4 class="form-section"><i class="ft-mail"></i>Secondary Contact</h4>

															<div class="row">
																<div class="col-md-6 skin skin-square">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="isSameAddress">Same as Business Address</label>
																		<div class="col-md-9 mx-auto">
																			<fieldset>
																				<input type="checkbox" name="isSameAddress" id="isSameAddress" <?php if ($isSameAddress == 1) { ?> checked <?php } ?>>
																			</fieldset>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryFirstName">First Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryFirstName" class="form-control" placeholder="First Name" name="secondaryFirstName" value="<?php echo ($secondaryFirstName); ?>" required>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryLastName">Last Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryLastName" class="form-control" placeholder="Last Name" name="secondaryLastName" value="<?php echo ($secondaryLastName); ?>" required>
																		</div>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryTitle">Title</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryTitle" class="form-control" name="secondaryTitle" value="<?php echo ($secondaryTitle); ?>">
																		</div>
																	</div>
																</div>

																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryEmail">Email<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="email" id="secondaryEmail" class="form-control" name="secondaryEmail" value="<?php echo ($secondaryEmail); ?>" required>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryMobileNo">Mobile No.<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryMobileNo" class="form-control phone-inputmask" name="secondaryMobileNo" value="<?php echo ($secondaryMobileNo); ?>" oninput="validatePhoneNumber(this);" required>
																			<span id="errorMobile" style="color: red;font-size: 13px;"></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryPhoneNo">Phone No.</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryPhoneNo" class="form-control phone-inputmask" name="secondaryPhoneNo" value="<?php echo ($secondaryPhoneNo); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryAddress">Address 1</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryAddress" class="form-control" placeholder="Address 1" name="secondaryAddress" value="<?php echo ($secondaryAddress); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryAddress2">Address 2</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryAddress2" class="form-control" placeholder="Address 2" name="secondaryAddress2" value="<?php echo ($secondaryAddress2); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryCity">City</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryCity" class="form-control letters-only" placeholder="City" name="secondaryCity" value="<?php echo ($secondaryCity); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryStateId">State</label>
																		<div class="col-md-9 mx-auto">
																			<select id="secondaryStateId" name="secondaryStateId" class="select2 form-control step4">
																				<option value="0" selected>Select</option>
																			</select>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="secondaryZipCode">Zip Code</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="secondaryZipCode" class="form-control numbers-only" placeholder="Zip Code" name="secondaryZipCode" value="<?php echo ($secondaryZipCode); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="cboCountry1">Country</label>
																		<div class="col-md-9 mx-auto">
																			<select id="cboCountry1" name="cboCountry1" class="select2 form-control step3">
																				<option value="" selected>Select</option>
																				<?php

																				if ($countries != "") {
																					foreach ($countries as $row) {
																						$location_id  = $row['location_id'];
																						$name  = stripslashes($row['name']);

																				?>
																						<option value="<?php echo ($location_id); ?>" <?php if ($secondaryCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
																				<?php

																					}
																				}
																				?>
																			</select>
																		</div>
																	</div>
																</div>

																<div class="col-md-6" style="display:none;">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="companyName">Company Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="companyName" class="form-control" name="companyName" value="<?php echo ($companyName); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="comments">Comments</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="comments" class="form-control" name="comments" value="<?php echo ($comments); ?>">
																		</div>
																	</div>
																</div>
															</div>
															<div class="form-actions center">
																<a href="<?php if ($_SESSION['isPrimary'] == 1) {
																				echo "viewbusinesscustomers.html";
																			} else {
																				echo "viewemployeecontacts.html";
																			} ?>">
																	<button type="button" class="btn btn-outline-warning btn-glow mr-1 cancelClass">
																		<i class="ft-x"></i> Cancel
																	</button>
																</a>
																<button type="submit" id="btnSave1" class="btn btn-outline-primary btn-glow cancelClass">
																	<i class="la la-check-square-o"></i> <?php echo ($buttonTitle); ?>
																</button>
															</div>
														</div>
													</form> <br /><br /><br />
													<div class="form-body">
														<h4 class="form-section mb-2"><i class="ft-mail mr-1"></i>Secondary Contacts</h4>
														<div class="table-responsive">
															<table class="table table-striped table-bordered default-ordering" width="100%" id="contactSecondaryIdTable">
																<thead>
																	<tr>
																		<th>Name</th>
																		<th>Title</th>
																		<th class="text-left">Address</th>
																		<th class="text-left">Contact Info</th>
																		<th class="text-center">Action</th>
																	</tr>
																</thead>
																<tbody>
																	<?php
																	if ($secondaries != '') {
																		$secFullName = '';
																		$objCountryStateMaster = new clsCountryStateMaster();
																		while ($row = mysqli_fetch_assoc($secondaries)) {
																			$contactSecondaryId = $row['contactSecondaryId'];
																			$companyName = $row['companyName'];
																			$email = $row['email'];
																			$phoneNo = ($row['phoneNo']);
																			$secondaryFirstName = ($row['secondaryFirstName']);
																			$secondaryLastName = ($row['secondaryLastName']);
																			$stateIds = ($row['stateId']);
																			$zipCode = ($row['zipCode']);
																			$city = ($row['city']);
																			$address = ($row['address']);
																			$address2 = ($row['address2']);
																			$secondaryTitle = ($row['secondaryTitle']);
																			$secFullName = $secondaryFirstName . ' ' . $secondaryLastName;

																			//get state name
																			$state  = $objCountryStateMaster->GetLocationName($stateIds);
																			$dbCountryIds = $objCountryStateMaster->GetParentIdFromChildId($stateIds);
																			$country = $objCountryStateMaster->GetLocationName($dbCountryIds);

																			$contactInfo = "";

																			if ($phoneNo != '') {
																				$contactInfo .= '<b>Phone No :</b> ' . $phoneNo;
																			}
																			if ($email != '') {
																				$contactInfo .= '<br><b>Email :</b> ' . $email;
																			}

																			$fullAddress = '';
																			if ($address != '') {
																				$fullAddress .= $address . ' ' . $address2 . ',';
																			}
																			if ($city != '') {
																				$fullAddress .= '<br> ' . $city . ',';
																			}
																			if ($zipCode != '') {
																				$fullAddress .= '&nbsp  ' . $zipCode;
																			}
																			if ($state != '') {
																				$fullAddress .= '<br> ' . $state;
																			}
																			if ($country != '') {
																				$fullAddress .= ' - ' . $country;
																			}
																	?>
																			<tr>
																				<td><?php echo ($secFullName); ?></td>
																				<td><?php echo ($secondaryTitle); ?></td>
																				<td class="text-left"><?php echo ($fullAddress); ?></td>
																				<td class="text-left"><?php echo ($contactInfo); ?></td>
																				<td class="text-center">
																					<a href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>&contactSecondaryId=<?php echo EncodeQueryData($contactSecondaryId); ?>#secondary" class="btn btn-sm btn-outline-success btn-glow mr-1 mb-1">Edit</a>
																					<!-- <?php $url = 'viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId);?>&oppourtunityId=<?php echo EncodeQueryData($oppourtunityId);?>#opportunities'; ?>															
																		<a href="<?php echo  $addCss = isset($invoiceDetailId) ? 'javascript:void(0);' : $url; ?>" class="btn btn-sm btn-outline-success btn-glow mr-1 mb-1" style="<?php echo  $addCss = isset($invoiceDetailId) ? 'cursor: no-drop;' : ''; ?>" >Edit</a> -->
																					<a href="javascript:void(0)" class="deleteContactSecondaryIdAjaxRow btn btn-sm btn-outline-danger  btn-glow mr-1 mb-1" contactSecondaryId="<?php echo EncodeQueryData($contactSecondaryId); ?>">Delete</a>
																				</td>
																			</tr>
																	<?php
																		}
																		unset($objCountryStateMaster);
																	}
																	?>
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>
										</section>
									</div>

									<!--- PERSONAL INFORMATION ---->
									<div role="tabpanel" class="tab-pane" id="personalinfo" aria-labelledby="personalinfo-tab" aria-expanded="true">
										<section id="horizontal-form-layouts">
											<div class="row">
												<div class="col-md-12">
													<form class="form form-horizontal" id="personalContactForm" method="post" action="addpersonalcontactsubmit.html" enctype="multipart/form-data">
														<input type="hidden" name="personalContactDetailId" id="personalContactDetailId" value="<?php echo ($personalContactDetailId); ?>">
														<input type="hidden" name="customerId" id="customerId" value="<?php echo ($customerId); ?>">
														<input type="hidden" name="businessId" id="businessId" value="<?php echo ($_SESSION["loggedBusinessId"]); ?>">
														<div class="form-body">
															<h4 class="form-section"><i class="ft-user"></i>Personal Details</h4>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalFirstName">First Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalFirstName" class="form-control" placeholder="First Name" name="personalFirstName" value="<?php echo ($personalFirstName); ?>" required>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalLastName">Last Name<span class="validate-field">*</span></label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalLastName" class="form-control" placeholder="Last Name" name="personalLastName" value="<?php echo ($personalLastName); ?>" required>
																		</div>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalAddress">Address 1</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalAddress" class="form-control" placeholder="Address 1" name="personalAddress" value="<?php echo ($personalAddress); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalAddress2">Address 2</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalAddress2" class="form-control" placeholder="Address 2" name="personalAddress2" value="<?php echo ($personalAddress2); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalCity">City</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalCity" class="form-control letters-only" placeholder="City" name="personalCity" value="<?php echo ($personalCity); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalStateId">State</label>
																		<div class="col-md-9 mx-auto">
																			<select id="personalStateId" name="personalStateId" class="select2 form-control step2">
																				<option value="0" selected>Select</option>
																			</select>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalZipCode">Zip Code</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalZipCode" class="form-control numbers-only" placeholder="Zip Code" name="personalZipCode" value="<?php echo ($personalZipCode); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalCountryId">Country</label>
																		<div class="col-md-9 mx-auto">
																			<select id="cboCountry" name="cboCountry" class="select2 form-control step1">
																				<option value="" selected>Select</option>
																				<?php

																				if ($countries != "") {
																					foreach ($countries as $row) {
																						$location_id  = $row['location_id'];
																						$name  = stripslashes($row['name']);

																				?>
																						<option value="<?php echo ($location_id); ?>" <?php if ($personalCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
																				<?php

																					}
																				}
																				?>
																			</select>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalEmail">Email</label>
																		<div class="col-md-9 mx-auto">
																			<input type="email" id="personalEmail" class="form-control" placeholder="Email" name="personalEmail" value="<?php echo ($personalEmail); ?>">
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="personalPhoneNo">Mobile No.</label>
																		<div class="col-md-9 mx-auto">
																			<input type="text" id="personalPhoneNo" class="form-control phone-inputmask mobileNo" placeholder="Mobile No." name="personalPhoneNo" value="<?php echo ($personalPhoneNo); ?>" oninput="validatePhoneNumber(this);">
																			<span id="errorMobile2" style="color: red;font-size: 13px;"></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="birthDate">Birth Date</label>
																		<div class="col-md-9 mx-auto">
																			<div class="input-group date-picker-class">
																				<input type="text" class="form-control dateValidation" id="birthDate" name="birthDate" value="<?php echo ($birthDate); ?>">
																				<div class="input-group-append">
																					<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="anniversaryDate">Anniversary Date</label>
																		<div class="col-md-9 mx-auto">
																			<div class="input-group date-picker-class">
																				<input type="text" class="form-control dateValidation1" id="anniversaryDate" name="anniversaryDate" value="<?php echo ($anniversaryDate); ?>">
																				<div class="input-group-append">
																					<span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group row">
																		<label class="col-md-3 label-control" for="relationId">Relationship</label>
																		<div class="col-md-9 mx-auto">
																			<select id="relationId" name="relationId" class="select2 form-control">
																				<option value="0" selected>Select</option>
																				<?php
																				if ($relations != "") {
																					while ($row = mysqli_fetch_assoc($relations)) {
																						$relationId = $row['relationId'];
																						$title = $row['title'];
																						$sortOrdera = $row['sortOrder'];

																				?>
																						<option value="<?php echo ($relationId); ?>" <?php if ($dbcontactRelationId == $relationId) { ?> selected <?php } ?>><?php echo ($title); ?></option>
																				<?php

																					}
																				}
																				?>
																			</select>
																		</div>
																	</div>
																</div>
															</div>
															<div class="form-actions center">
																<a href="<?php if ($_SESSION['isPrimary'] == 1) {
																				echo "viewbusinesscustomers.html";
																			} else {
																				echo "viewemployeecontacts.html";
																			} ?>">
																	<button type="button" class="btn btn-outline-warning btn-glow mr-1 cancelClass">
																		<i class="ft-x"></i> Cancel
																	</button>
																</a>
																<button type="submit" id="btnSave1" class="btn btn-outline-primary btn-glow cancelClass">
																	<i class="la la-check-square-o"></i> <?php echo ($buttonTitle); ?>
																</button>
															</div>
														</div>
													</form><br /><br /><br />
													<div class="form-body">
														<h4 class="form-section mb-2"><i class="ft-user-check"></i>View Personal Contacts</h4>
														<div class="table-responsive">
															<table class="table table-striped table-bordered default-ordering" width="100%" id="personalContactTable">
																<thead>
																	<tr>
																		<th>Name</th>
																		<th>Relationship</th>
																		<th class="text-left">Address</th>
																		<th class="text-left">Contact Info</th>
																		<th class="text-center">Action</th>
																	</tr>
																</thead>
																<tbody>
																	<?php
																	if ($personalcontacts != '') {
																		$personFullName = '';
																		$relationRow = '';
																		$relationTitle = '';
																		$objCountryStateMaster = new clsCountryStateMaster();
																		$objBusinessContactRelationship = new clsBusinessContactRelationship();
																		while ($row = mysqli_fetch_assoc($personalcontacts)) {
																			$personalContactDetailId = $row['personalContactDetailId'];
																			$email = $row['email'];
																			$phoneNo = ($row['phoneNo']);
																			$firstName = ($row['firstName']);
																			$lastName = ($row['lastName']);
																			$stateId1 = ($row['stateId']);
																			$zipCode = ($row['zipCode']);
																			$city = ($row['city']);
																			$address = ($row['address']);
																			$address2 = ($row['address2']);
																			$relationId = ($row['relationId']);
																			$personFullName = $firstName . ' ' . $lastName;

																			//***** GET RELATIONSHIP NAME *****//
																			$relationRow = $objBusinessContactRelationship->GetRelationshipDetails($relationId);
																			if ($relationRow != '') {
																				$relationTitle = stripslashes($relationRow['title']);
																			} else {
																				$relationTitle = '';
																			}

																			//get state name
																			$state  = $objCountryStateMaster->GetLocationName($stateId1);
																			$dbCountryId1 = $objCountryStateMaster->GetParentIdFromChildId($stateId1);
																			$country = $objCountryStateMaster->GetLocationName($dbCountryId1);

																			$personContactInfo = "";

																			if ($phoneNo != '') {
																				$personContactInfo .= '<b>Phone No :</b> ' . $phoneNo;
																			}
																			if ($email != '') {
																				$personContactInfo .= '<br><b>Email :</b> ' . $email;
																			}

																			$personFullAddress = '';
																			if ($address != '') {
																				$personFullAddress .= $address . ' ' . $address2 . ',';
																			}
																			if ($city != '') {
																				$personFullAddress .= '<br> ' . $city . ',';
																			}
																			if ($zipCode != '') {
																				$personFullAddress .= '&nbsp  ' . $zipCode;
																			}
																			if ($state != '') {
																				$personFullAddress .= '<br> ' . $state;
																			}
																			if ($country != '') {
																				$personFullAddress .= ' - ' . $country;
																			}
																	?>
																			<tr>
																				<td><?php echo ($personFullName); ?></td>
																				<td><?php echo ($relationTitle); ?></td>
																				<td class="text-left"><?php echo ($personFullAddress); ?></td>
																				<td class="text-left"><?php echo ($personContactInfo); ?></td>
																				<td class="text-center mob-wrap">
																					<a href="viewcontactinfo.html?id=<?php echo EncodeQueryData($customerId); ?>&personalContactDetailId=<?php echo EncodeQueryData($personalContactDetailId); ?>#personalinfo" class="btn btn-sm btn-outline-success btn-glow mr-1 mb-1">Edit</a>
																					<a href="javascript:void(0)" class="deletePersonalContactAjaxRow btn btn-sm btn-outline-danger  btn-glow mr-1 mb-1" personalContactDetailId="<?php echo EncodeQueryData($personalContactDetailId); ?>">Delete</a>
																				</td>
																			</tr>
																	<?php
																		}
																		unset($objCountryStateMaster);
																	}
																	?>
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>
										</section>
									</div>

								</div>
							</div>
						</div>
					</div>
				</section>
				<!--/ Default ordering table -->
			</div>
		</div>
	</div>
	<!-- END: Content-->

	<!-- BEGIN: Footer-->
	<?php include('includes/footer.php'); ?>
	<?php include('includes/footerJs.php'); ?>
	<?php include('includes/datatableJs.php'); ?>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/custom.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/common.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/multiselect-dropdown.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/select/form-select2.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/polyfill.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/jquery.cascadingdropdown.js"></script>
	<!-- <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/checkbox-radio.js"></script> -->
	<script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/moment/moment.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-inputmask.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/js/read-more.min.js"></script>
	<script>
		$readMoreJS({
			target: '.desc', // Selector of the element the plugin applies to (any CSS selector, eg: '#', '.'). Default: ''
			wordsCount: 10, // Number of words to initially display (any number). Default: 50
			toggle: true, // If true, user can toggle between 'read more' and 'read less'. Default: true
			moreLink: 'read more ...', // The text of 'Read more' link. Default: 'read more ...'
			lessLink: ' read less' // The text of 'Read less' link. Default: 'read less'
		});

		var dbcategoryId = '<?php echo $dbcategoryId; ?>';
		$(window).on('load', function() {
			var dbProductId = '<?php echo $dbProductId; ?>';
			if (dbProductId > 0)
				$('#productId').trigger('change');
			else
				$('#productId').val('').trigger('change');


		});

		$(document).ready(() => {
			let url = location.href.replace(/\/$/, "");

			if (location.hash) {
				const hash = url.split("#");
				$('#myTab a[href="#' + hash[1] + '"]').tab("show");
				url = location.href.replace(/\/#/, "#");
				history.replaceState(null, null, url);
				setTimeout(() => {
					$(window).scrollTop(0);
				}, 400);
			}

			$('a[data-toggle="tab"]').on("click", function() {
				let newUrl;
				const hash = $(this).attr("href");
				if (hash == "#home") {
					newUrl = url.split("#")[0];
				} else {
					newUrl = url.split("#")[0] + hash;
				}
				newUrl += "/";
				history.replaceState(null, null, newUrl);
			});


		});

		function validatePhoneNumber(input) {
			const phoneNumber = input.value.replace(/\D/g, ''); // Remove non-numeric characters
			if (phoneNumber.length < 10) {
				document.getElementById('errorMobile').textContent = 'Mobile number must have at least 10 digits.';
				document.getElementById('errorMobile2').textContent = 'Mobile number must have at least 10 digits.';
				$('#btnSave1').attr('disabled', 'disabled');
			} else {
				document.getElementById('errorMobile').textContent = '';
				document.getElementById('errorMobile2').textContent = '';
				$('#btnSave1').removeAttr('disabled');

			}
		}

		//***** DATE-TIME PICKER WITH CAL-ICON ******//
		$(function() {
			$('.date-picker-class').datetimepicker({
				icons: {
					date: 'fa fa-calendar',
					up: 'fa fa-chevron-up',
					down: 'fa fa-chevron-down',
					previous: 'fa fa-chevron-left',
					next: 'fa fa-chevron-right',
					today: 'fa fa-check',
					clear: 'fa fa-trash'
				},
				allowInputToggle: true
			});
		});


		//***** TO-DO FORM VALIDATION *****//
		// $('#todoForm').parsley().on('field:validated', function() {
		// 		var ok = $('.parsley-error').length === 0;
		// })
		// .on('form:submit', function() {
		// 	return true; // Don't submit form for this demo
		// });

		//To-Do datatable alignment
		var current_todo_datatable = $("#todoTable").DataTable({
			"aaSorting": [],
			// "order": [[ 0, "DESC" ]],
			"language": {
				"info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			"aoColumns": [{
					"sWidth": "30%",
					"bSortable": true
				},
				{
					"sWidth": "30%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": false
				}
			]
		});

		

		//****** STATUS CHANGE TO-DO ******//		  
		$('.isStatus').on('click', function() {
			var thisanchor = $(this);
			var todoId = $(this).attr('todoId');
			var isStatus = $(this).attr('isStatus');

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with status change?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Ok',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger btn-glow ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_set_todo_status.html",
						data: {
							todoId: todoId,
							isStatus: isStatus
						},
						success: function() {
							thisanchor.attr('isStatus', isStatus == 0 ? 1 : 0);
							if (isStatus == 0) {
								thisanchor.html('Complete');
								$('#isActive_' + todoId).removeClass('btn btn-sm btn-outline-secondary btn-glow');
								$('#isActive_' + todoId).addClass('btn btn-sm btn-outline-success btn-glow');
							} else {
								thisanchor.html('Incomplete');
								$('#isActive_' + todoId).removeClass('btn btn-sm btn-outline-success btn-glow');
								$('#isActive_' + todoId).addClass('btn btn-sm btn-outline-secondary btn-glow');
							}
							Swal.fire({
								type: "success",
								title: 'Status updated!',
								confirmButtonClass: 'btn btn-outline-success btn-glow',
							})
						}
					});
				}
			})
		});


		//***** DELETE TO-DO LIST RECORD *****//
		$('.deleteAjaxRow').on('click', function() {
			var current_datatable_row = current_todo_datatable.row($(this).parents('tr'));
			var todoId = $(this).attr('todoId');;

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_todo.html",
						data: {
							todoId: todoId
						},
						success: function() {
							current_todo_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success',
							})
						}
					});
				}
			})
		});


		//***** OPPORTUNITIES TABLE *****//
		var current_opp_datatable = $("#oppTable").DataTable({
			"order": [
				[0, "DESC"]
			],
			"language": {
				"info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			"aoColumns": [{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "10%",
					"bSortable": true
				},
				{
					"sWidth": "15%",
					"bSortable": true
				},
				{
					"sWidth": "15%",
					"bSortable": true
				},
				{
					"sWidth": "10%",
					"bSortable": true
				},
				{
					"sWidth": "10%",
					"bSortable": true
				}
			]
		});

		//***** OPPORTUNITIES FORM VALIDATION *****//
		$('#oppFprm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});

		//***** DELETE OPPORTUNITIES LIST RECORD *****//
		$('.deleteOppAjaxRow').on('click', function() {
			var current_datatable_row = current_opp_datatable.row($(this).parents('tr'));
			var oppourtunityId = $(this).attr('oppourtunityId');;

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_opportunities.html",
						data: {
							oppourtunityId: oppourtunityId
						},
						success: function() {
							current_opp_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success',
							})
						}
					});
				}
			})
		});

		$('#productId').on('change', function() {
			var businessId = '<?php echo $_SESSION["loggedBusinessId"]; ?>';
			var productId = $(this).val();

			$("#categoryId").html('');
			if (productId > 0) {
				$.ajax({
					type: "GET",
					dataType: 'json',
					url: "../ajax/ajax_get_categories_by_product.html",
					data: {
						productId: productId,
						businessId: businessId
					},
					success: function(data) {
						var categories = "<option value=''>Select</option>";
						$.each(data, function(name, value) {
							categories += "<option value='" + value.categoryId + "'>" + value.categoryTitle + "</option>";
						});

						$("#categoryId").html(categories);

						if (typeof dbcategoryId !== 'undefined')
							$('#categoryId').val(dbcategoryId).trigger('change');
						else
							$('#categoryId').trigger('change');

					}
				});
			}
		});

		//***** NOTES DATATABLE ALIGNMENT *****//
		var current_notes_datatable = $("#notesTable").DataTable({
			"order": [
				[0, "DESC"]
			],
			"language": {
				"info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			"aoColumns": [{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "30%",
					"bSortable": true
				},
				{
					"sWidth": "40%",
					"bSortable": true
				},
				{
					"sWidth": "10%",
					"bSortable": true
				}
			]
		});


		//***** TO-DO FORM VALIDATION *****//
		$('#NotesForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				$('#btnSaveNotes').attr('disabled', 'disabled');
				return true; // Don't submit form for this demo
			});

		//***** DELETE NOTES LIST RECORD *****//
		// $('.deleteNotesAjaxRow').on('click', function () 
		$("#notesTable tbody").on("click", ".deleteNotesAjaxRow", function() {
			var current_datatable_row = current_notes_datatable.row($(this).parents('tr'));
			var notesId = $(this).attr('notesId');;

			// alert(notesId);

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_notes.html",
						data: {
							notesId: notesId
						},
						success: function() {
							current_notes_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success',
							})
						}
					});
				}
			})
		});


		//***** SECONDARY CONTACT TABLE *****//
		var current_secondary_datatable = $("#contactSecondaryIdTable").DataTable({
			"order": [
				[0, "DESC"]
			],
			"language": {
				"info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			"aoColumns": [{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": false
				}
			]
		});

		//***** SECONDARY CONTACT FORM VALIDATION *****//
		$('#secondaryContactForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});

		//***** DELETE SECONDARY CONTACT LIST RECORD *****//
		$('.deleteContactSecondaryIdAjaxRow').on('click', function() {
			var current_datatable_row = current_secondary_datatable.row($(this).parents('tr'));
			var contactSecondaryId = $(this).attr('contactSecondaryId');;

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_secondary_contacts.html",
						data: {
							contactSecondaryId: contactSecondaryId
						},
						success: function() {
							current_secondary_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success',
							})
						}
					});
				}
			})
		});

		//******* SECONDARY CONTACT FORM ******//
		$('#secondaryContactForm').cascadingDropdown({
			selectBoxes: [{
					selector: '.step3',
					selected: '<?php echo ($secondaryCountryId); ?>'
				},
				{
					selector: '.step4',
					selected: '<?php echo ($secondaryStateId); ?>',
					requires: ['.step3'],
					requireAll: true,
					source: function(request, response) {

						$.getJSON('../ajax/getStates1.html', request, function(data) {
							response($.map(data, function(item, index) {
								return {
									label: item['StateNames'],
									value: item['StateIds']
								};
							}));
						});
					}
				}
			]
		});

		//****** ONCLICK CHECKBOX GET BUSINESS ADDRESS AND FILL-UP ******//		  
		$('#isSameAddress').on('click', function() {
			var businessId = $("#businessId").val();

			if ($(this).prop("checked") == true) {
				$.ajax({
					type: "GET",
					url: "../ajax/ajax_get_business_details.html",
					data: {
						businessId: businessId
					},
					dataType: "json",
					success: function(data) {
						$("#secondaryAddress").val(data[0].secondaryAddress);
						$("#secondaryAddress2").val(data[0].secondaryAddress2);
						$("#secondaryCity").val(data[0].secondaryCity);
						$("#cboCountry1").val(data[0].cboCountry1);
						$("#secondaryStateId").val(data[0].secondaryStateId);
						$("#secondaryZipCode").val(data[0].secondaryZipCode);
					}
				});
			} else if ($(this).prop("checked") == false) {
				$("#secondaryAddress").val('');
				$("#secondaryCity").val('');
				$("#cboCountry1").val('');
				$("#secondaryStateId").val('');
				$("#secondaryZipCode").val('');
			}



		});


		//country dropdown code
		$('#personalContactForm').cascadingDropdown({
			selectBoxes: [{
					selector: '.step1',
					selected: '<?php echo ($personalCountryId); ?>'
				},
				{
					selector: '.step2',
					selected: '<?php echo ($personalStateId); ?>',
					requires: ['.step1'],
					requireAll: true,
					source: function(request, response) {

						$.getJSON('../ajax/getStates.html', request, function(data) {
							response($.map(data, function(item, index) {
								return {
									label: item['StateName'],
									value: item['StateId']
								};
							}));
						});
					}
				}
			]
		});

		//***** PERSONAL CONTACT TABLE *****//
		var current_personal_datatable = $("#personalContactTable").DataTable({
			"order": [
				[0, "DESC"]
			],
			"language": {
				"info": "Showing _PAGE_ of _PAGES_ Entries",
				"lengthMenu": "Show _MENU_ Entries"
			},
			"aoColumns": [{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": true
				},
				{
					"sWidth": "20%",
					"bSortable": false
				}
			]
		});

		//***** PERSONAL CONTACT FORM VALIDATION *****//
		$('#personalContactForm').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				return true; // Don't submit form for this demo
			});

		//***** DELETE PERSONAL CONTACT LIST RECORD *****//
		$('.deletePersonalContactAjaxRow').on('click', function() {
			var current_datatable_row = current_personal_datatable.row($(this).parents('tr'));
			var personalContactDetailId = $(this).attr('personalContactDetailId');;

			Swal.fire({
				title: 'Confirmation',
				text: "Continue with delete?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Delete',
				confirmButtonClass: 'btn btn-outline-primary',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_personal_contacts.html",
						data: {
							personalContactDetailId: personalContactDetailId
						},
						success: function() {
							current_personal_datatable.row(current_datatable_row).remove().draw(false);
							Swal.fire({
								type: "success",
								title: 'Deleted!',
								text: 'Deleted.',
								confirmButtonClass: 'btn btn-success',
							})
						}
					});
				}
			})
		});
	</script>

</body>
<!-- END: Body-->

</html>