<?php
//include classes and config file here	
include('./includes/validatebusinesslogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsEmployee.php');
include('../class/clsChatApp.php');


//variables
$countries = '';
$dbCountryId = '224';
$isIndependent = 0;
$stateId = 0;
$employeeId = 0;
$businessId = $_SESSION["loggedBusinessId"];
$firstName = '';
$lastName = '';
$email = '';
$phoneNo = '';
$address = '';
$address2 = '';
$city = '';
$zipCode = '';
$imageName = '';
$employeeImagePath = '';
$pageTitle = 'Add';
$buttonTitle = 'Save';
$empStartDate  = date("m/d/Y h:i A");
$empTitle = '';
$empNotes = '';
$empNotes = '';


//Get Location
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
unset($objCountryStateMaster);

if (isset($_GET['id'])) {
    $employeeId = DecodeQueryData($_GET['id']);

    $pageTitle = 'Edit';
    $buttonTitle = 'Update';

    //object
    $objEmployee = new clsEmployee();

    //get employee details
    $row = $objEmployee->GetEmployeeDetails($employeeId);
    unset($objEmployee);

    $firstName  = stripslashes($row['firstName']);
    $lastName  = stripslashes($row['lastName']);
    $email  = stripslashes($row['email']);
    $phoneNo  = stripslashes($row['phoneNo']);
    $address  = stripslashes($row['address']);
    $address2  = stripslashes($row['address2']);
    $city  = stripslashes($row['city']);
    $zipCode  = stripslashes($row['zipCode']);
    $stateId  = stripslashes($row['stateId']);
    $imageName  = stripslashes($row['imageName']);
    $isIndependent  = stripslashes($row['isIndependent']);
    $empTitle  = stripslashes($row['empTitle']);
    $empStartDate  = (date("m/d/Y h:i A", strtotime($row['empStartDate'])));
    $empNotes  = stripslashes($row['empNotes']);

    //get employee image path
    $employeeImagePath = GetEmployeeImagePath($businessId, $employeeId, $imageName);

    //get Country from State
    $objCountryStateMaster = new clsCountryStateMaster();
    $dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
    unset($objCountryStateMaster);

    $objChatApp = new clsChatApp();
    $usermanagemetData=$objChatApp->getUserDetails(3,$employeeId);
    $usermanagemetId = $usermanagemetData['id'];

}


?>
<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="ltr">

<head>
    <title><?php echo ($pageTitle); ?> Employee</title>
    <?php include('includes/headerCss.php'); ?>
    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/assets/vendors/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/animate/animate.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/extensions/sweetalert2.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/selects/select2.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/icheck/icheck.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/vendors/css/forms/icheck/custom.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/css/plugins/forms/extended/form-extended.css">
    <link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo BASE_PATH; ?>/assets/cropper.css">
<style>
	
        /* Cropped image state styling */
        .upload-area.cropped-state {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border: 2px solid #667eea;
        }
 
        .upload-area.cropped-state:hover {
            background: linear-gradient(135deg, #f0f4ff 0%, #e0f0f6 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
 
        .modal-lg {
            width: 90%;
            max-width: 1200px;
        }
 
        .cropped-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 10px;
        }
 
        .success-message {
            color: #01A750;
            margin-top: 5px;
            font-weight: bold;
        }
 
        /* Make sure the cropper is visible in the modal */
        #cropperModal #cropperSection {
            display: block !important;
        }
		button{
        color: black !important;
		font-size:14px !important;
}

.upload-section {
    margin-bottom: 30px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 100%;
    /* height: 320px; */
}

  
/* Make upload and cropper section full width on smaller screens */
@media (max-width: 1024px) {
    .upload-section,
    .cropper-section {
        width: 100% !important;
    }

    .image-container {
        width: 100%;
        height: auto;
    }

    .preview-section {
        width: 100%;
    }
}

/* Stack image and preview vertically on tablets and below */
@media (max-width: 768px) {
    .cropper-section > div {
        flex-direction: column !important;
        align-items: center !important;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .image-container {
        width: 100% !important;
        height: auto !important;
    }

    .preview-section {
        width: 100%;
        margin-top: 20px;
        text-align: center;
    }

    .preview-section canvas {
        max-width: 100%;
        height: auto;
    }

    .upload-section {
        padding: 15px;
		        width: 95% !important;
    }
}
@media (max-width: 357px) {
form .form-actions.right {
    text-align: right;
    display: flex;
}
form .form-actions {
    border-top: 1px solid #d1d5ea;
    padding: 20px 0;
    margin-top: 20px;
    display: flex;
}
}
/* Smaller mobile adjustments */
@media (max-width: 576px) {
    .mode-buttons {
        flex-direction: column;
        width: 100%;
    }

    .mode-btn {
        width: 100%;
        text-align: center;
    }

    .action-btn {
        width: 100%;
    }

    .zoom-controls {
        /* flex-direction: column; */
        gap: 5px;
    }

    .zoom-slider {
        width: 100%;
    }

    .upload-content p {
        font-size: 1rem;
    }

    .upload-content .file-types {
        font-size: 0.8rem;
    }

    .controls {
        padding: 10px;
        gap: 15px;
    }

	button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled) {
    cursor: pointer;
    font-size: 14px  !important;
}
}
	/* Responsive wrapper */
.cropper-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

/* Make upload and cropper section full width on smaller screens */
@media (max-width: 1024px) {
    .upload-section,
    .cropper-section {
        width: 100% !important;
    }

    .image-container {
        width: 100%;
        height: auto;
    }

    .preview-section {
        width: 100%;
    }
}

/* Stack image and preview vertically on tablets and below */
@media (max-width: 768px) {
    .cropper-section > div {
        flex-direction: column !important;
        align-items: center !important;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .image-container {
        width: 100% !important;
        height: auto !important;
    }

    .preview-section {
        width: 100%;
        margin-top: 20px;
        text-align: center;
    }

    .preview-section canvas {
        max-width: 100%;
        height: auto;
    }

    .upload-section {
        padding: 15px;
    }
}
@media (max-width: 357px) {
form .form-actions.right {
    text-align: right;
    display: flex;
}
form .form-actions {
    border-top: 1px solid #d1d5ea;
    padding: 20px 0;
    margin-top: 20px;
    display: flex;
}
}
/* Smaller mobile adjustments */
@media (max-width: 576px) {
    .mode-buttons {
        flex-direction: column;
        width: 100%;
    }

    .mode-btn {
        width: 100%;
        text-align: center;
    }

    .action-btn {
        width: 100%;
    }

    .zoom-controls {
        /* flex-direction: column; */
        gap: 5px;
    }

    .zoom-slider {
        width: 100%;
    }

    .upload-content p {
        font-size: 1rem;
    }

    .upload-content .file-types {
        font-size: 0.8rem;
    }

    .controls {
        padding: 10px;
        gap: 15px;
    }

	button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled) {
    cursor: pointer;
    font-size: 14px  !important;
}
}

@media (max-width: 576px) {
    .upload-section, .cropper-section {
        /* width: 100% !important; */
        max-width: 100% !important;
    }
}

    </style>
    
<style>
	/* Critical fix for preview image auto-sizing issue */
	.cropper-section {
    width: 50%;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.previewImage {
    max-width: 100% !important;
    max-height: 400px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    display: block;
}

/* Fix image container overflow */
.image-container {
    position: relative;
    width: 100%;
    max-width: 500px; /* Prevent container from growing too large */
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden; /* Prevent image overflow */
    border: 2px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

/* Fix cropper section layout on mobile */
@media screen and (max-width: 768px) {
    .cropper-section > div {
        flex-direction: column !important;
        align-items: center !important;
    }
    
    .image-container {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
    }
    
    .previewImage {
        max-width: 100% !important;
        max-height: 300px !important; /* Smaller height on mobile */
    }
}

/* Additional mobile fixes for very small screens */
@media screen and (max-width: 576px) {
    .previewImage {
        max-height: 250px !important;
    }
    
    .image-container {
        min-height: 200px;
    }
}

@media screen and (max-width: 357px) {
    .previewImage {
        max-height: 200px !important;
    }
}

/* Ensure containers don't break layout */
.cropper-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden; /* Prevent horizontal scroll */
}
</style>
</head>

<body class="horizontal-layout horizontal-menu 2-columns  " data-open="hover" data-menu="horizontal-menu" data-col="2-columns">

    <!-- include header-->
    <?php include('includes/header.php'); ?>

    <!-- BEGIN: Content-->
    <div class="app-content content">
        <div class="content-overlay"></div>
        <div class="content-wrapper">
            <div class="content-header row">
                <div class="content-header-left col-md-6 col-12 mb-2">
                    <h3 class="content-header-title">Employee</h3>
                    <div class="row breadcrumbs-top">
                        <div class="breadcrumb-wrapper col-12">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="index.html">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item"><a href="employee.html">Employee</a>
                                </li>
                                <li class="breadcrumb-item active"><a href="#"><?php echo ($pageTitle); ?> Employee</a>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="content-header-right col-md-6 col-12">
                    <div class="text-right">
                        <!---button type="button" class="btn btn-outline-primary btn-min-width mr-1 mb-1">Add</button--->
                    </div>
                </div>
            </div>
            <div class="content-body">

                <!-- Basic form layout section start -->
                <section id="horizontal-form-layouts">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title" id="horz-layout-colored-controls">Employee</h4>
                                    <a class="heading-elements-toggle"><i class="la la-ellipsis-v font-medium-3"></i></a>
                                    <div class="heading-elements">
                                        <ul class="list-inline mb-0">
                                            <li><a data-action="reload"><i class="ft-rotate-cw"></i></a></li>
                                            <li><a data-action="expand"><i class="ft-maximize"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-content collpase show">
                                    <div class="card-body">
                                        <form class="form form-horizontal" id="employeeForm" method="post" action="addemployeesubmit.html" enctype="multipart/form-data">
                                            <input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($employeeId); ?>">
                                            <input type="hidden" name="businessId" id="businessId" value="<?php echo ($businessId); ?>">
                                            <input type="hidden" name="imageName" id="imageName" value="<?php echo ($imageName); ?>">
                                            <input type="hidden" name="usermanagemetId" id="usermanagemetId" value="<?php echo ($usermanagemetId); ?>">
                                            <div class="form-body">
                                                <h4 class="form-section"><i class="ft-user"></i>Personal Details</h4>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput4">First Name<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="firstName" class="form-control" placeholder="First Name" name="firstName" value="<?php echo ($firstName); ?>" required oninput="charactersOnly(this.id);">
										                        <span id="firstNameSpan" style="color: red; font-size: 12px;"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="lastName">Last Name<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="lastName" class="form-control" placeholder="Last Name" name="lastName" value="<?php echo ($lastName); ?>" required oninput="charactersOnly(this.id);">
																<span id="lastNameSpan" style="color: red; font-size: 12px;"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="empTitle">Title<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="empTitle" class="form-control" placeholder="Title" name="empTitle" value="<?php echo ($empTitle); ?>" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="empStartDate">Start Date<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <div class="input-group date-picker-class">
                                                                    <input type="text" class="form-control" id="empStartDate" name="empStartDate" value="<?php echo ($empStartDate); ?>">
                                                                    <div class="input-group-append">
                                                                        <span class="input-group-text" id="basic-addon2"><i class="la la-calendar"></i></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="address">Address 1</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="address" class="form-control" placeholder="Address 1" name="address" value="<?php echo ($address); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="address">Address 2</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="address2" class="form-control" placeholder="Address 2" name="address2" value="<?php echo ($address2); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                   
                                                </div>
                                                <div class="row">
                                                <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="city">City</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="city" class="form-control" placeholder="City" name="city" value="<?php echo ($city); ?>" oninput="charactersOnly(this.id);">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput3">Email<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="email" class="form-control" placeholder="Email" name="email" value="<?php echo ($email); ?>" required>
                                                                <span class="hide errorEmail text-danger" id="errorEmail" style="width: 100%;">Please enter valid email </span>

                                                            </div>

                                                        </div>
                                                    </div>
                                                  
                                                </div>
                                                <div class="row">
                                                <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="phoneNo">Mobile No.<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="mobileNo" class="form-control phone-inputmask" oninput="validatePhoneNumber(this);" placeholder="Mobile No." name="phoneNo" value="<?php echo ($phoneNo); ?>" required>
                                                                <span id="errorMobile" style="color: red;font-size: 13px;"></span>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="city">Zip Code</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="zipCode" class="form-control" maxlength="6" placeholder="Zip Code" name="zipCode" value="<?php echo ($zipCode); ?>" oninput="numberOnly(this.id);">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput3">Country<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <select id="cboCountry" name="cboCountry" class="select2 form-control step1" data-parsley-errors-container="#error-country" required>
                                                                    <option value="" selected>Select</option>
                                                                    <?php

                                                                    if ($countries != "") {
                                                                        while ($row = mysqli_fetch_assoc($countries)) {
                                                                            $location_id  = $row['location_id'];
                                                                            $name  = stripslashes($row['name']);

                                                                    ?>
                                                                            <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                                                    <?php

                                                                        }
                                                                    }
                                                                    ?>
                                                                </select>
                                                                <div id="error-country"></div>

                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput4">State<span class="validate-field">*</span></label>
                                                            <div class="col-md-9 mx-auto">
                                                                <select id="stateId" name="stateId" class="select2 form-control step2" data-parsley-errors-container="#error-stateId" required>
                                                                    <option value="" selected>Select</option>
                                                                </select>
                                                                <div id="error-stateId" style="width: 100%;"></div>

                                                            </div>

                                                        </div>
                                                    </div>
                                                        <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="empNotes">Note</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <input type="text" id="empNotes" class="form-control" placeholder="Note" name="empNotes" value="<?php echo ($empNotes); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                    <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control" for="userinput4">Is Independent</label>
                                                            <div class="col-md-9 mx-auto">
                                                                <fieldset>
                                                                    <input type="checkbox" id="input-5" name="isIndependent" id="isIndependent" <?php if ($isIndependent == 1) { ?> checked <?php } ?>>
                                                                </fieldset>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                </div>
                                                <h4 class="form-section"><i class="ft-mail"></i> Profile Image</h4>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group row">
                                                            <!-- <label class="col-md-3 label-control" >Image</label> -->
                                                            <!-- <div class="col-md-9 mx-auto">
                                                                <fieldset class="form-group">
                                                                    <div class="custom-file">
                                                                        <input type="file" class="custom-file-input" id="inputGroupFile02" name="employeeImage" onchange="browseImage(this);" accept=".png, .gif, .jpeg, .jpg" value="">
                                                                        <label class="custom-file-label" for="inputGroupFile02" aria-describedby="inputGroupFile02"><?php echo ($imageName); ?></label>
                                                                    </div>
                                                                </fieldset>
                                                            </div> -->

                                                             <div class="cropper-container cropper-instance-1">
                                                                <input type="hidden" name="imageName" id="imageName"  class="fileLogo" value="<?php echo ($imageName); ?>">
                                                                <input type="hidden" class="hasCroppedImage" value="0">


        <section class="upload-section" >
            <div class="upload-area">
                <input type="file" class="fileInput" accept="image/*" hidden>
                <div class="upload-content">
                    <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    <p>Click to browse or drag and drop an image</p>
                    <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                </div>
            </div>
        </section>

        <section class="cropper-section" style="display: none;">
            <div class="controls">
                <div class="control-group">
                    <div class="mode-buttons">
                        <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                        <button type="button" class="mode-btn" data-mode="square">Square</button>
                    </div>
                </div>
                <div class="control-group">
                    <button type="button" class="action-btn resetBtn">Reset</button>
                    <button type="button" class="action-btn backBtn">Back</button>
                    <button type="button" class="action-btn bgRemovelToggle" style="display: none;">Remove Background</button>
                    <button type="button" class="action-btn primary cropBtn" style="display: none;">Crop & Save</button>
                </div>
                <div class="control-group">
                    <label>Zoom:</label>
                    <div class="zoom-controls">
                        <button type="button" class="zoom-btn zoomOut">-</button>
                        <span class="zoom-level zoomLevel">100%</span>
                        <button type="button" class="zoom-btn zoomIn">+</button>
                        <input type="range" class="zoom-slider zoomSlider" min="20" max="300" value="100">
                    </div>
                </div>
            </div>
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div class="image-container">
                    <img class="previewImage" src="" alt="Preview">
                    <div class="crop-overlay">
                        <div class="crop-selection"></div>
                    </div>
                </div>
                <div class="preview-section">
                    <h4>Cropped Preview</h4>
                    <canvas class="previewCanvas" name="previewCanvas"></canvas>
                </div>
            </div>
        </section>
  

                                                    </div>
                                                
                                                    <div class="col-md-6">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-3 label-control"></label>
                                                            <div class="col-md-9 mx-auto">
                                                                
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-actions text-right">
                                                    <a href="employee.html">
                                                        <button type="button" class="btn btn-outline-warning btn-glow mr-1">
                                                            <i class="ft-x"></i> Cancel
                                                        </button>
                                                    </a>
                                                    <button type="submit" id="btnSave" class="btn btn-outline-primary btn-glow">
                                                        <i class="la la-check-square-o"></i> <?php echo ($buttonTitle); ?>
                                                    </button>
                                                </div>
                                        </form>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
    <!-- END: Content-->

    <!-- BEGIN: Footer-->
    <?php include('includes/footer.php'); ?>
    <?php include('includes/footerJs.php'); ?>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/custom.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/polyfill.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/select/select2.full.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/select/form-select2.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/icheck/icheck.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/checkbox-radio.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/vendors/js/forms/extended/inputmask/jquery.inputmask.bundle.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/scripts/forms/extended/form-inputmask.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/moment/moment.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/js/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo BASE_PATH; ?>/assets/cropper.js" type="text/javascript"></script>

	   <script>
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
        
		  const coverCropper = new ImageCropper('.cropper-instance-1');


    
            
            // Check if default school image exists and display it
            const defaultImagePath = '<?php echo $employeeImagePath; ?>';

            console.log("Default image path:", defaultImagePath);
            // console.log("profileImageFilePath image path:", profileImageFilePath);
            
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('.cropper-instance-1 .upload-area');
                const $uploadContent = $('.cropper-instance-1 .upload-content');
                
                console.log("Upload area element exists:", $uploadArea.length > 0);
                console.log("Upload content element exists:", $uploadContent.length > 0);
                
                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
                
                // Create the image element first to check if it loads
                const imgElement = document.createElement('img');
                imgElement.onload = function() {
                    console.log("Image loaded successfully");
                };
                imgElement.onerror = function() {
                    console.error("Failed to load image from path:", defaultImagePath);
                };
                imgElement.src = defaultImagePath + "?randId=" + new Date().getTime();
                imgElement.alt = "School Logo Preview";
                imgElement.style = "max-width: 150px; max-height: 150px; border-radius: 8px; border: 2px solid #667eea; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-top: 10px !important;";
                
                // Update the content with the existing image
                $uploadContent.empty().append(
                    $('<div>').css({
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '10px'
                    }).append(
                        imgElement,
                        $('<div>').css({
                            textAlign: 'center'
                        }).append(
                            $('<p>').css({
                                color: '#666EE8',
                                fontWeight: 'bold',
                                margin: '5px 0',
                                fontSize: '14px'
                            }).text('✓ Current image'),
                            $('<span>').addClass('file-types').css({
                                color: '#666',
                                fontSize: '12px'
                            }).text('Click to select a different image if needed')
                        )
                    )
                );
            } 
			
        });
  
  </script>
    <script>
        function validatePhoneNumber(input) {
    const phoneNumber = input.value.replace(/\D/g, ''); // Remove non-numeric characters
    if (phoneNumber.length < 10) {
        document.getElementById('errorMobile').textContent = 'Mobile number must have at least 10 digits';
        $('#btnSave').attr('disabled','disabled');
    } else {
        document.getElementById('errorMobile').textContent = '';
        $('#btnSave').removeAttr('disabled');

    }
}
        //form validation
        $('#employeeForm').parsley().on('field:validated', function() {
                var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                $("#btnSave").prop("disabled", true);
                return true; // Don't submit form for this demo
            });

        //***** DATE-TIME PICKER WITH CAL-ICON ******//
        $(function() {
            $('.date-picker-class').datetimepicker({
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-check',
                    clear: 'fa fa-trash',
                    close: 'fa fa-times'
                },
                allowInputToggle: true
            });
        });

        //number validation 
        $('#mobileNo').keypress(function(event) {
            var keycode = event.which;
            if (!(event.shiftKey == false && (keycode == 46 || keycode == 8 || keycode == 37 || keycode == 39 || (keycode >= 48 && keycode <= 57)))) {
                event.preventDefault();
            }
        });

        // character only validation
		function charactersOnly(id) {
			var element = document.getElementById(id);

			// Check if element exists
			if (!element) {
				console.error('Element with id "' + id + '" not found');
				return;
			}

			// Check for URL patterns first - more specific pattern to avoid false positives
			var urlPattern = /https?:\/\/[^\s]+|www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\/[^\s]*/gi;
			if (urlPattern.test(element.value)) {
				element.value = "";

				// Show error message for the specific field
				var errorSpanId = id + 'Span';
				var errorSpan = $('#' + errorSpanId);
		
				// Check if error span exists before trying to show message
				if (errorSpan.length > 0) {
					errorSpan.fadeIn().html("URLs are not allowed in this field.");
					setTimeout(function() {
						errorSpan.fadeOut('slow');
					}, 5000);
				}

				return;
			}

			// Remove non-alphabetical characters (allow spaces for names)
			element.value = element.value.replace(/[^a-zA-Z\s]/gi, "");
		}

        // number only validation
        function numberOnly(id) {
            var element = document.getElementById(id);
            element.value = element.value.replace(/[^0-9]/gi, "");
        }

        
        //country dropdown code
        $('#employeeForm').cascadingDropdown({
            selectBoxes: [{
                    selector: '.step1',
                    selected: '<?php echo ($dbCountryId); ?>'
                },
                {
                    selector: '.step2',
                    selected: '<?php echo ($stateId); ?>',
                    requires: ['.step1'],
                    requireAll: true,
                    source: function(request, response) {

                        $.getJSON('../ajax/getStates.html', request, function(data) {
                            response($.map(data, function(item, index) {
                                return {
                                    label: item['StateName'],
                                    value: item['StateId']
                                };
                            }));
                        });
                    }
                }
            ]
        });
        <?php
        if ($dbCountryId == 0) {
        ?>
            $('#cboCountry').val('224').trigger('change');
        <?php
        }
        ?>

        //check email already exist
        $("#email").change(function() {
            var email = $("#email").val();
            var businessId = $("#businessId").val();

            var pattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

            if (pattern.test(email)) {
                $.ajax({
                    type: "GET",
                    url: "../ajax/ajax_check_duplicate_email.html",
                    data: {
                        email: email,
                        businessId: businessId
                    },
                    success: function(data) {
                        if (data > 0) {
                            Swal.fire({
                                type: "danger",
                                title: 'Email already exist!',
                                confirmButtonClass: 'btn btn-danger',
                            })
                            $("#email").val('').focus();
                            $("#btnSave").removeAttr("disabled", true);
                        }
                    }
                });
            } else {
                $(".errorEmail").removeClass('hide');
                setTimeout(function() {
                    $('.errorEmail').addClass('hide');

                }, 3000);

                $("#email").val('');
                $("#email").focus();

            }
        });
    </script>

</body>
<!-- END: Body-->

</html>