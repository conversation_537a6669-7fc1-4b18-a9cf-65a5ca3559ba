<?php
class clsCustomer
{	
	var $businessId =0;
	var $customerId  = 0;
	var $firstName  = '';
	var $lastName  = '';
	var $phoneNo  = '';
	var $address  = '';
	var $address2  = '';
	var $city  = '';
	var $zipCode  = '';
	var $stateId  = 0; 
	var $email  = '';
	var $shareType='';
	var $employeeId=0;
	var $affiliateId=0;
	var $refId =0;

	var $businessaddress = '';
	var $businessaddress2 = '';
	var $businesscity = '';
	var $stateName = '';
	var $businesszipCode = '';
	var $websiteUrl = '';
	var $businessname = '';
	var $businessNotes = '';
	var $socialMediaUrl = '';
	var $customerBirthDay = '';
	var $customerAnniversary = '';
	var $isAddToCalendar = '';
	var $isSentNotification = '';
	var $isShownCalendar = '';
	
	function SaveCustomer($customerId)
	{	 
		$objDB = new clsDB();
		$sql = '';	
		if($customerId > 0)
		{
			$sql = "UPDATE customer SET 																										
							firstName = '".addslashes($this->firstName)."',																
							lastName = '".addslashes($this->lastName)."',																
							phoneNo = '".addslashes($this->phoneNo)."',																
							address = '".addslashes($this->address)."',																
							address2 = '".addslashes($this->address2)."',																
							city = '".addslashes($this->city)."',																
							zipCode = '".addslashes($this->zipCode)."',																
							stateId = '".addslashes($this->stateId)."',
							email = '".addslashes($this->email)."'
							WHERE customerId= ".$customerId;	
			// echo $sql;exit;											
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
			$sql = "INSERT INTO customer(firstName,lastName,email,phoneNo,address,address2,city,zipCode,stateId) 
								VALUES(								  												
									  '".addslashes($this->firstName)."',								
									  '".addslashes($this->lastName)."',								
									  '".addslashes($this->email)."',								
									  '".addslashes($this->phoneNo)."',								
									  '".addslashes($this->address)."',								
									  '".addslashes($this->address2)."',								
									  '".addslashes($this->city)."',								
									  '".addslashes($this->zipCode)."',
									  '".addslashes($this->stateId)."'		
									  )";	 
            // echo $sql;exit;    									  
			$customerId = $objDB->ExecuteInsertQuery($sql);
		}		
		unset($objDB);
		return $customerId;
	}

	function SaveCustomerBusinessDetails($customerId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		
			$sql = "UPDATE customer SET 													
							businessaddress = '".addslashes($this->businessaddress)."',																
							businessaddress2 = '".addslashes($this->businessaddress2)."',																
							businesscity = '".addslashes($this->businesscity)."',																
							stateName = '".addslashes($this->stateName)."',																
							businesszipCode = '".addslashes($this->businesszipCode)."',																
							websiteUrl = '".addslashes($this->websiteUrl)."',																
							businessname = '".addslashes($this->businessname)."',																
							businessNotes = '".addslashes($this->businessNotes)."',
							socialMediaUrl = '".addslashes($this->socialMediaUrl)."',
							customerBirthDay = '".addslashes($this->customerBirthDay)."',
							customerAnniversary = '".addslashes($this->customerAnniversary)."',
							isAddToCalendar = '".addslashes($this->isAddToCalendar)."',
							isSentNotification = '".addslashes($this->isSentNotification)."',
							isShownCalendar = '".addslashes($this->isShownCalendar)."'
							WHERE customerId= ".$customerId;	
			// echo $sql;exit;											
		$objDB->ExecuteQuery($sql);
		unset($objDB);
		return $customerId;
	}

	function GetCustomerDetailsByCustomerId($customerId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer WHERE customerId = '".$customerId."'";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;

	}
	
	function GetCustomerDetails($customerId,$businessId=0)  
	{
		$rows = "";
		$objDB = new clsDB();
		// $sql = "SELECT * FROM customer WHERE customerId = '".$customerId."'";
		$sql = "SELECT customer.*,businesscustomer.shareType,businesscustomer.affiliateId,businesscustomer.employeeId,businesscustomer.businessId FROM customer 
		LEFT JOIN businesscustomer on customer.customerId=businesscustomer.customerId
		WHERE customer.customerId='".$customerId."'";
		if ($businessId != 0) {

			$sql.="AND businesscustomer.businessId='".$businessId."'";

		}
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetAffiliateCustomerDetails($customerId,$businessId)  
	{
		$rows = "";
		$objDB = new clsDB();
		// $sql = "SELECT * FROM customer WHERE customerId = '".$customerId."'";
		$sql = "SELECT customer.*,businesscustomer.shareType,businesscustomer.affiliateId FROM customer 
		INNER JOIN businesscustomer on customer.customerId=businesscustomer.customerId
		WHERE customer.customerId='".$customerId."' AND businesscustomer.businessId='".$businessId."'";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function DeleteCustomer($customerId)
	{		
		$result="";
		if($customerId > 0)
		{			
			$objDB = new clsDB();
			$sql = "DELETE customer.*,businesscustomer.*,businessgroupdetails.* FROM customer 
			        LEFT JOIN businesscustomer ON customer.customerId = businesscustomer.customerId
			        LEFT JOIN businessgroupdetails ON customer.customerId = businessgroupdetails.customerId
			        WHERE customer.customerId='".$customerId."' AND businesscustomer.customerId='".$customerId."'";
        			// echo $sql;exit;			
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	} 
	
	function GetAllCustomers($status = -1)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer";	 
		if($status != -1)
		{
		   $sql .=" WHERE status='".$status."'";
		}       
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function SetCustomerStatus($id, $status)
	{
		if($id > 0)
		{
			$objDB = new clsDB();
			$sql = "Update customer set status =".$status." Where customerId = ".$id; 
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	
	function SetCustomerFavoriteStatus($id, $isFavorite)
	{
		if($id > 0)
		{
			$objDB = new clsDB();
			$sql = "Update customer set isFavorite =".$isFavorite." Where customerId = ".$id; 
			// echo $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}
	
	function CheckCustomerLogin($email)
	{
		$customerId = "0";
		$objDB = new clsDB();
		$sql = "SELECT customerId FROM customer WHERE email='".$email."'";
		$customerId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $customerId;
	}

	function checkCustomerAsAffiliate($email)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM affiliate WHERE email='".$email."'";
		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetCustomerBusinessList($customerId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.*,customer.customerId,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE businesscustomer.customerId='".$customerId."' GROUP BY businesscustomer.businessId ORDER BY businessName ASC";
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows; 
	}
	
	function SaveBusinessCustomer($customerId)
	{	
		$objDB = new clsDB();
		$sql = '';	
		if($customerId > 0)
		{
			$sql = "UPDATE businesscustomer SET 													
							customerId = '".addslashes($this->customerId)."',																
							businessId = '".addslashes($this->businessId)."',																							
							shareType = '".addslashes($this->shareType)."',
							employeeId = '".addslashes($this->employeeId)."'
							WHERE customerId= ".$customerId;	
			// echo $sql;exit;											
			$objDB->ExecuteQuery($sql);		 					
		}
		else
		{
			$sql = "INSERT INTO businesscustomer(customerId,businessId,shareType,employeeId,affiliateId,refId) 
								VALUES(								  				
									  '".addslashes($this->customerId)."',								
									  '".addslashes($this->businessId)."',								
									  '".addslashes($this->shareType)."',				
									  '".addslashes($this->employeeId)."',				
									  '".addslashes($this->affiliateId)."',				
									  '".addslashes($this->refId)."'				
									  )";		
									//    echo $sql;exit;
			$customerId = $objDB->ExecuteInsertQuery($sql);
		}
		
		unset($objDB);
		return $customerId;
	}
	
	function CheckCustomerMobileAndEmailExist($email,$phoneNo) 
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer 
		        INNER JOIN businesscustomer ON customer.customerId = businesscustomer.customerId";
		       
		if($phoneNo!='')
		{
			$sql .= " WHERE phoneNo='".$phoneNo."'";
		}			
		if($email!='')
		{
			$sql .= " WHERE email='".$email."'";
		}		
		$sql .= " LIMIT 1";		
		// echo $sql;exit;		
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	
	function CheckCustomerMobileNumberExist($phoneNo) 
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer 
		        WHERE phoneNo='".$phoneNo."'";
		// echo $sql;exit;		
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	
	function CheckAuthenticationCode($authCode,$customerId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer WHERE authCode='".$authCode."' AND customerId='".$customerId."'";
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}	
	
	function CheckCustomerMobileNumberRegisterOrNot($customerId,$businessId,$phoneNo) 
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT customer.*,businesscustomer.*
				FROM businesscustomer 
				LEFT JOIN customer ON customer.customerId = businesscustomer.customerId 
				WHERE businesscustomer.businessId='".$businessId."' AND customer.customerId='".$customerId."' AND phoneNo='".$phoneNo."'";
		// echo $sql;exit;		
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	
	function CheckContactMobileOrEmailExist($email,$phoneNo='') 
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT customerId FROM customer";
		       
		if($phoneNo!='' && $email=='')
		{
			$sql .= " WHERE phoneNo='".$phoneNo."'";
		}			
		if($email!='' && $phoneNo=='')
		{
			$sql .= " WHERE email='".$email."'";
		}
        if($email!='' && $phoneNo!='')
		{
			$sql .= " WHERE email='".$email."'  OR phoneNo='".$phoneNo."'";
		}

		// if($businessId > 0)
		// {
		// 	$sql.="AND businessId =$businessId";
		// }
		// echo $sql;exit;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $row;
	}

    function CheckCustomerExistForBusiness($customerId,$businessId) 
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT customerId FROM businesscustomer WHERE customerId='".$customerId."' AND businessId='".$businessId."'";
		// echo $sql;exit;		
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB); 
		return $row;
	}	

	function CheckCustomerExist($email,$businessId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.customerId FROM `customer` left join businesscustomer on customer.customerId=businesscustomer.customerId and businesscustomer.businessId=$businessId WHERE email='$email'  AND status=1";
		// $sql = "SELECT customerId from customer where email='$email'";
		// echo $sql;exit;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB); 
		return $row;
	}

	// created date : 21-08-23
	function CheckCustomerExistProbizca($email,$businessId,$customerId)
	{
		$row = "0";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.customerId FROM `customer` left join businesscustomer on customer.customerId=businesscustomer.customerId and businesscustomer.businessId=$businessId WHERE email='$email' AND customer.customerId =$customerId   AND status=1";
		// $sql = "SELECT customerId from customer where email='$email'";
		// echo $sql;exit;
		$row = $objDB->GetSingleFieldValue($sql);
		unset($objDB); 
		return $row;
	}
	
	
	function GetAllBusinessContactList($businessId) 
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT
					customer.email,customer.phoneNo,customer.address,customer.address2,customer.city,customer.zipCode,customer.stateId,
					customer.status,customer.firstName AS custFirstName,customer.lastName AS custLastName,customer.customerId,
					businesscustomer.*
					FROM businesscustomer 
					INNER JOIN customer ON customer.customerId = businesscustomer.customerId ";
					
        
					if($businessId > 0)
					{
						$sql .= " WHERE businesscustomer.businessId='".$businessId."'";
					}
		$sql .= " GROUP BY businesscustomer.customerId";
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetBusinessContactDetails($businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.businessId,business.businessName,employee.employeeId,employee.firstName AS empFirstName,
					employee.lastName AS empLastName
					FROM business 					
					INNER JOIN employee ON employee.businessId = business.businessId
					WHERE business.businessId='".$businessId."'"; 
      
		// $sql .= " GROUP BY customer.customerId";
		// echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	function GetContacBusinesstList($customerId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT
					business.businessName,business.businessId
					FROM business
					INNER JOIN businesscustomer ON businesscustomer.businessId = business.businessId
					WHERE businesscustomer.customerId='".$customerId."'";
		// $sql .= " GROUP BY businesscustomer.customerId";
		// echo $sql;exit;		
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	

	
	
	function CheckIsCustomerMobileNumberExist($phoneNo,$customerId) 
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer 
		        WHERE phoneNo='".$phoneNo."' AND customerId='".$customerId."'";
		// echo $sql;exit;		
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	function CheckDuplicateCustomerEmail($email)
	{
		$customerId = 0;
		$objDB = new clsDB();
		$sql = "SELECT customerId  FROM customer WHERE email = '".($email)."'";
		// echo $sql;
		$dataRow = $objDB->GetDataRow($sql);
		if($dataRow)
		{
			$customerId  = $dataRow['customerId'];
		}
		unset($objDB);
		return $customerId ;
	}
	function GetBusinessCustomerCount($businessId)
	{
		$dataRow = "";
		$objDB = new clsDB();
		$sql = "SELECT count(*) as totalContacts FROM businesscustomer WHERE businessId = '".$businessId."'";
		// echo $sql;exit;
		$dataRow = $objDB->GetDataRow($sql);
		if($dataRow)
		{
			$totalContacts  = $dataRow['totalContacts'];
		}
		unset($objDB);
		return $totalContacts;
	}
	function GetCustomerBusinessDeatilByBusiness($customerId,$businessId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT businesscustomer.*
				FROM businesscustomer 
				WHERE customerId='".$customerId."' AND businessId='".$businessId."'";
		// echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	
	
	 function GetCustomerBusinessDeatils($customerId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.*,employee.*,customer.customerId,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				INNER JOIN employee ON employee.businessId = business.businessId 
				WHERE businesscustomer.customerId='".$customerId."' AND employee.isPrimary='1'";
		// echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	} 
	
	/* function GetCustomerBusinessDeatils($customerId)
	{
		$rows = "";
		$objDB = new clsDB();
		$sql = "SELECT business.*,customer.customerId,businesscustomer.*
				FROM businesscustomer 
				INNER JOIN customer ON customer.customerId = businesscustomer.customerId 
				INNER JOIN business ON business.businessId = businesscustomer.businessId 
				WHERE businesscustomer.customerId='".$customerId."' GROUP BY businesscustomer.businessId ORDER BY businessName ASC LIMIT 1";
		// echo $sql;exit;		
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	} */
	function CheckIsCustomerMobileNumberExistOrNot($phoneNo) 
	{
		$row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM customer 
		        WHERE phoneNo='".$phoneNo."'";
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}
	
	function GetBusinessContactId($customerId,$businessId)
	{
		$result='';
		$objDB = new clsDB();
		$sql = "Select businessCustomerId from businesscustomer  Where customerId = '".$customerId."' AND businessId = '".$businessId."'";
		// echo $sql;exit;
		$result = $objDB->GetSingleFieldValue($sql);
		unset($objDB);		
		return $result;
	}
	function CheckDuplicateBusinessCustomer($customerId,$businessId)
	{
		$businessCustomerId = 0;
		$objDB = new clsDB();
		$sql = "SELECT businessCustomerId  FROM businesscustomer WHERE businessId = '".($businessId)."' AND customerId = '".($customerId)."'";
		// echo $sql;exit;
		$dataRow = $objDB->GetDataRow($sql);
		if($dataRow) 
		{
			$businessCustomerId  = $dataRow['businessCustomerId'];
		}
		unset($objDB);
		return $businessCustomerId ;
	}

	function GetMarketplaceCounts($industeryId)
	{
		$rows = "";
		$objDB = new clsDB();
        $sql = "SELECT * FROM `business` WHERE `industeryId`='".$industeryId."' ";
		
		
		$rows = $objDB->GetResultset($sql);
		// echo $sql;exit;
		unset($objDB);
		return $rows;
	}
}
?>