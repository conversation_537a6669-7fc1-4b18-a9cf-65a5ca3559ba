<?php
	// Updated on 16-01-2023
	include('includes/validatesystemuserlogin.php'); 
    include('../includes/config.php'); 
	include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsPlanMaster.php'); 
    include('../class/clsBusinessPlanMaster.php'); 
	include('../class/clsAddonMaster.php');
	include('../class/clsBusinessProBizCaForms.php');
	include('../class/clsBusinessProBizCa.php');
	include('../class/clsChatApp.php');


  
	if(isset($_POST['btnSave']))
	{
	//    
		$objBusinessPlanMaster = new clsBusinessPlanMaster();

		$startDate  = isset($_POST['startDate']) ? trim($_POST['startDate']) : "";
		$endDate  = isset($_POST['endDate']) ? trim($_POST['endDate']) : "";
		$paymentPlan  = isset($_POST['paymentPlan']) ? trim($_POST['paymentPlan']) : 0;
		$appliedPlanAmt  = isset($_POST['appliedPlanAmt']) ? trim($_POST['appliedPlanAmt']) : 0;
		
		$smsLimit  = isset($_POST['smsLimit']) ? trim($_POST['smsLimit']) : 0;
		$emailLimit  = isset($_POST['emailLimit']) ? trim($_POST['emailLimit']) : 0;

		$customQrFormchk = isset($_POST['customQrFormchk'])?1:0;
      
		//Get All Inputs	
		$objBusinessPlanMaster->price = $_POST['price'];
		$objBusinessPlanMaster->title = $_POST['title'];
		$objBusinessPlanMaster->contactLimit = $_POST['contactLimit'];
		$objBusinessPlanMaster->cardLimit = $_POST['cardLimit'];
		$objBusinessPlanMaster->subCardLimit = $_POST['subCardLimit'];
		$objBusinessPlanMaster->allowThemeEditing = isset($_POST['allowThemeEditing'])?1:0;

		$objBusinessPlanMaster->imageInfographSectionLimit = $_POST['imageInfographSectionLimit'];
		$objBusinessPlanMaster->videoInfographSectionLimit = $_POST['videoInfographSectionLimit'];
		$objBusinessPlanMaster->imageCarouselLimit = $_POST['imageCarouselLimit'];
		$objBusinessPlanMaster->imageInfographLimit = $_POST['imageInfographLimit'];

		$objBusinessPlanMaster->videoInfographLimit = $_POST['videoInfographLimit'];
		$objBusinessPlanMaster->formLimit = $_POST['formLimit'];

		$objBusinessPlanMaster->allowLogoInclusion = isset($_POST['allowLogoInclusion'])?1:0;
		$objBusinessPlanMaster->allowPhotoInclusion = isset($_POST['allowPhotoInclusion'])?1:0;
		$objBusinessPlanMaster->allowVideoProfile = isset($_POST['allowVideoProfile'])?1:0;
		$objBusinessPlanMaster->isDedicatedURL = isset($_POST['isDedicatedURL'])?1:0;
		$objBusinessPlanMaster->isCrmIntergration = isset($_POST['isCrmIntergration'])?1:0;
		$objBusinessPlanMaster->isCouponServices = isset($_POST['isCouponServices'])?1:0;
		$objBusinessPlanMaster->isWhiteLabel = isset($_POST['isWhiteLabel'])?1:0;
		$objBusinessPlanMaster->isPDFSubscription = isset($_POST['isPDFSubscription'])?1:0;
		$objBusinessPlanMaster->chatRoom = isset($_POST['chatRoom'])?1:0;
		$objBusinessPlanMaster->shoppingCart = isset($_POST['shoppingCart'])?1:0;
		$objBusinessPlanMaster->pushNotification = isset($_POST['pushNotification'])?1:0;
		
		$objBusinessPlanMaster->email = isset($_POST['email'])?1:0;
		$objBusinessPlanMaster->smschk = isset($_POST['smschk'])?1:0;
		$objBusinessPlanMaster->surveychk = isset($_POST['surveychk'])?1:0;
		$objBusinessPlanMaster->customQrFormchk = $customQrFormchk;
		$objBusinessPlanMaster->geolocation = isset($_POST['geolocation'])?1:0;
		$objBusinessPlanMaster->socialfeed = isset($_POST['socialfeed'])?1:0;
		$objBusinessPlanMaster->statistics = isset($_POST['statistics'])?1:0;
		$objBusinessPlanMaster->support = isset($_POST['support'])?1:0;
		$objBusinessPlanMaster->todo = isset($_POST['todo'])?1:0;
		$objBusinessPlanMaster->probizcaplus = isset($_POST['probizcaplus'])?1:0;
		$objBusinessPlanMaster->invoice = isset($_POST['invoice'])?1:0;
		
		//new code
		$objBusinessPlanMaster->infoContentLimit = $_POST['infoContentLimit'];
		$objBusinessPlanMaster->infoSectionLiimit = $_POST['infoSectionLiimit'];
		$objBusinessPlanMaster->galleryImageLimit = $_POST['galleryImageLimit'];
		
		$objBusinessPlanMaster->annualAmount = $_POST['annualAmount'];
		$objBusinessPlanMaster->semiAnnualAmount = $_POST['semiAnnualAmount'];
		$objBusinessPlanMaster->quarterlyAmount = $_POST['quarterlyAmount'];
		$objBusinessPlanMaster->appliedPlanAmt = $appliedPlanAmt;

		// Subcription Details
		$objBusinessPlanMaster->subscriptionStartDate =(date("Y-m-d H:i:s",strtotime($startDate))); 
		$objBusinessPlanMaster->subscriptionEndDate = (date("Y-m-d H:i:s",strtotime($endDate)));
		$objBusinessPlanMaster->paymentPlan = $paymentPlan;

		$type = $_POST['type'];
		
		$objBusinessPlanMaster->businessId = $_POST['businessIds'];
	    $businessId = $_POST['businessIds'];
		
		//update into business table				
		$objDB = new clsDB();
		$objDB->UpdateSingleColumnValueToTable('business','planId',$_POST['planId'],'businessId',$businessId);
		unset($objDB);	
		
		$objBusinessPlanMaster->planId = $_POST['planId'];
		$detailId = $_POST['detailId'];
		$retplanId = $objBusinessPlanMaster->SaveBusinessPlanMaster($detailId);
		if($retplanId > 0)
		{

			$objChatApp = new clsChatApp();
            $chatEnabled = (isset($_POST['chatRoom'])) ? true : false;

            $data = [
                'parent_id' => $businessId,
                'chatEnabled' => $chatEnabled
            ];
            $postData = json_encode($data);
            $response = $objChatApp->UpdatechatSetting($postData);

			//Update Type
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('businessplansubscriptiondetails','type',$type,'businessPlanDetailId',$retplanId);	
			$dfCurrentMonth = date('m');
			$objAddonMaster = new clsAddonMaster();
			$retaddondtlId = $objAddonMaster->UpdateAddondtls($dfCurrentMonth,$emailLimit,$businessId, 1);
			$retaddondtlId = $objAddonMaster->UpdateAddondtls($dfCurrentMonth,$smsLimit,$businessId, 2);

			if($customQrFormchk)
			{
				$objBusinessProBizCa = new clsBusinessProBizCa();	

				$businessFormId=$objBusinessProBizCa->checkBusinessQrForms($businessId);
				if(!$businessFormId)
				{

					// echo "hello";exit;
					//save and update data
					$objBusinessProBizCa->formTitle = 'Custom QR Form';		
					$objBusinessProBizCa->formBackgroundColor = '#ffffff';
					$objBusinessProBizCa->formTextColor = '#000000';
					$objBusinessProBizCa->formButtonColor = '#ffffff';
					$objBusinessProBizCa->businessId = $businessId;
					$objBusinessProBizCa->formSortOrder = '1';
					$objBusinessProBizCa->formRecipientsEmail = '';
					$retbusinessFormId = $objBusinessProBizCa->SaveProBizCaFormInformation(0); 

					$objDB->UpdateSingleColumnValueToTable('businessform', 'isdefaultqr',1,'businessFormId',$retbusinessFormId);				

					unset($objBusinessProBizCa);

					if($retbusinessFormId > 0 )
					{
						$objBusinessProBizCaForms = new clsBusinessProBizCaForms();	
						$objBusinessProBizCaForms->sectionTitle = 'QR Form';		
						$objBusinessProBizCaForms->sortOrder = 1;		
						$objBusinessProBizCaForms->businessFormId = $retbusinessFormId;		
						$retInfographicSectionIdId = $objBusinessProBizCaForms->SaveSection(0);
						$objDB->UpdateSingleColumnValueToTable('businessformsections', 'isPrimaryForm',1,'businessFormSectionId',$retInfographicSectionIdId);				
					}

				}
			
			}
			
			unset($objDB);

		}
		if($retplanId>0)
		{
			header('location:viewbisunesses.html?status=updated');
			exit();	
		}
		else
		{
			header('location:viewbisunesses.html?status=error');
			exit();	
		}

	}
	else if(isset($_POST['planId']))
	{
		$planId = $_POST['planId'];
		$detailId = $_POST['detailId'];
		$businessId = $_POST['businessId'];
		
		//get plan details
		$objPlanMaster = new clsPlanMaster();
		$objBusinessPlanMaster = new clsBusinessPlanMaster();
		$row = $objPlanMaster->GetPlanDetails($planId);
		unset($objPlanMaster);
			
		$objBusinessPlanMaster->price = $row['price'];
		$objBusinessPlanMaster->title = $row['title'];
		$objBusinessPlanMaster->contactLimit = $row['contactLimit'];
		$objBusinessPlanMaster->cardLimit = $row['cardLimit'];
		$objBusinessPlanMaster->subCardLimit = $row['subCardLimit'];
		$objBusinessPlanMaster->allowThemeEditing = ($row['allowThemeEditing']);
		$objBusinessPlanMaster->imageInfographSectionLimit = $row['imageInfographSectionLimit'];
		$objBusinessPlanMaster->videoInfographSectionLimit = $row['videoInfographSectionLimit'];
		$objBusinessPlanMaster->imageCarouselLimit = $row['imageCarouselLimit'];
		$objBusinessPlanMaster->imageInfographLimit = $row['imageInfographLimit'];
		$objBusinessPlanMaster->videoInfographLimit = $row['videoInfographLimit'];
		$objBusinessPlanMaster->formLimit = $row['formLimit'];
		$objBusinessPlanMaster->allowLogoInclusion = ($row['allowLogoInclusion']);
		$objBusinessPlanMaster->allowPhotoInclusion = ($row['allowPhotoInclusion']);
		$objBusinessPlanMaster->allowVideoProfile = ($row['allowVideoProfile']);
		$objBusinessPlanMaster->isDedicatedURL = ($row['isDedicatedURL']);
		$objBusinessPlanMaster->isCrmIntergration = ($row['isCrmIntergration']);
		$objBusinessPlanMaster->isCouponServices = ($row['isCouponServices']);
		$objBusinessPlanMaster->isWhiteLabel = ($row['isWhiteLabel']);
		$objBusinessPlanMaster->isPDFSubscription = ($row['isPDFSubscription']);
		$objBusinessPlanMaster->chatRoom = ($row['chatRoom']);
		$objBusinessPlanMaster->shoppingCart = ($row['shoppingCart']);
		$objBusinessPlanMaster->pushNotification = ($row['pushNotification']);
		$objBusinessPlanMaster->email = ($row['email']);
		$objBusinessPlanMaster->geolocation = ($row['geolocation']);
		$objBusinessPlanMaster->socialfeed = ($row['socialfeed']);
		$objBusinessPlanMaster->statistics = ($row['statistics']);
		$objBusinessPlanMaster->support = ($row['support']);
		$objBusinessPlanMaster->todo = ($row['todo']);
		$objBusinessPlanMaster->probizcaplus = ($row['probizcaplus']);
		$objBusinessPlanMaster->invoice = ($row['invoice']);
		
		
		//new code
		$objBusinessPlanMaster->infoContentLimit = $row['infoContentLimit'];
		$objBusinessPlanMaster->infoSectionLiimit = $row['infoSectionLiimit'];
		$objBusinessPlanMaster->galleryImageLimit = $row['galleryImageLimit'];
		
		$objBusinessPlanMaster->annualAmount = $row['annualAmount'];
		$objBusinessPlanMaster->semiAnnualAmount = $row['semiAnnualAmount'];
		$objBusinessPlanMaster->quarterlyAmount = $row['quarterlyAmount'];
		
		$objBusinessPlanMaster->businessId = $businessId;
		
		//update into business table				
		$objDB = new clsDB();
		$objDB->UpdateSingleColumnValueToTable('business','planId',$planId,'businessId',$businessId);
		unset($objDB);	
		
		$objBusinessPlanMaster->planId = $planId;
		$retplanId = $objBusinessPlanMaster->SaveBusinessPlanMaster($detailId);
		if($retplanId>0)
		{
			header('location:viewbisunesses.html?status=updated');
			exit();	
		}
		else
		{
			header('location:viewbisunesses.html?status=updated');
			exit();
		}
	}
	else
	{
		header('location:viewbisunesses.html?status=updated');
		exit();	
	}
	
?>