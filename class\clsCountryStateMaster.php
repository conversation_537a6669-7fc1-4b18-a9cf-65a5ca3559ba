<?php

class clsCountryStateMaster
{
	
	
	function GetAllCountry()
	{
		$returnRows = "";
		$objDB = new clsDB();
		 $sql = "SELECT countryStateMasterId as location_id,name,location_type,parent_id,is_visible,Code
		FROM countrystatemaster WHERE location_type=0 order by name";
		$returnRows = $objDB->GetResultset($sql);
		unset($objDB);
		return $returnRows;
	}
	
	function GetAllStates($selectedCountryId)
	{
		$returnRows = "";
		$objDB = new clsDB();
		$sql = "select * from countrystatemaster where location_type=1 and parent_id=".$selectedCountryId." order by name";
		$returnRows = $objDB->GetResultset($sql);
		unset($objDB);
		return $returnRows;
	}
	
	function GetAllCity($selectedStateId)
	{
		$returnRows = "";
		$objDB = new clsDB();
		$sql = "select * from countrystatemaster where location_type=2 and parent_id=".$selectedStateId." order by name";
		$returnRows = $objDB->GetResultset($sql);
		unset($objDB);
		return $returnRows;
	}

	function GetLocationCode($LocationId)
	{
		$currentCode = "";
		$objDB = new clsDB();
		 $sql = "SELECT code FROM countrystatemaster Where countryStateMasterId= '".$LocationId."'";
		$currentCode = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $currentCode;
	}
	
	function GetLocationName($LocationId)
	{
		$currentName = "";
		$objDB = new clsDB();
		 $sql = "SELECT name FROM countrystatemaster Where countryStateMasterId= '".$LocationId."'";
		$currentName = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $currentName;
	}	

	function GetParentIdFromChildId($countryStateMasterId)
	{
		$parent_id  = "";
		$objDB = new clsDB();
		$sql = "SELECT parent_id  FROM countrystatemaster Where countryStateMasterId= '".$countryStateMasterId."'";
		$parent_id  = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $parent_id ;
	}

	function GetStateIdByStateName($stateName)
	{
		$StateId  = "";
		$objDB = new clsDB();
		$sql = "SELECT countryStateMasterId FROM `countrystatemaster` WHERE name LIKE '$stateName'";
		// echo $sql;
		$StateId  = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $StateId ;

	}
		
	
}
?>