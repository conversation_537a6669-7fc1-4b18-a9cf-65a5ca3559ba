﻿<?php
//include classes and config file here
require_once('../includes/config.php');
?>

<!-- <PERSON> Loader -->
<!-- <div class="ping" id="loading-div-background1"></div>
<div id="bg-loader"> </div> -->
<!-- Page Loader -->
<!-- <div id="image-viewer">
	<div class="close-parent">
		<span class="close">&times;</span>
		<img class="modal-content" id="full-image">
	</div>
</div> -->


<!--Datatable js----->
<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/vendors.min.js"></script>
<!-- BEGIN Vendor JS-->

<!-- BEGIN: Page Vendor JS-->
<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/ui/jquery.sticky.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/charts/jquery.sparkline.min.js"></script>
<!-- END: Page Vendor JS-->

<!-- BEGIN: Theme JS-->
<script src="<?php echo BASE_PATH; ?>/assets/js/core/app-menu.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/js/core/app.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/customizer.min.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/footer.min.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/ui/breadcrumbs-with-stats.min.js"></script>
<!-- END: Theme JS-->
<!---- alertify js ---->
<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/sweetalert2.all.min.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/vendors/js/extensions/polyfill.min.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/extensions/ex-component-sweet-alerts.js"></script>
<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/tooltip/tooltip.js"></script>
<script src="<?php echo BASE_PATH; ?>/superadmin/vendors/alertifyjs/alertify.js"></script>


<!--- magnipopjs ---->
<script src="<?php echo BASE_PATH; ?>/assets/js/scripts/magnificpopup/jquery.magnific-popup.min.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.11.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.11.0/firebase-messaging-compat.js"></script>

<!-- Common Js -->
<script src="<?php echo BASE_PATH; ?>/assets/js/common.js"></script>

<script>
	// const button = document.querySelector(".button");
	toast = document.querySelector(".custom-toast");
	closeIcon = document.querySelector(".custom-toast-close");
	progress = document.querySelector(".chat-progress");

	let timer1, timer2;

	// button.addEventListener("click", () => {
	// 	console.log("clicked");
	// 	progress.classList.add("active");
	// 	toast.classList.add("active");

	// 	timer1 = setTimeout(() => {
	// 		toast.classList.remove("active");
	// 	}, 5000); //1s = 1000 milliseconds

	// 	timer2 = setTimeout(() => {
	// 		progress.classList.remove("active");
	// 	}, 5300);
	// });

	closeIcon.addEventListener("click", () => {
		toast.classList.remove("active");
		console.log("outside here");
		setTimeout(() => {
			console.log('inside here');
			progress.classList.remove("active");
		}, 0);

		clearTimeout(timer1);
		clearTimeout(timer2);
	});

	// setTimeout(() => {
	//     toast.classList.remove("active");
	// }, 5300);
</script>

<script>
	var businessId = $("#loggedBusinessId").val();
	var isPrimary = $('#isPrimary').val();
	var role = (isPrimary == 1) ? 2 : 3;
	var loggedUserEmail = $('#loggedBusinessEmail').val();	
	var ref_userId = '<?php echo $loggedEmployeeId; ?>';
	var xpikey = '<?php echo CHAT_APIKEY; ?>'; // 
	var domain = "<?php echo CHAT_USER_SERVICE; ?>";

	$(document).ready(function () {
		// GetChatNotificationsList();
		GetChatNotificationsList(role, loggedUserEmail, businessId, ref_userId);
		WebSocketConnectionStart(businessId,loggedUserEmail,ref_userId,role); //
	});

	// alert(businessId);
	const firebaseConfig = {
		apiKey: "AIzaSyAgJl8xk4kzoEGjRihzTGV_LOwpWd4jeQ4",
		authDomain: "probizca-chat-notification.firebaseapp.com",
		projectId: "probizca-chat-notification",
		storageBucket: "probizca-chat-notification.appspot.com",
		messagingSenderId: "427763745974",
		appId: "1:427763745974:web:c7c95614dafd54eceed217",
	};

	const app = firebase.initializeApp(firebaseConfig)
	const messaging = firebase.messaging()

	// console.log("fiebaseApp :", app);
	// console.log("messaging :", messaging);

	// Retrieve the device token
	messaging.getToken({
			vapidKey: "BO-6QCkGbB7dQHFvWGbduabakJ4TnQBD11fk3YQ3VH99tlErXJjCZi0AfkoWkiToa4zOHdqCcqww4wGIpxwxHIA"
		})
		.then((currentToken) => {
			console.log("Device Token:", currentToken);
			// document.getElementById('deviceToken').textContent = currentToken;
			// SendDeviceTokenToChatApp(currentToken);
			SendDeviceTokenToChatApp(currentToken, businessId, role, loggedUserEmail, ref_userId, xpikey, domain);
		})
		.catch((error) => {
			console.error('Error getting token:', error);
		});


	// Listen for messages when the app is in the foreground
	messaging.onMessage((payload) => {
		// console.log('Message received:', payload);

		const title = payload.notification.title;
		const body = payload.notification.body;

		// console.log('lenght:', payload.length);
		console.log('Title:', title);
		console.log('Body:', body);
		// alertify.success(body);

		$('#Notititle').text(title);
		$('#Notimessage').text(body);

		toast.classList.add("active");
		setTimeout(() => {
			toast.classList.remove("active");
		}, 5300);
		sGetChatNotificationsList(role, loggedUserEmail, businessId, ref_userId);
		console.log('Message body:', payload.notification.body);
	});

</script>

<script>
	<?php
	if (isset($_SESSION['loggedNewSystemUserId'])) {
		unset($_SESSION['scustomerId']);
	?>
		//send loginAs details 		
		$('.loginAs').on('click', function() {
			var backToSystemUseId = $("#systemUserId").val();
			Swal.fire({
				title: 'Confirmation',
				text: "Continue with login?",
				type: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				confirmButtonText: 'Login',
				confirmButtonClass: 'btn btn-outline-primary btn-glow',
				cancelButtonClass: 'btn btn-outline-danger ml-1',
				buttonsStyling: false,
			}).then(function(result) {
				if (result.value) {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_login_as_employee.html",
						data: {
							backToSystemUseId: backToSystemUseId
						},
						success: function() {
							Swal.fire({
								type: "success",
								title: 'Login!',
								text: 'Login Successfully.',
								confirmButtonClass: 'btn btn-success btn-glow',
							})
							window.location.href = "../superadmin/dashboard.html";
						}
					});
				}
			})
		});
	<?php
	}
	?>
</script>
<script>
	// $('.viewNotificationPopup').magnificPopup({
	// 	type: 'inline',
	// 	gallery: {
	// 	  enabled: true
	// 	},
	// 	preloader: false,
	// 	// When elemened is focused, some mobile browsers in some cases zoom in
	// 	// It looks not nice, so we disable it:
	// 	callbacks: {
	// 		beforeOpen: function() {
	// 			if($(window).width() < 700) {
	// 				this.st.focus = false;
	// 			} else {

	// 			}
	// 		}
	// 	}
	// });

	$(document).ready(function() {
		// $('.viewNotificationPopup').magnificPopup({
		// 	type: 'inline',
		// 	gallery: {
		// 	  enabled: true
		// 	},
		// 	preloader: false,
		// 	// When elemened is focused, some mobile browsers in some cases zoom in
		// 	// It looks not nice, so we disable it:
		// 	callbacks: {
		// 		beforeOpen: function() {
		// 			if($(window).width() < 700) {
		// 				this.st.focus = false;
		// 			} else {

		// 			}
		// 		}
		// 	}
		// });

		$('.viewNotificationPopup').magnificPopup({
			type: 'ajax',
			midClick: true,
			mainClass: 'mfp-fade',
			closeOnBgClick: false
		});
		$('form input').keydown(function(e) {
			if (e.keyCode == 13) {
				e.preventDefault();
				return false;
			}
		});
	});


	//***** ON-CLICK SHOW REMOVE NOTIFICATION AND UPDATE NOTIFICATION COUNT ******//
	// var newCount = 0;
	var notificationCount = $("#notificationCountValue").val();	
</script>