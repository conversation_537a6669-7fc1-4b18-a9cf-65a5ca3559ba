<?php
   
    //include file and class
    include('includes/validatebusinesslogin.php');
    include('../includes/config.php');
	include('../includes/commonfun.php');
	include('../class/clsDB.php');
	include('../class/PasswordHash.php');	
	include('../class/clsService.php');
	include('../class/clsAddonMaster.php');
    include('../class/clsBusiness.php');
    include('../class/clsSendEmails.php');
	include('../class/class.phpmailer.php');
	include('../class/clsPushNotification.php');
	include('../class/clsNotification.php');
    include('../class/clsServiceCategory.php');
	include('../class/clsAffiliate.php');
    require  '../class/Twilio/autoload.php';
	use Twilio\Rest\Client; 


	
    if($_SERVER['REQUEST_METHOD'] == "POST")
	{
	   
		//Get Post Values
		$serviceId  = isset($_POST['serviceId']) ? trim($_POST['serviceId']) : "";
		$businessId  = isset($_POST['businessId']) ? trim($_POST['businessId']) : "";		
		$serviceCode = isset($_POST['serviceCode']) ? trim($_POST['serviceCode']) : "";
		$title = isset($_POST['title']) ? trim($_POST['title']) : "";
		// $serviceCategoryId = isset($_POST['serviceCategoryId']) ? trim($_POST['serviceCategoryId']) : "";
		$serviceCategoryId = isset($_POST['serviceCategoryId']) ? trim($_POST['serviceCategoryId']) : trim($_POST['serveCatId']);

		$price = isset($_POST['price']) ? trim($_POST['price']) : "";
		$affiliate_per = isset($_POST['affiliate_per']) ? trim($_POST['affiliate_per']) : "";
		$affiliate_amt = isset($_POST['affiliate_amt']) ? trim($_POST['affiliate_amt']) : "";
		$description = isset($_POST['description']) ? trim($_POST['description']) : "";
		$imageName = isset($_POST['imageName']) ? trim($_POST['imageName']) : "";	

		// $couponType = isset($_POST['couponType']) ? trim($_POST['couponType']) : "";
		$couponType = isset($_POST['couponType']) ? trim($_POST['couponType']) : trim($_POST['couponTypeId']);
		$coupon_amt = isset($_POST['coupon_amt']) ? trim($_POST['coupon_amt']) : "";

		// echo "couponType -> ".$couponType;
		// echo "<br>";

		$reward_amt = isset($_POST['reward_amt']) ? trim($_POST['reward_amt']) : "";
		$reward_points = isset($_POST['reward_points']) ? trim($_POST['reward_points']) : "";

		// $loyaltyType = isset($_POST['loyaltyType']) ? trim($_POST['loyaltyType']) : "";
		$loyaltyType = isset($_POST['loyaltyType']) ? trim($_POST['loyaltyType']) : trim($_POST['loyaltyTypeId']);
		$loyalty_amt = isset($_POST['loyalty_amt']) ? trim($_POST['loyalty_amt']) : "";		
		$loyalty_per = isset($_POST['loyalty_per']) ? trim($_POST['loyalty_per']) : "";		

		if($loyaltyType == 1 && $loyalty_per > 0 || $loyalty_per !='')
		{
			$lpoints = ($loyalty_amt * $loyalty_per)/100;	
		}


		$loyalty_points = ($_POST['loyalty_points']) > 0 ? trim($_POST['loyalty_points']) : $lpoints;	

		$startDate = isset($_POST['startDate']) ? trim($_POST['startDate']) : "";					
		$endDate = isset($_POST['endDate']) ? trim($_POST['endDate']) : "";		
		// $affiliateIdArray  = isset($_POST['affiliateId']) ? ($_POST['affiliateId']) : "";			
		$notificationType  = isset($_POST['notificationType']) ? ($_POST['notificationType']) : "";			
		$affiliate_min_payout  = isset($_POST['affiliate_min_payout']) ? ($_POST['affiliate_min_payout']) : "";			
		$affiliateEligibleamt  = isset($_POST['affiliateEligibleamt']) ? ($_POST['affiliateEligibleamt']) : "";			
		
		// print_r($affiliateIdArray);exit;
		$objServiceCategory = new clsServiceCategory();
		$serviceCategoryDtls = $objServiceCategory->GetServiceCategoryDetails($serviceCategoryId);
		$serviceCategoryName = $serviceCategoryDtls['title'];


		function RemoveSpecialChar($str) { 
			// Using str_replace() function
			// to replace the word
			$res = str_replace( array( '\'', '"',
			',' , ';', '<', '>','(',')','-',' '), '', $str);
			// Returning the result
			return $res;
			}
		
		
	    //check categoryId
		if($serviceCategoryId ==1)
		{
		    $reward_points = 0;
		    $reward_amt = 0;
			$loyalty_per = 0;
			$loyalty_amt = 0;
			$loyalty_points = 0;
		}
		if($serviceCategoryId ==2)
		{
		    $coupon_per = 0;
		    $coupon_amt = 0;
			$loyalty_per = 0;
			$loyalty_amt = 0;
			$loyalty_points = 0;
		}
		if($serviceCategoryId ==3)
		{
		    $coupon_per = 0;
		    $coupon_amt = 0;
			$reward_points = 0;
			$reward_amt = 0;
		}
		//save and update data
		$objService = new clsService();
		$objService->serviceCode = $serviceCode;
		$objService->title = $title;
		$objService->serviceCategoryId = $serviceCategoryId;
		$objService->price = $price;
		$objService->affiliate_per = $affiliate_per;
		$objService->affiliate_amt = $affiliate_amt;
		$objService->affiliateEligibleamt = $affiliateEligibleamt;
		$objService->affiliate_min_payout = $affiliate_min_payout;
		$objService->description = $description;
		$objService->couponType = $couponType;		

		if($couponType == 1) //Percentage
		{
			$objService->coupon_per = $coupon_amt;		

		}
		else if($couponType == 2) //Amount
		{
			$objService->coupon_amt = $coupon_amt;		
		}	

		$objService->reward_amt = $reward_amt;		
		$objService->reward_points = $reward_points;	
		$objService->loyaltyType = $loyaltyType;	
		$objService->loyalty_per = $loyalty_per;	
		$objService->loyalty_amt = $loyalty_amt;
		$objService->loyalty_points = $loyalty_points;		
		$objService->businessId = $businessId;
		$objService->startDate = $startDate;
		$objService->endDate = $endDate;
		$objService->notificationType = $notificationType;

		// echo "<pre>";
		// print_r($objService);
		// exit;
		$retServiceId = $objService->SaveService($serviceId); 		
		
		if($retServiceId > 0)
		{	

		    //Here we are going to upload logo
			if(isset($_FILES['serviceImage']))
			{
				$Image = $_FILES['serviceImage']['name'];
				if($Image) 
				{
					$ext = strtolower(pathinfo($_FILES['serviceImage']['name'], PATHINFO_EXTENSION));
					if($ext!="png" && $ext !="jpg" && $ext !="jpeg" && $ext!="gif")
					{
						header('location:services.html?status=Error');
						exit();
					}

					//File Name
					$serviceImageName = $Image;

					//Check User Directory
					$uploaddir = "../upload/service/";
					if(!file_exists($uploaddir))
					{
						mkdir($uploaddir); 
					}

				   	$uploaddir .= $retServiceId."/";
					if(!file_exists($uploaddir))
					{
						mkdir($uploaddir); 
					}
					
					if($imageName!='')
					{
					    $uploadPaths = ROOT_PATH.'/upload/service/'.$retServiceId.'/'.$imageName;
						unlink($uploadPaths);
					}
					
					//Save File Path
					$uploadFilePath = $uploaddir.$serviceImageName;					

					//Copy File
					copy($_FILES['serviceImage']['tmp_name'], $uploadFilePath);
					
					//-----------------------------------
					//Update File Name to DB
					//-----------------------------------
					//UPDATE users TABLE 
					$objDB = new clsDB();
					$objDB->UpdateSingleColumnValueToTable('service', 'imageName',$serviceImageName,'serviceId',$retServiceId);
					unset($objDB);					
					//-----------------------------------
				}
			}

			// Send Notification to all Customers and Selected Affiliates
			$objAddonMaster = new clsAddonMaster();
			$emailsmsCount = $objAddonMaster->GetPurchaseAddonDetailsInRow($businessId);
			if($emailsmsCount !='')
			{		

					// pks for Email / SMS
					$addondtlId_Email = $emailsmsCount['addondtlId_Email'];
					$addondtlId_Sms = $emailsmsCount['addondtlId_Sms'];

					// EMAIL
					$defaultEmailCnt = $emailsmsCount['defaultCount'];
					$defaultEmailUsedCnt = $emailsmsCount['dfUsed'];
					$purchasedEmailCnt = $emailsmsCount['purchasedCount'];
					$purchaseUsedEmailCnt = $emailsmsCount['purUsed'];
	
					$remainEmailCount = ($defaultEmailCnt - $defaultEmailUsedCnt) + ($purchasedEmailCnt - $purchaseUsedEmailCnt);

					// SMS
					$defaultSMSCnt = $emailsmsCount['dfSMS'];
					$defaulSMSUsedCnt = $emailsmsCount['dfsmsU'];
					$purchasedSMSCnt = $emailsmsCount['SmsPurchaseCnt'];
					$purchaseUsedSMSCnt = $emailsmsCount['SmsPurUsed'];
	
					$remainSMSCount = ($defaultSMSCnt - $defaulSMSUsedCnt) + ($purchasedSMSCnt - $purchaseUsedSMSCnt);			
			}

			$objBusiness = new clsBusiness();	
			$businessName = $objBusiness->GetBusinessName($businessId);
			$employeeId = $objBusiness->CheckBusinesAsAnEmployee($businessId);
			$customerData = $objBusiness->GetBusinessCustomerList($businessId,$isFavoriteId='',$customerId='');
			if($customerData != '')
			{

				$customerCount = mysqli_num_rows($customerData);
				
				function validateEmail($email) {
					// Define the regular expression pattern for a valid email address
					$pattern = '/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/';
				
					// Use the preg_match function to test if the email matches the pattern
					if (preg_match($pattern, $email)) {
						return true; // Valid email address
					} else {
						return false; // Invalid email address
					}
				}
				// echo $customerCount;exit;

				while($row = mysqli_fetch_assoc($customerData))
				{		
					// echo "customerId -> ".
					$customerId = $row['customerId']; 
					// echo "<br>";	echo "<br>";														  
					$firstName = $row['firstName']; 
					$lastName = ($row['lastName']);
					// echo "email -> ".
					$email = stripslashes($row['email']); 
					// echo "<br>"; echo "<br>";
					$address = ($row['address']);									   
					$status = ($row['status']);	
					// echo "phoneNo -> ".
					$phoneNo = RemoveSpecialChar($row['phoneNo']);	
					//  echo "<br>"; echo " ----------------------------------------------------";	 echo "<br>";
					$fullName = $firstName.' '.$lastName;
					
					$objNotification= new clsPushNotification();
					$objNotification->businessId = $businessId;		
					$objNotification->title = $businessName.' Announces New '.$serviceCategoryName.' Service';		
					$objNotification->description = $title.' '.$serviceCategoryName.' will be Starting From '.(date("m/d/Y",strtotime($startDate))).' Check out the incentives for more details.';		
					$objNotification->isSystemNotification = 1;				
					$objNotification->systemUserId = 0;				
					$objNotification->notificationDate = date("Y-m-d H:i:s");
					$retNotificationId=$objNotification->SaveNotification(0);
					if($retNotificationId != '')
					{
						$objNotification= new clsPushNotification();
						$objNotification->businessId = $businessId;	
						$objNotification->customerId = $customerId;	
						$objNotification->affiliateId = 0;	
						$objNotification->notificationId = $retNotificationId;	
						$objNotification->employeeId = 0;	
						$retnotificationDetailId=$objNotification->SaveNotificationDetails(0);
						// unset($objDB);
					}
					
					
					if($email!='' && validateEmail($email) && ($notificationType ==1 || $notificationType==3))
					{	
						
						if($defaultEmailCnt > $defaultEmailUsedCnt)
						{
							$objSendEmails = new clsSendEmails();
							$objSendEmails->SendIncentiveNotification($fullName,$retServiceId,$email,$businessName);
							unset($objSendEmails);
							
							$defaultEmailUsedCnt = $defaultEmailUsedCnt + 1;
							$objAddonMaster->UpdateDefaultUsedCount($businessId,1,$defaultEmailUsedCnt,$addondtlId_Email);

							if(($defaultEmailCnt * 80)/100 == $defaultEmailUsedCnt) 
							{
								AddonNotify80Percent($businessId,$employeeId,1);
							}
							else if($defaultEmailCnt == $defaultEmailUsedCnt)
							{
								addonLimitiReached($businessId,$employeeId,1);
							}
						}
						else if($purchasedEmailCnt >  $purchaseUsedEmailCnt)
						{
							$objSendEmails = new clsSendEmails();
							$objSendEmails->SendIncentiveNotification($fullName,$retServiceId,$email,$businessName);
							unset($objSendEmails);
							
							$purchaseUsedEmailCnt = $purchaseUsedEmailCnt + 1;
							$objAddonMaster->UpdatePurchaseUsedCount($businessId,1,$purchaseUsedEmailCnt,$addondtlId_Email);

							if(($purchasedEmailCnt * 80)/100 == $purchaseUsedEmailCnt)
							{
								AddonNotify80Percent($businessId,$employeeId,1);
							}
							else if($purchasedEmailCnt == $purchaseUsedEmailCnt)
							{
								addonLimitiReached($businessId,$employeeId,1);
							}
						}
						else
						{
							addonLimitiReached($businessId,$employeeId,1);
						}						
						
					}

					if($phoneNo!='' && ($notificationType ==2 || $notificationType==3))
					{
						if($defaultSMSCnt > $defaulSMSUsedCnt)
						{
							$toNumber = '+1'.$phoneNo;
							$messageBody ='';
							$messageBody = $businessName.' Announces New : '.$serviceCategoryName." Service";
							$messageBody .= $title.' '.$serviceCategoryName.' will be Starting From '.(date("m/d/Y",strtotime($startDate))).' Check out the incentives for more details.'."\n\n".' Reply STOP to unsubscribe';

							
							// $txtCustomerPhone = '+1'.$phoneNo;  
							try {
								sendSMS($phoneNo,$messageBody);
							// 	// Create a new instance of the Client class
							// 	$client = new Client(ACCOUNT_SID, AUTH_TOKEN_ID);
							
							// 	// Create the message
							// 	$message = $client->messages->create(
							// 		$txtCustomerPhone,
							// 		array(
							// 			'from' => REGISTER_NUMBER,
							// 			'body' => $messageBody
							// 		)
							// 	);
							
								// Success message
								// echo "SMS sent successfully.";
								$defaulSMSUsedCnt = $defaulSMSUsedCnt + 1;
								$objAddonMaster->UpdateDefaultUsedCount($businessId,2,$defaulSMSUsedCnt,$addondtlId_Sms);

								if(($defaultSMSCnt * 80)/100 == $defaulSMSUsedCnt) 
								{
									AddonNotify80Percent($businessId,$employeeId,2);
								}
								else if($defaultSMSCnt == $defaulSMSUsedCnt)
								{
									addonLimitiReached($businessId,$employeeId,2);
								}
							} catch (Exception $e) {
								// Handle exceptions or errors during the SMS sending process
								// echo "Error sending SMS: " . $e->getMessage();
								$error = $e->getMessage();
								$error =str_replace("'", "", $error);
									
								$objNotification = new clsNotification();
								$objNotification->businessId = $businessId;
								$objNotification->referenceId = $retServiceId;
								$objNotification->type = 1;  
								$objNotification->userType = 2;  
								$objNotification->userId = $customerId;
								$objNotification->notificationType = 4;
								$retnNotificationid = $objNotification->SaveNotificationLog();
								if($retnNotificationid > 0)
								{
									$objDB = new clsDB();
									$objDB->UpdateSingleColumnValueToTable('notificationlog','response',$error,'notificationid',$retnNotificationid);
									unset($objDB);
								}
					
							}
							// $client = new Client(ACCOUNT_SID, AUTH_TOKEN_ID);  
							// $message = $client->messages->create(			
							// 	$txtCustomerPhone,
							// 		array(
							// 			'from' => REGISTER_NUMBER,							
							// 			'Body' => $messageBody
							// 		)
							// );
							
							// $defaulSMSUsedCnt = $defaulSMSUsedCnt + 1;
							// $objAddonMaster->UpdateDefaultUsedCount($businessId,2,$defaulSMSUsedCnt);

							// if(($defaultSMSCnt * 80)/100 == $defaulSMSUsedCnt) 
							// {
							// 	AddonNotify80Percent($businessId,$employeeId,2);
							// }
							// else if($defaultSMSCnt == $defaulSMSUsedCnt)
							// {
							// 	addonLimitiReached($businessId,$employeeId,2);
							// }
						}
						else if($purchasedSMSCnt >  $purchaseUsedSMSCnt)
						{
							$toNumber = '+1'.$phoneNo;
							$messageBody ='';
							$messageBody = $businessName.' Announces New '.$serviceCategoryName.' Service';
							$messageBody .= $title.' '.$serviceCategoryName.' will be Starting From '.(date("m/d/Y",strtotime($startDate))).' Check out the incentives for more details.'."\n\n".' Reply STOP to unsubscribe';

							$txtCustomerPhone = '+1'.$phoneNo;  

							try {
								sendSMS($phoneNo,$messageBody);

								// Create a new instance of the Client class
								// $client = new Client(ACCOUNT_SID, AUTH_TOKEN_ID);
							
								// // Create the message
								// $message = $client->messages->create(
								// 	$txtCustomerPhone,
								// 	array(
								// 		'from' => REGISTER_NUMBER,
								// 		'body' => $messageBody
								// 	)
								// );
							
								// Success message
								// echo "SMS sent successfully.";
								$purchaseUsedSMSCnt = $purchaseUsedSMSCnt + 1;
								$objAddonMaster->UpdatePurchaseUsedCount($businessId,2,$purchaseUsedSMSCnt,$addondtlId_Sms);

								if(($purchasedSMSCnt * 80)/100 == $purchaseUsedSMSCnt)
								{
									AddonNotify80Percent($businessId,$employeeId,2);
								}
								else if($purchasedSMSCnt == $purchaseUsedSMSCnt)
								{
									addonLimitiReached($businessId,$employeeId,2);
								}
							} catch (Exception $e) {
								// Handle exceptions or errors during the SMS sending process
								// echo "Error sending SMS: " . $e->getMessage();
								$error = $e->getMessage();
								$error =str_replace("'", "", $error);
									
								$objNotification = new clsNotification();
								$objNotification->businessId = $businessId;
								$objNotification->referenceId = $retServiceId;
								$objNotification->type = 1;  
								$objNotification->userType = 2;  
								$objNotification->userId = $customerId;
								$objNotification->notificationType = 4;
								$retnNotificationid = $objNotification->SaveNotificationLog();
								if($retnNotificationid > 0)
								{
									$objDB = new clsDB();
									$objDB->UpdateSingleColumnValueToTable('notificationlog','response',$error,'notificationid',$retnNotificationid);
									unset($objDB);
								}
					
							}


							// $client = new Client(ACCOUNT_SID, AUTH_TOKEN_ID);  
							// $message = $client->messages->create(			
							// 	$txtCustomerPhone,
							// 		array(
							// 			'from' => REGISTER_NUMBER,							
							// 			'Body' => $messageBody
							// 		)
							// );
							
							// $purchaseUsedSMSCnt = $purchaseUsedSMSCnt + 1;
							// $objAddonMaster->UpdatePurchaseUsedCount($businessId,2,$purchaseUsedSMSCnt);

							// if(($purchasedSMSCnt * 80)/100 == $purchaseUsedSMSCnt)
							// {
							// 	AddonNotify80Percent($businessId,$employeeId,2);
							// }
							// else if($purchasedSMSCnt == $purchaseUsedSMSCnt)
							// {
							// 	addonLimitiReached($businessId,$employeeId,2);
							// }
						}
						else
						{
							addonLimitiReached($businessId,$employeeId,2);
						}
						
					}


				}
				
			}

			$messageText = $serviceId ? 'updated' : 'added';
			header('location:businessservices.html?status='.$messageText);
			exit();	
			
		}
		else
		{
			header('location:businessservices.html');
			exit();
		}

	}
	else
	{
		header('location:businessservices.html');
		exit();	
	}
	function AddonNotify80Percent($businessId,$employeeId,$addonNameId)
	{
		$objBusiness = new clsBusiness();
		$businessDtls=$objBusiness->GetBusinessDetails($businessId);
		$businessEmail = $businessDtls['email'];
		$businessName = $businessDtls['businessName'];
		// echo "notify80";exit;
		$addonName = ($addonNameId == 1) ? "Email" : "SMS";
		
		// 80% Addon usage Notification
		$objPushNotification= new clsPushNotification(); 
		$objPushNotification->businessId = $businessId;		
		$objPushNotification->title = 'Your '.$addonName.' usage is at 80%';		
		$objPushNotification->description = 'You are reaching your '.$addonName.' limit for the month. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';		
		$objPushNotification->isSystemNotification = 1;				
		$objPushNotification->systemUserId = 0;				
		$objPushNotification->notificationDate = date("Y-m-d H:i:s");
		$retNotificationId=$objPushNotification->SaveNotification(0);
		if($retNotificationId != '')
			{
				$objPushNotification= new clsPushNotification();
				$objPushNotification->businessId = $businessId;	
				$objPushNotification->customerId = 0;	
				$objPushNotification->affiliateId = 0;	
				$objPushNotification->notificationId = $retNotificationId;	
				$objPushNotification->employeeId = $employeeId;	
				$retnotificationDetailId=$objPushNotification->SaveNotificationDetails(0);
			}
		// super <NAME_EMAIL>
		$superAdminEmail = '<EMAIL>';					
		$businessAdminEmail = '<EMAIL>,'.$businessEmail;					
		$superAdmindescription = "The business ".$businessName." has reached it’s limit of ".$addonName." Addon upto 80%.";				
		//$businessdescription = 'You are reaching your '.$addonName.' limit for the month. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';				
		$message="You are reaching your ".$addonName." Subscription limit.You’re currently at 80% for the month. For additional ".$addonName." messages, you will need to upgrade your subscription plan.";
		$title="Your ".$addonName." Addon Usage is Reaching 80% for the Month";
			


		$objSendEmails = new clsSendEmails();

		$objSendEmails->SendAddonSubscriptionSurpassed($businessName,$businessAdminEmail,$message,$addonName);
		$objSendEmails->SendAddonSubscriptionDetailsToSuperAdminSurpassed($businessName,$superAdminEmail,$superAdmindescription,$addonName);
		unset($objSendEmails);	
	}
	
	function addonLimitiReached($businessId,$employeeId,$addonNameId)
	{

		$objBusiness = new clsBusiness();
		$businessDtls=$objBusiness->GetBusinessDetails($businessId);
		$businessEmail = $businessDtls['email'];
		$businessName = $businessDtls['businessName'];

		$addonName = ($addonNameId == 1) ? "Email" : "SMS";

		// Addon Limit Reached Notification
		$objPushNotification= new clsPushNotification();
		$objPushNotification->businessId = $businessId;		
		$objPushNotification->title = 'Your '.$addonName.' usage has reached it’s limit';		
		$objPushNotification->description = 'Your '.$addonName.' limit for the month has been surpassed. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';		
		$objPushNotification->isSystemNotification = 1;				
		$objPushNotification->systemUserId = 0;				
		$objPushNotification->notificationDate = date("Y-m-d H:i:s");
		$retNotificationId=$objPushNotification->SaveNotification(0);
		if($retNotificationId != '')
			{
				$objPushNotification= new clsPushNotification();
				$objPushNotification->businessId = $businessId;	
				$objPushNotification->customerId = 0;	
				$objPushNotification->affiliateId = 0;	
				$objPushNotification->notificationId = $retNotificationId;	
				$objPushNotification->employeeId = $employeeId;	
				$retnotificationDetailId=$objPushNotification->SaveNotificationDetails(0);
			}

		// super <NAME_EMAIL>
		$superAdminEmail = '<EMAIL>';					
		$businessAdminEmail = '<EMAIL>,'.$businessEmail;									
		$superAdmindescription = "The business ".$businessName." has reached it’s limit of ".$addonName."";				
		//$businessdescription = 'Your '.$addonName.' limit for the month has been surpassed. For additional '.$addonName.' messages, you will need to upgrade your subscription plan.';
		$message="Your ".$addonName." limit for the month has been surpassed. For additional ".$addonName." messages, you will need to upgrade your subscription plan.";
		$title= $addonName." Limit Surpassed";
		$objSendEmails = new clsSendEmails();
		$objSendEmails->SendAddonSubscriptionSurpassed($businessName,$businessAdminEmail,$message,$title);
		$objSendEmails->SendAddonSubscriptionDetailsToSuperAdminSurpassed($businessName,$superAdminEmail,$superAdmindescription,$title);
		unset($objSendEmails);	
	}

?>


