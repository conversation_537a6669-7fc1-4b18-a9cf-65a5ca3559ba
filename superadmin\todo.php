<?php
	//include classes and config file here	
	include('includes/validatesystemuserlogin.php');
	include('../includes/config.php');
    include('../includes/commonfun.php');
    include('../class/clsDB.php');
    include('../class/clsToDo.php');
    include('../class/clsEmployee.php');
    include('../class/clsSystemUser.php');
	include('validatepermission.php');

   if($permissionArray['IsToDoViewAccess'] ==0) {header('location:dashboard.html?status=PermissionError'); exit; }
	
	//***** TODO VARIABLES ******//
	    $taskname = '';		
		$tododescription = '';		
		$todonotes = '';
		$allToDoList = '';
		$employeeList = '';
		$isFlag = 0;
		$todoCount = 0;
	    $assignDate  = date("m/d/Y h:i A");
		$systemUserId = $_SESSION["loggedSystemUserId"];
		$isPrimary = $_SESSION["isPrimary"];
		
	//***** GET ALL TODO LIST *****// 
		$objToDo = new clsToDo();
		if(isset($_GET['isFlag']))
		{
			$isFlag = DecodeQueryData($_GET['isFlag']);	
            // if($_SESSION["isPrimary"] ==1)
			// {				
			    // $allToDoList = $objToDo->GetAllToDoList(0 , $systemUserId , $isFlag , 0); // businessId , systemUserId , isFlag , employeeId			
			// }
			// else
			// {
				$allToDoList = $objToDo->GetAllSystemUserToDoList($systemUserId , $isFlag); // businessId , systemUserId , isFlag , employeeId			
			// }
			$todoCount = mysqli_num_rows($allToDoList);
		}
		else
		{	
	        // if($_SESSION["isPrimary"] ==1)
			// { 
		        // $allToDoList = $objToDo->GetAllToDoList(0 , $systemUserId , 0 , 0); // businessId , systemUserId , isFlag , employeeId		   
			// }
			// else
			// {
				$allToDoList = $objToDo->GetAllSystemUserToDoList($systemUserId , 0); // businessId , systemUserId , isFlag , employeeId		   
			// }
			$todoCount = mysqli_num_rows($allToDoList);
		}
	   
	
?>
<!DOCTYPE html>
<html lang="en">
  <head>
  <title>ToDo</title>
  <?php include("includes/headercss.php") ?>
      
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i%7CQuicksand:300,400,500,700" rel="stylesheet">
  <!-- BEGIN: Vendor CSS-->
    <link rel="stylesheet" type="text/css" href="build/js/vendors/css/vendors.min.css">
    <!-- END: Vendor CSS-->

    <!-- BEGIN: Theme CSS-->
    <link rel="stylesheet" type="text/css" href="build/js/css/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="build/js/css/bootstrap-extended.css">
    <link rel="stylesheet" type="text/css" href="build/js/css/colors.css">
    <link rel="stylesheet" type="text/css" href="build/js/css/components.css">
    <!-- END: Theme CSS-->

    <!-- BEGIN: Page CSS-->
    <link rel="stylesheet" type="text/css" href="build/js/core/menu/menu-types/horizontal-menu.css">
    <link rel="stylesheet" type="text/css" href="build/js/core/colors/palette-gradient.css">
    <link rel="stylesheet" type="text/css" href="build/js/fonts/simple-line-icons/style.min.css">
    <link rel="stylesheet" type="text/css" href="build/js/css/pages/app-email.css">
    <!-- END: Page CSS-->

    <!-- BEGIN: Custom CSS-->
	<link rel="stylesheet" href="<?php echo BASE_PATH;?>/superadmin/vendors/alertifyjs/css/alertify.css">
    
    <!-- END: Custom CSS-->
	
	<style>
	   .ScrollStyle
		{
			max-height: 500px;
			overflow-y: scroll;
		}

				
		.ScrollStyle {
    max-height: calc(100vh - 110px) !important;
    overflow-y: auto !important;
}

		.ScrollStyle::-webkit-scrollbar {
    max-height: calc(100vh - 110px) !important;
    /* overflow-y: scroll; */
	display: none;
}     

.badge {
    position: relative !important;
    border-radius: 10% !important;

}
	</style>
	
  </head>
  <body class="nav-md horizontal-layout horizontal-menu content-left-sidebar email-application"  data-open="hover" data-menu="horizontal-menu" data-col="content-left-sidebar">
    <div class="container body" style="width: 100%;max-width: 100%;padding: 0;">
       <div class="main_container">
       <?php include("includes/leftnav.php") ?>
       <?php include("includes/header.php") ?>

        <!-- page content -->
        <div class="right_col" role="main">
          <div class="">
            <div class="page-title">
              <div class="title_left">
                <h3>ToDo</h3>
              </div>
            </div>
            <div class="clearfix"></div>
			 <div class="row email-wrapper">
			 
				 <!-- BEGIN: Content-->
					<div class="app-content content w-100">
						<div class="sidebar-left">
							<div class="sidebar">
								<div class="sidebar-content email-app-sidebar d-flex">
									<!-- sidebar close icon -->
									<span class="sidebar-close-icon">
										<i class="ft-x"></i>
									</span>
									<!-- sidebar close icon -->
									<div class="email-app-menu">
									    <?php if($permissionArray['IsToDoCreateAccess'] >0) {?>
										<div class="form-group form-group-compose">
											<a href="addtask.html">
											<button type="button" class="btn btn-danger btn-glow btn-block my-2 compose-btn">
												<i class="ft-plus"></i>
												New Task
											</button>
											</a>
										</div>
									    <?php }?>
										<div class="sidebar-menu-list mt-1">
											<div class="list-group">
												<a href="todo.html?isFlag=<?php echo EncodeQueryData(-1);?>" class="list-group-item border-0 active">
													<span class="fonticon-wrap mr-50">
														<i class="ft-align-justify"></i>
													</span>
													<span> All</span>
												</a>
											</div>											
											<label class="filter-label mt-2 mb-1 pt-25">Filters</label>
											<div class="list-group">
												<a href="todo.html?isFlag=<?php echo EncodeQueryData(1);?>" class="list-group-item border-0">
													<span class="fonticon-wrap mr-50">
														<i class="ft-star"></i>
													</span>
													<span>Favorites</span>
												</a>
												<a href="todo.html?isFlag=<?php echo EncodeQueryData(2);?>" class="list-group-item border-0">
													<span class="fonticon-wrap mr-50">
														<i class="ft-check"></i>
													</span>
													<span>Completed</span>
												</a>
												<a href="todo.html?isFlag=<?php echo EncodeQueryData(3);?>" class="list-group-item border-0">
													<span class="fonticon-wrap mr-50">
														<i class="ft-trash-2"></i>
													</span>
													<span>Deleted (Archive)</span>
												</a>
											</div>											
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="content-right">
							<div class="content-overlay"></div>
							<div class="content-wrapper">
								<div class="content-header row">
								</div>
								<div class="content-body">
									<!-- email app overlay -->
									<div class="app-content-overlay"></div>
									<div class="email-app-area">
										<!-- Email list Area -->
										<div class="email-app-list-wrapper">
											<div class="email-app-list">
												<!-- email user list start -->
												<div class="email-user-list list-group">
													<ul class="users-list-wrapper media-list ScrollStyle">
														<?php						  
															if($todoCount >0)
															{	
																$empFullName = '';
																$empRow = '';
																$empImagePath = '';
																$starColor = '';
																$bageClass = '';
																$bageText = '';
																$objEmployee = new clsEmployee();
																$objSystemUser = new clsSystemUser();
																while($row = mysqli_fetch_assoc($allToDoList))
																{
																	$todoId = $row['todoId']; 															  
																	$taskname = $row['taskname']; 															  
																	$systemUserId = $row['systemUserId']; 															  
																	$comments = $row['comments']; 															  
																	$assignTags = $row['assignTags'];
																	$assignSystemUserId = $row['assignSystemUserId'];
																	$businessId1 = $row['businessId'];
																	$isDeleted = $row['isDeleted'];
																	$isCompleted = $row['isCompleted'];
																	$isFavourite = $row['isFavourite'];
																	$noOfOpenDays = $row['noOfOpenDays'];
																	$updatedDate = $row['updatedDate'];
																	$assignDate  = (date("m/d/Y h:i A",strtotime($row['assignDate']))); 
																	
																	//***** IS-FAV CHECK *****//
																	if($isFavourite==1)
																	{
																		$starColor = '#FFD700';
																	}
																	if($isFavourite==0)
																	{
																		$starColor ='';
																	}
																	
																	//***** COMPLETED/INCOMPLETDED CHECK *****//
																	if($isCompleted ==1)
																	{
																		$bageClass = 'badge-success';
																		$bageText = 'COMPLETED';
																		$color = '#28d094';
																	}
																	if($isCompleted ==0)
																	{
																		$bageClass = 'badge-warning';
																		$bageText = 'INCOMPLETE';
																		$color = '#ff9149';
																	}
																	
																	//***** ASSIGNED USER IMAGE PATH *****//
																		$rows = $objSystemUser->GetSystemUserDetails($assignSystemUserId);
																		$imageName  = stripslashes($rows['imageName']);																		
																		
																		//get employee image path
																		if($_SESSION["isPrimary"]==1) 
																		{
																		   $systemUserImagePath = GetSystemUserImagePath($assignSystemUserId,$imageName);
																		}
																		else
																		{
																			$rows1 = $objSystemUser->GetSystemUserDetails($systemUserId);
																			$imageNames  = stripslashes($rows1['imageName']);																			
																			$systemUserImagePath = GetSystemUserImagePath($systemUserId,$imageNames);
																		}
																		
																	
														?>
														<li class="media mail-read" id="todoId_<?php echo ($todoId);?>">	
															<div class="pr-50">
															   <a href="addtask.html?id=<?php echo EncodeQueryData($todoId);?>" style="<?php echo $permissionArray['IsToDoEditAccess'] ? '' : 'pointer-events: none;'?>">
																<div class="avatar">
																	<img class="rounded-circle" src="<?php echo ($systemUserImagePath);?>" alt="Generic placeholder image">
																</div>
																</a>
															</div>
															<div class="media-body">
																<div class="user-details">
																	<div class="mail-items">
																		<span class="list-group-item-text text-truncate mb-0"><a href="addtask.html?id=<?php echo EncodeQueryData($todoId);?>" style="<?php echo $permissionArray['IsToDoEditAccess'] ? '' : 'pointer-events: none;'?>"><?php echo ($taskname);?></a></span>
																	</div>
																</div>
															</div>
															<div class="todo-item-action d-flex text-right align-items-center">
																<div class="todo-badge-wrapper d-flex">
																	<?php if($isCompleted==1){?> 
																		<span class="badge badge-primary badge-pill mr-1" style="background-color:#666ee8;"><?php echo timeAgo($row['updatedDate']);?></span>														    
																	<?php }if($isCompleted==0){?>
																	   <span class="badge badge-primary badge-pill mr-1" style="background-color:#666ee8;"><?php echo timeAgo($row['assignDate']);?></span>														    
																	<?php }?>
																	<span class="badge <?php echo ($bageClass);?> badge-pill" style="background-color:<?php echo ($color);?>"><?php echo ($bageText);?></span>														    
																</div>
																<?php //if($_SESSION["isPrimary"]==1) {?>															
																	<a class='todo-item-favorite setStatusFavorite ml-75' todoId="<?php echo ($todoId);?>" isFavourite="<?php echo ($isFavourite);?>"><i class="ft-star" style="color:<?php echo ($starColor);?>"></i></a>														
																	<?php if($isFlag==3 || $isDeleted==1){?>
																		<a class="ml-75 deleteAjaxRow " style="<?php echo $permissionArray['IsToDoDeleteAccess'] ? '' : 'pointer-events: none;'?>" data-toggle="tooltip" data-popup="tooltip-custom" data-original-title="Deleted (Archive)" data-bg-color="warning" todoId="<?php echo ($todoId);?>"><i class="ft-trash-2" style="color:red;"></i></a>
																	<?php }else{?>
																		<a class='ml-75 deleteSetAjaxRow' todoId="<?php echo ($todoId);?>"><i class="ft-trash-2"></i></a>
																	<?php }?>
																<?php //}?>
														
																
															</div>
														</li>
														
														<?php
															} unset($objEmployee);unset($objSystemUser);
														}
														else
														{
													?>
													<li>
														<h5 class="text-center mt-4 mb-4">No Items Found</h5>
													</li>
													<?php }?>
													</ul>
													<!-- email user list end -->

													<!-- no result when nothing to show on list -->
													<div class="no-results">
														<i class="ft-info font-large-2"></i>
														<h5>No Items Found</h5>
													</div>
												</div>
											</div>
										</div>
										<!--/ Email list Area -->

										
									</div>

								</div>
							</div>
						</div>
					</div>
					<!-- END: Content-->
					<div class="sidenav-overlay"></div>
					<div class="drag-target"></div>
			 </div>
			 
			 
          </div>
        </div>
        <!-- /page content -->

        <?php include("includes/footer.php") ?>
        
      </div>
    </div>

   <?php include("includes/footerjs.php") ?>
   
   
   <!-- BEGIN: Vendor JS-->
    <script src="build/js/vendors/js/vendors.min.js"></script>
    <!-- BEGIN Vendor JS-->

    <!-- BEGIN: Page Vendor JS-->
    <script src="build/js/vendors/js/ui/jquery.sticky.js"></script>
    <script src="build/js/vendors/js/charts/jquery.sparkline.min.js"></script>
    <!-- END: Page Vendor JS-->

    <!-- BEGIN: Theme JS-->
    <script src="build/js/core/app-menu.js"></script>
    <script src="build/js/core/app.js"></script>
    <!-- END: Theme JS-->

    <!-- BEGIN: Page JS-->
    <script src="build/js/scripts/ui/breadcrumbs-with-stats.js"></script>
    <script src="build/js/scripts/pages/app-email.js"></script>
	<script src="<?php echo BASE_PATH;?>/superadmin/vendors/alertifyjs/alertify.js"></script>
    <!-- END: Page JS-->
	
	<script>
	   //***** DELETE TO-DO LIST RECORD *****//
			$('.deleteAjaxRow').on('click', function () 
			{	
				var todoId = $(this).attr('todoId');
                alertify.confirm('Confirmation', 'Continue with delete?', function() {
					$.ajax({
						type: "GET",
						url: "../ajax/ajax_delete_business_todo.html",
						data: {todoId:todoId},
						success:function()
						{					
							$('[id="todoId_'+todoId+'"]').remove(); 
						}
					});
				}, function() {});
			});
			
		//***** FAVOURITE TO-DO LIST RECORD *****//
			$('.setStatusFavorite').on('click', function () 
			{ 
			    var thisanchor = $(this);
				var todoId = $(this).attr('todoId'); 
				var isFavourite = $(this).attr('isFavourite');
				if(isFavourite==1)
				{
					alertify.confirm('Confirmation', 'Continue with remove from favorite?', function() {				   
							$.ajax({
								type: "GET",						
								url: "../ajax/ajax_set_todo_tofavourite.html",
								data: {todoId:todoId,isFavourite:isFavourite},
								success:function()
								{
 									$('[id="todoId_'+todoId+'"]').remove();
									alertify.error("Removed."); 
								}
							});
					}, function() {});	
				}
				if(isFavourite==0)
				{					
					alertify.confirm('Confirmation', 'Continue with added to favorite?', function() {				   
							$.ajax({
								type: "GET",						
								url: "../ajax/ajax_set_todo_tofavourite.html",
								data: {todoId:todoId,isFavourite:isFavourite},
								success:function()
								{
                                    $('[id="todoId_'+todoId+'"]').remove();									
									alertify.success("Added."); 
								}
							});
					}, function() {});
				}
				
					
					
			});	
			
			
			
			//***** REMOVE FROM FAVOURITE TO-DO LIST RECORD *****//
			$('.deleteAjaxRowFavouriteRemove').on('click', function () 
			{	
				var todoId = $(this).attr('todoId'); ; 			
				alertify.confirm('Confirmation', 'Continue with added to favorite?', function() {			   
					$.ajax({
						type: "GET",						
						url: "../ajax/ajax_set_todo_tofavourite.html",
						data: {todoId:todoId},
						success:function()
						{								
							$('[id="todoId_'+todoId+'"]').remove(); 
							alertify.success("Added."); 
						}
					});
				}, function() {});		
			});
			
			
			 //***** SET AS DELETE TO-DO LIST RECORD *****//
			$('.deleteSetAjaxRow').on('click', function () 
			{	
				var todoId = $(this).attr('todoId'); ; 			
				alertify.confirm('Confirmation', 'Continue with add to delete?', function() {				   
					$.ajax({
						type: "GET",						
						url: "../ajax/ajax_set_to_as_deleted.html",
						data: {todoId:todoId},
						success:function()
						{								
							$('[id="todoId_'+todoId+'"]').remove();
                            alertify.success("Added."); 							
						}
					});
				}, function() {});		
			});
	</script>
  </body>
</html>
