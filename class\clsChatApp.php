<?php

class clsChatApp
{
    var $userManagmentId = 0;
    var $userId = 0;
    var $firstName = '';
    var $lastName = '';
    var $email = '';
    var $phone_no = 0;
    var $address = '';
    var $profileImagePath = '';
    var $businessName = '';
    var $parent_id = 0;
    var $role_id = 0;
    var $created_at = 0;
    var $updated_at = 0;
    var $deleted_at = 0;
    var $realtions ='';


    function SaveNewuserdataTochat($postData)
    {
        // echo "create new user";

        // print_r($postData);
        //  exit;

        // $localDomain = 'http://************:4000';
        // $liveDomaein = 'https://chatuserservice.probizca.net';

        // Endpoint URL
        $endpoint = CHAT_USER_SERVICE.'/api/v1/imports/createUser';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY; // CHAT_APIKEY CHAT_APIKEY

        // Initialize cURL session
        $ch = curl_init();
        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: '.$API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute cURL request
        // Execute cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        //  echo json_encode($responseArray);
        // Output response
        return $responseArray;

        // exit;
    }



    function UpdateUserProfilePicTochat($postData)
    {

        // $localDomain = 'http://************:4000';
        // $liveDomaein = 'https://chatuserservice.probizca.net';

        // Endpoint URL
        $endpoint = CHAT_USER_SERVICE.'/api/v1/user/updateProfilePic';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY;

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: '.$API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        

        // Execute cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);

        // Output response
        return $responseArray;


        // exit;
    }


    function UpdateUserDataTochat($postData)
    {
            // print_r($postData);
            // exit;

        // Endpoint URL
        $endpoint =CHAT_USER_SERVICE.'/api/v1/user/update';
        
        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY;

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: '.$API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // echo $ch;exit;

        // Execute cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);exit;
        // Output response
        return $responseArray;


        // exit;
    }


    function DeleteUserDataTochat($userId,$role_id)
    {
            // print_r($postData);
            // exit;
    

        // curl --location --request DELETE 'http://localhost:4000/api/v1/user/delete?ref_userId=200&parent_id=60&role=3'
        // --header 'x-api-key: c3d2118b7bb3d73f'

        // $localDomain = 'http://************:4000';
        // $liveDomaein = 'https://chatuserservice.probizca.net';

        // Endpoint URL
        $endpoint = CHAT_USER_SERVICE.'/api/v1/user/delete?ref_userId='.$userId.'&role='.$role_id;

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY;

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: '.$API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        

        // Execute cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);
        // Output response
        return $responseArray;


        // exit;
    }


     function SendDeviceTokenToChatApp($postData)
    {
        // print_r($postData);
        // exit;

        $endpoint = CHAT_DOMAIN_BACKEND . '/api/v1/auth/updateFcm';
        // $endpoint = 'https://' . CHAT_DOMAIN_BACKEND . '/api/v1/auth/updateFcm';
       
        $API_KEY= CHAT_APIKEY;

        // Initialize cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: '.$API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // echo $ch;exit;

        // Execute cURL request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        // echo json_encode($responseArray);exit;
        // Output response
        return $responseArray;
    }


     function UpdatechatSetting($postData)
    {

        // print_r($postData);
        // exit;

        
        // Endpoint URL
        $endpoint = CHAT_DOMAIN_BACKEND . '/api/v1/client/chat';

        // Client Secret Key to be sent in the request body
        $API_KEY = CHAT_APIKEY; // CHAT_APIKEY_LOCAL CHAT_APIKEY

        // Initialize cURL session
        $ch = curl_init();
        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'x-api-key: ' . $API_KEY,
            'Content-Type: application/json'
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute cURL request
        $response = curl_exec($ch);


        // Check for errors
        if (curl_errno($ch)) {
            // If there's an error, return the error status and message
            $responseArray = array(
                'status' => 'Apierror',
                'message' => curl_error($ch)
            );
        } else {
            // If successful, return success status and response object
            $responseArray = array(
                'status' => 'Apisuccess',
                'response' => json_decode($response, true)
            );
        }

        // Close cURL session
        curl_close($ch);

        //  echo json_encode($responseArray);exit;
        // Output response
        return $responseArray;

        // exit;
    }


    function GetUserData($roleId)
    {
        $rows = "";
        $objDB = new clsDB();
        // Superadmin user roleid 1
        if ($roleId == 1) {
            $sql = "SELECT 
            systemuser.systemUserId as userId,
            systemuser.firstName,
            systemuser.lastName,
            systemuser.email,
            systemuser.phoneNo as phone_no,
            systemuser.address,
            systemuser.imageName,
            1 as role_id
            FROM 
            systemuser";
        } else if ($roleId == 2) {
            // Business Owner roleid 2
            $sql = "SELECT 
                    e.employeeId as userId,
                    e.firstName,
                    e.lastName,
                    e.email,
                    e.phoneNo as phone_no,
                    e.address,
                    b.businessName,
                    b.businessId as parent_id,
                    e.imageName,
                    2 as role_id
                FROM 
                    business b
                INNER JOIN 
                    employee e ON b.businessId = e.businessId
                WHERE 
                    e.isPrimary = 1 or e.isPrimaryUser = 1";
        } else if ($roleId == 3) {
            // Employee roleid 3
            $sql = "SELECT 
                    e.employeeId as userId,
                    e.firstName,
                    e.lastName,
                    e.email,
                    e.phoneNo as phone_no,
                    e.address,
                    b.businessName,
                    b.businessId as parent_id,
                    e.imageName,
                    3 as role_id
                FROM 
                    business b
                INNER JOIN 
                    employee e ON b.businessId = e.businessId
                WHERE 
                    e.isPrimary = 0 AND e.isPrimaryUser = 0";
        } else if ($roleId == 4) {
            // Customer/Contact roleid 4
            $sql = "SELECT 
                    customer.customerId as userId,
                    customer.firstName,
                    customer.lastName,
                    customer.email,
                    customer.phoneNo as phone_no,
                    customer.address,
                    business.businessName,
                    business.businessId as parent_id,
                    businesscustomer.refId,
                    businesscustomer.refType,
                    businesscustomer.employeeId,
                    businesscustomer.affiliateId,
                    customer.imageName,
                    4 as role_id
                FROM 
                    businesscustomer
                INNER JOIN 
                    customer ON customer.customerId = businesscustomer.customerId
                INNER JOIN 
                    business ON business.businessId = businesscustomer.businessId
                WHERE 
                    business.businessId = businesscustomer.businessId AND customer.status = 1 
                ORDER BY 
                    businesscustomer.createdDate DESC";
        } else if ($roleId == 5) {
            // Affiliate roleid 5
            $sql = "SELECT 
                        affiliate.affiliateId as userId ,
                        affiliate.firstName,
                        affiliate.lastName,
                        affiliate.email,
                        affiliate.phoneNo as phone_no,
                        affiliate.address,
                        affiliate.imageName,
                        business.businessName, 
                        business.businessId as parent_id,
                        5 as role_id,
                        businessaffiliate.requestStatus 
                    FROM 
                        affiliate 
                    LEFT JOIN 
                        businessaffiliate ON affiliate.affiliateId=businessaffiliate.affiliateId
                    LEFT JOIN 
                        business ON businessaffiliate.businessId=business.businessId";
            // GROUP BY 
            //     businessaffiliate.businessId
            // echo $sql;exit;
        }


        // echo $sql;exit;		
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }


    function checkUserExists($userId,$role_id)
    {
        $rows = "";
        $objDB = new clsDB();

        $sql = "SELECT * FROM `userrolemanagement` WHERE userId = $userId AND role_id = $role_id ";
        // echo $sql;exit;
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;

    }

    function SaveAllUsers($userManagmentId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($userManagmentId > 0) {
            $sql = "UPDATE userrolemanagement SET 													
							userId = '" . addslashes($this->userId) . "',																
							firstName = '" . addslashes($this->firstName) . "',																
							lastName = '" . addslashes($this->lastName) . "',
							email = '" . addslashes($this->email) . "',																
							phone_no = '" . addslashes($this->phone_no) . "',																
							address = '" . addslashes($this->address) . "',																
							profileImagePath = '" . addslashes($this->profileImagePath) . "',																
							businessName = '" . addslashes($this->businessName) . "',																
							parent_id = '" . addslashes($this->parent_id) . "',																
							role_id = '" . addslashes($this->role_id) . "'															
							WHERE id= " . $userManagmentId;
            //  echo $sql;exit;										
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO userrolemanagement(userId,firstName,lastName,email,phone_no,address,profileImagePath,businessName,parent_id,role_id) 
								VALUES(								  				
									  '" . addslashes($this->userId) . "',								
									  '" . addslashes($this->firstName) . "',								
									  '" . addslashes($this->lastName) . "',																
									  '" . ($this->email) . "',								
									  '" . addslashes($this->phone_no) . "',								
									  '" . addslashes($this->address) . "',								
									  '" . addslashes($this->profileImagePath) . "',																
									  '" . addslashes($this->businessName) . "',																
									  '" . addslashes($this->parent_id) . "',																
									  " . $this->role_id . "				
									  )";
            // echo $sql;exit;  									  
            $userManagmentId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $userManagmentId;
    }

    
    function updateUserRelations($userId,$realtions,$role_id)
    {
        $row = "0";
		$objDB = new clsDB();
		$sql = "UPDATE userrolemanagement SET 									    														
		realtions = '".addslashes($realtions)."'
		WHERE userId= '".$userId."' And role_id ='".$role_id."'";
		// echo $sql;exit;	
		$objDB->ExecuteQuery($sql);	
		unset($objDB);
		return $row;
    }

    function DeleteUser($userId,$role_id)
    {
        $row = "0";
		$objDB = new clsDB();
		$sql = "UPDATE userrolemanagement SET 																							
		deleted_at = '".date('Y-m-d H:i:s')."'
		WHERE userId= '".$userId."' And role_id ='".$role_id."'";
		// echo $sql;exit;	
		$objDB->ExecuteQuery($sql);	
		unset($objDB);
		return $row;
    }


    function GetUserDatat($userId,$role_id)
    {
        $row = "";
		$objDB = new clsDB();
		$sql = "SELECT * FROM userrolemanagement 
		WHERE userId= '".$userId."' And role_id ='".$role_id."'";
		// echo $sql;exit;	
        $rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
    }

    function getUserDetails($role_id, $userId)
    {
        $rows = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM `userrolemanagement` WHERE role_id = $role_id AND userId = $userId";
        // echo $sql;exit;	
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
    }


    function getUserDetailsForCustomer($role_id, $userId)
    {
        $rows = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM `userrolemanagement` WHERE role_id = $role_id AND userId = $userId";
        // echo $sql;exit;	
        $rows = $objDB->GetResultset($sql);
        unset($objDB);
        return $rows;
    }


        function SaveUserFCMToken($tokenId)
    {
        $objDB = new clsDB();
        $sql = '';
        if ($tokenId > 0) {
            $sql = "UPDATE userfcmtokens SET 													
							businessId = '" . addslashes($this->businessId) . "',																
							userId = '" . addslashes($this->userId) . "',																
							roleId = '" . addslashes($this->roleId) . "',																
							fcmToken	 = '" .($this->fcmToken) . "',														
							device	 = '" .($this->device) . "',														
							updated_at = '" . (date("Y-m-d h:i:s")) . "'														
							WHERE tokenId= " . $tokenId;
            //  echo $sql;exit;	
            // address = '" . addslashes($this->address) . "',																
            // 				profileImagePath = '" . addslashes($this->profileImagePath) . "',									
            $objDB->ExecuteQuery($sql);
        } else {
            $sql = "INSERT INTO userfcmtokens(businessId,userId,roleId,fcmToken,device,created_at) 
								VALUES(								  				
									  '" . addslashes($this->businessId) . "',								
									  '" . addslashes($this->userId) . "',								
									  '" . addslashes($this->roleId) . "',								
									  '" . addslashes($this->fcmToken) . "',																
									  '" . ($this->device) . "',								
									 '" . date("Y-m-d h:i:s") . "'				    
									  )";
            // echo $sql;exit;  									  
            $tokenId = $objDB->ExecuteInsertQuery($sql);
        }

        unset($objDB);
        return $tokenId;
    }

    function checkUserFCMToken($businessId,$userId,$chatroleId,$device)
    {
        $row = "";
        $objDB = new clsDB();
        $sql = "SELECT * FROM userfcmtokens 
		WHERE businessId = $businessId  AND userId= $userId AND roleId = $chatroleId AND  device = $device";
        // echo $sql;exit;	
        $rows = $objDB->GetDataRow($sql);
        unset($objDB);
        return $rows;
        
    }

    public function batchUpdateUserRelations($userRelations, $role_id) {
        $objDB = new clsDB();
        $successCount = 0;
        
        // Use a transaction for better performance
        $objDB->ExecuteQuery("START TRANSACTION");
        
        try {
            foreach ($userRelations as $userId => $relations) {
                $jsonRelations = json_encode($relations);
                $sql = "UPDATE userrolemanagement SET 
                        realtions = '" . addslashes($jsonRelations) . "'
                        WHERE userId = '" . $userId . "' AND role_id = '" . $role_id . "'";
                
                $objDB->ExecuteQuery($sql);
                $successCount++;
            }
            
            $objDB->ExecuteQuery("COMMIT");
        } catch (Exception $e) {
            $objDB->ExecuteQuery("ROLLBACK");
            // Handle error
        }
        
        unset($objDB);
        return $successCount;
    }

    public function getAllExistingUsers($roleId) {
        $objDB = new clsDB();
        $sql = "SELECT userId FROM userrolemanagement WHERE role_id = '" . $roleId . "' AND deleted_at IS NULL";
        $result = $objDB->GetResultset($sql);
        unset($objDB);
        return $result;
    }

    public function batchSaveUsers($users) {
        $objDB = new clsDB();
        $insertValues = [];
        $successCount = 0;
        
        foreach ($users as $user) {
            $insertValues[] = "('" . 
                addslashes($user['userId']) . "', '" . 
                addslashes($user['firstName']) . "', '" . 
                addslashes($user['lastName']) . "', '" . 
                addslashes($user['email']) . "', '" . 
                addslashes($user['phone_no']) . "', '" . 
                addslashes($user['address']) . "', '" . 
                addslashes($user['profileImagePath']) . "', '" . 
                addslashes($user['businessName']) . "', " . 
                $user['role_id'] . ")";
        }
        
        if (!empty($insertValues)) {
            $sql = "INSERT INTO userrolemanagement(userId, firstName, lastName, email, phone_no, address, 
                    profileImagePath, businessName, role_id) VALUES " . implode(', ', $insertValues);
            
            $objDB->ExecuteQuery($sql);
             $successCount = count($insertValues);
        }
        
        unset($objDB);
        return $successCount;
    }
}
