<?php
	
	// require_once(__DIR__ . '/vendor/autoload.php');
	function delete_file($filename)
	{
		if(file_exists($filename))
		{
            return unlink($filename);
        }
	}
	function ConverToTz($time="")
    {   
		$fromTz=DEFAULT_SERVER_TIMEZONE;
		$toTz='America/Chicago';

        // timezone by php friendly values
        $date = new DateTime($time, new DateTimeZone($fromTz));
        $date->setTimezone(new DateTimeZone($toTz));
		
        $time= $date->format('H:i');
        return $time;
	}

	function ConvertToTimezone($datetime="",$convertDateFormat,$toTimezone,$dateOnly=0)
	{ 		
		if($dateOnly==1)
		{
			$convertDateFormat = $convertDateFormat;
			$time = date($convertDateFormat,strtotime($datetime));	
			return $time;
		}

		if($convertDateFormat=='d/m/Y' ||  $convertDateFormat=='m d,Y')
		{
			$DateFormat = 'Y-m-d h:i A';
			$convertDateFormat = $convertDateFormat.' h:i A';	
			$time = date($DateFormat,strtotime($datetime));
			$date = new DateTime($time, new DateTimeZone('Asia/Kolkata'));
		}else{			
			$convertDateFormat = $convertDateFormat.' h:i A';	
			$time = date($convertDateFormat,strtotime($datetime));
			$date = new DateTime($time, new DateTimeZone('Asia/Kolkata'));	
		}

		$date->setTimezone(new DateTimeZone($toTimezone));
		$time= $date->format($convertDateFormat);
		return $time;
	}
    

	// Create Short/tiny Url
	function getTinyUrl($URL)
	{
		
		$data = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		$randomNumber = substr(str_shuffle($data), 0, 8);
		$objDB = new clsDB();
		$isRandomNumber = $objDB->GetSingleColumnValueFromTable('redirecturls', 'redirectUrlId', 'redirectUrlId', $randomNumber);
		unset($objDB);
		if($isRandomNumber != '')
		{
			getTinyUrl($URL);
		}
		else
		{
			include_once('../class/clsSurvey.php');
			$objsurvey = new clsSurvey();
			$objsurvey->redirectUrlId = $randomNumber;		
			$objsurvey->redirectUrl = $URL;	
			$redirectUrlId = $objsurvey->SaveRedirectUrl();
			unset($objsurvey);
		}
		return $randomNumber;
	}


	function GenerateRandormNumber()
	{
		$number = mt_rand(1000,9999);
		return $number;
	}

	function DeleteDirectory($dirname) 
	{
		$dir_handle="";
       if (is_dir($dirname))
          $dir_handle = opendir($dirname);
       if (!$dir_handle)
          return false;
       while($file = readdir($dir_handle)) {
          if ($file != "." && $file != "..") {
             if (!is_dir($dirname."/".$file)){
				unlink($dirname."/".$file);
			 }
			 else{
				DeleteDirectory($dirname.'/'.$file); 
			 }   
          }
       }
	   closedir($dir_handle);
       @rmdir($dirname);
       return true;
    }	
	
	function DetectMobile()
	{
		if(preg_match('/(alcatel|amoi|android|avantgo|blackberry|benq|cell|cricket|docomo|elaine|htc|iemobile|iphone|ipad|ipaq|ipod|j2me|java|midp|mini|mmp|mobi|motorola|nec-|nokia|palm|panasonic|philips|phone|playbook|sagem|sharp|sie-|silk|smartphone|sony|symbian|t-mobile|telus|up\.browser|up\.link|vodafone|wap|webos|wireless|xda|xoom|zte)/i', $_SERVER['HTTP_USER_AGENT']))
			return true;

		else
			return false;
	}

	function GenerateRandomString($length = 10) 
	{
		return substr(str_shuffle(str_repeat($x='ABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
	}

	function CopyDirectory($source,$target) 
	{
		if ( is_dir( $source ) ) {
			@mkdir( $target );
			$d = dir( $source );
			while ( FALSE !== ( $entry = $d->read() ) ) {
				if ( $entry == '.' || $entry == '..' ) {
					continue;
				}
				$Entry = $source . '/' . $entry; 
				if ( is_dir( $Entry ) ) {
					copy_directory( $Entry, $target . '/' . $entry );
					continue;
				}
				copy( $Entry, $target . '/' . $entry );
			}

			$d->close();
		}else {
			copy( $source, $target );
		}

    }

	function GenerateRandomAlphaNumericNumber($digits=6)
	{
		$key = '';
		$keys = array_merge(range(0, 9), range('a', 'z'));

		for ($i = 0; $i < $digits; $i++) {
			$key .= $keys[array_rand($keys)];
		}

		return $key;
	}
	
	function GenerateRandomNumericNumber($digits=6)
	{
		$key = '';
		$keys = array_merge(range(0, 9), range(0, 9));

		for ($i = 0; $i < $digits; $i++) {
			$key .= $keys[array_rand($keys)];
		}

		return $key;
	}

	function EncodeQueryData($input)
	{
		return base64_encode($input);
	}
	
	function DecodeQueryData($input)
	{
		return base64_decode($input);
	}

	function ConvertDateIntoDBFormat($date="")
	{   
		return date('Y-m-d',strtotime($date));
	}	
	
	//Get Images For Project From here	
	function GetSystemUserImagePath($systemUserId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default-user.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/superadmin/".$systemUserId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/superadmin/".$systemUserId."/".$imageName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessImagePath($businessId,$imageName,$isRemoveBasePath=0)
	{
	    if($isRemoveBasePath)
		    $defaultImagePath = "../upload/default_store.png";
		else
		    $defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		
		if($imageName!="")
		{
		    if($isRemoveBasePath)
		        $checkImagePath = "../upload/business/".$businessId."/logo/".$imageName;			
			else
			    $checkImagePath = ROOT_PATH."/upload/business/".$businessId."/logo/".$imageName;			
			    
			if(file_exists($checkImagePath))
			{
			    if($isRemoveBasePath)
				    $defaultImagePath = "../upload/business/".$businessId."/logo/".$imageName;				
				else
				    $defaultImagePath = BASE_PATH."/upload/business/".$businessId."/logo/".$imageName;				
				    
			}
			// echo $defaultImagePath;exit; 
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessCoverImagePath($businessId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/coverimage/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/coverimage/".$imageName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessAlbumImagePath($businessAlbumId,$businessId,$albumImageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($albumImageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/album/".$businessAlbumId.'/'.$albumImageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/album/".$businessAlbumId.'/'.$albumImageName;					
			}
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessInfoGraphicImagePath($infographicDetailId,$businessInfographicId,$businessId,$infographicImageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($infographicImageName!="")
		{
			$defaultImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/'.$infographicImageName;			
			if(file_exists($defaultImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/'.$infographicImageName;					
			}
			else
			{
				$defaultImagePath = BASE_PATH."/upload/default_store.png";
			}
		}
		return $defaultImagePath;
		
	}	
	
	function GetBusinessAlbumGallaryImagePath($businessAlbumId,$businessId,$businessAlbumDetailsId,$albumImageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($albumImageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/album/".$businessAlbumId.'/gallary/'.$businessAlbumDetailsId.'/'.$albumImageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/album/".$businessAlbumId.'/gallary/'.$businessAlbumDetailsId.'/'.$albumImageName;					
			}
		}
		return $defaultImagePath;
		
	}
	function GetInfographicAlbumImagePath($businessInfographicId,$infographicDetailId,$businessId,$infoalbumdetailId,$infoalbumImageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($infoalbumImageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/infographicalbumdetails/'.$infoalbumdetailId.'/'.$infoalbumImageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/infographicalbumdetails/'.$infoalbumdetailId.'/'.$infoalbumImageName;					
			}
		}
		return $defaultImagePath;
		
	}
	function DeleteInfographicAlbumImage($businessInfographicId,$infographicDetailId,$infoalbumdetailId,$businessId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/infographicalbumdetails/'.$infoalbumdetailId.'/';			
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	function DeleteAlbumImage($businessId,$businessAlbumId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/probizca/album/".$businessAlbumId.'/';
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	function DeleteAlbumGallaryImage($businessAlbumId,$businessId,$businessAlbumDetailsId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/probizca/album/".$businessAlbumId.'/gallary/'.$businessAlbumDetailsId.'/';			
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	function GetBusinessProfileImagePath($businessId,$fileName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($fileName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/profileimage/".$fileName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/profileimage/".$fileName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessProfileVideoFilePath($businessId,$fileName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($fileName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/profilevideo/".$fileName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/probizca/profilevideo/".$fileName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessAboutImageImagePath($businessId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/aboutus/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/aboutus/".$imageName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	//employee module function
	function GetEmployeeImagePath($businessId,$employeeId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default-user.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/employee/".$employeeId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/employee/".$employeeId."/".$imageName;				
			}
		}
		return $defaultImagePath;

	}
	
	function DeleteEmployeeImage($businessId,$employeeId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/employee/".$employeeId."/";
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	//customer module function
	function GetCustomerImagePath($customerId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default-user.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/customer/".$customerId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/customer/".$customerId."/".$imageName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function DeletCustomerImage($customerId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/customer/".$customerId."/";
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	//affiliate module function
	function GetAffiliateImagePath($affiliateId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default-user.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/affiliate/".$affiliateId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/affiliate/".$affiliateId."/".$imageName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function DeletAffiliateImage($affiliateId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/affiliate/".$affiliateId."/";
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}

	function GetServiceImagePath($serviceId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default-user.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/service/".$serviceId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/service/".$serviceId."/".$imageName;				
			}
		}
		return $defaultImagePath;		
	} 
	
	function DeletServiceImage($serviceId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/service/".$serviceId."/";
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}

	// Survey module

	function GetSurveyHeaderImagePath($businessId,$surveyId,$imageName)
	{
		
		$defaultImagePath = BASE_PATH."/assets/images/default-companyLogo.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/survey/".$surveyId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/survey/".$surveyId."/".$imageName;				
			}
		}
		return $defaultImagePath;

	}

	function GetSurveySImagePath($businessId,$surveyId,$imageName)
	{
		
		$defaultImagePath = BASE_PATH."/assets/images/survey/survey.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/business/".$businessId."/survey/".$surveyId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/business/".$businessId."/survey/".$surveyId."/".$imageName;				
			}
		}
		return $defaultImagePath;

	}
	
	function GetStudentImagePath($studentId,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default-user.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/student/".$studentId."/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/upload/student/".$studentId."/".$imageName;				
			}
		}
		return $defaultImagePath;
		
	}
	
	function DeletStudentImage($studentId)
	{		
		$deleteFolderPath = ROOT_PATH."/upload/student/".$studentId."/";
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	function GetTemplatePreviewImage($templateName,$imageName)
	{
	    
		$defaultImagePath = BASE_PATH."/upload/default_store.png";
		
		if($imageName!="")
		{
			$checkImagePath = ROOT_PATH."/templates/".$templateName."/previewimages/".$imageName;			
			if(file_exists($checkImagePath))
			{
				$defaultImagePath = BASE_PATH."/templates/".$templateName."/previewimages/".$imageName;					
			}
		}
		return $defaultImagePath;
		
	}
	
	function GetBusinessWhiteLabelLogo()
	{
		// $defaultImagePath = BASE_PATH."/assets/images/logo-white.png";
        $defaultImagePath = BASE_PATH.'/assets/images/logo/logo-white.png';		
		return $defaultImagePath;
		
	}
	
	function DeleteInfographicImage($businessInfographicId,$businessId,$infographicDetailId,$infographicImageName)
	{
		if($infographicImageName!='')
		{
			$deleteFile = ROOT_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/'.$infographicImageName;																
			if(file_exists($deleteFile))
			{
				unlink($deleteFile);
			}
		}
		else
		{
			$deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/infographicdetails/'.$infographicDetailId.'/';																
			if(file_exists($deleteFolderPath))
		    DeleteDirectory($deleteFolderPath);	
		}
	}
	
	function DeleteInfographicImageFolder($businessId,$businessInfographicId)
	{
        $deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/probizca/infographic/".$businessInfographicId.'/';									
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	function DeleteBusinessFolder($businessId)
	{
        $deleteFolderPath = ROOT_PATH."/upload/business/".$businessId."/";									
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	// function getYoutubeEmbedUrl($url)
	// {
	// 	$youtube_id = '';
	// 	$shortUrlRegex = '/youtu.be\/([a-zA-Z0-9_-]+)\??/i';
	// 	$longUrlRegex = '/youtube.com\/((?:embed)|(?:watch))((?:\?v\=)|(?:\/))([a-zA-Z0-9_-]+)/i';

	// 	if (preg_match($longUrlRegex, $url, $matches)) {
	// 		$youtube_id = $matches[count($matches) - 1];
	// 	}

	// 	if (preg_match($shortUrlRegex, $url, $matches)) {
	// 		$youtube_id = $matches[count($matches) - 1];
	// 	}
	// 	return 'https://www.youtube.com/embed/' . $youtube_id ;
	// }

	function getYoutubeEmbedUrl($url)
	{
		$youtube_id = '';
		$shortUrlRegex = '/youtu.be\/([a-zA-Z0-9_-]+)\??/i';
		$longUrlRegex = '/youtube.com\/((?:embed)|(?:watch))((?:\?v\=)|(?:\/))([a-zA-Z0-9_-]+)/i';
		$shortsUrlRegex = '/youtube.com\/shorts\/([a-zA-Z0-9_-]+)/i';

		if (preg_match($longUrlRegex, $url, $matches)) {
			$youtube_id = $matches[count($matches) - 1];
		}

		if (preg_match($shortUrlRegex, $url, $matches)) {
			$youtube_id = $matches[count($matches) - 1];
		}

		if (preg_match($shortsUrlRegex, $url, $matches)) {
			$youtube_id = $matches[count($matches) - 1];
		}

		return 'https://www.youtube.com/embed/' . $youtube_id;
	}
	
	function imageResize($imageSrc,$imageWidth,$imageHeight) 
	{

		$newImageWidth =502;
		$newImageHeight =127;

		$newImageLayer=imagecreatetruecolor($newImageWidth,$newImageHeight);
		imagecopyresampled($newImageLayer,$imageSrc,0,0,0,0,$newImageWidth,$newImageHeight,$imageWidth,$imageHeight);

		return $newImageLayer;
	}
	
	
	function GetInvoicePdfFilePath($fileName)
	{
	    
		$defaultFilePath = BASE_PATH."/upload/default_store.png";
		
		if($fileName!="")
		{
			$checkImagePath = ROOT_PATH."/upload/".$fileName;			
			if(file_exists($checkImagePath))
			{
				$defaultFilePath = BASE_PATH."/upload/".$fileName;				
			}
		}
		return $defaultFilePath;
		
	}
	function DeleteContactsImport()
	{	
		$deleteFolderPath = ROOT_PATH."/upload/business/Contacts/";			
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	function DeleteInvoicePdf($fileName)
	{
        $deleteFolderPath = ROOT_PATH."/upload/".$fileName.'/';	       	
		if(file_exists($deleteFolderPath))
		DeleteDirectory($deleteFolderPath);	
	}
	
	function GetTimeInLastSeen($time)
	{
		$seconds_ago = (time() - strtotime($time));

		if($seconds_ago >= 31536000) 
		{
			echo "Seen " . intval($seconds_ago / 31536000) . " Years Ago";
		} 
		elseif ($seconds_ago >= 2419200) 
		{
			echo "Seen " . intval($seconds_ago / 2419200) . " Months Ago";
		}
		elseif ($seconds_ago >= 86400) 
		{
			echo "Seen " . intval($seconds_ago / 86400) . " Days Ago";
		} 
		elseif ($seconds_ago >= 3600) 
		{
			echo "Seen " . intval($seconds_ago / 3600) . " Hours Ago";
		} 
		elseif ($seconds_ago >= 60) 
		{
			echo "Seen " . intval($seconds_ago / 60) . " Minutes Ago";
		} 
		else 
		{
			echo "Seen less than a minute ago";
		}
	}
	
	function GetTimeForToDo($time)
	{
		$seconds_ago = (time() - strtotime($time));

		if($seconds_ago >= 31536000) 
		{
			echo intval($seconds_ago / 31536000)." Years";
		} 
		elseif ($seconds_ago >= 2419200) 
		{
			echo intval($seconds_ago / 2419200)." Months";
		}
		elseif ($seconds_ago >= 86400) 
		{
			echo intval($seconds_ago / 86400)." Days";
		} 
		elseif ($seconds_ago >= 3600) 
		{
			echo intval($seconds_ago / 3600)." Hours";
		} 
		elseif ($seconds_ago >= 60) 
		{
			echo intval($seconds_ago / 60)." Minutes ";
		} 
		else 
		{
			echo "Minutes";
		}
	}
	function DeleteBusinessCoverImage($businessId,$coverImageName)
	{
		if($coverImageName!="")
		{
			$businessCoverImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/coverimage/".$coverImageName;			
			if(file_exists($businessCoverImagePath))
			{
				unlink($businessCoverImagePath);
			}
		}
	}
	function DeleteBusinessProfileImage($businessId,$profileImageName)
	{
		if($profileImageName!="")
		{
			$businessProfileImagePath = ROOT_PATH."/upload/business/".$businessId."/probizca/profileimage/".$profileImageName;						
			if(file_exists($businessProfileImagePath))
			{
				unlink($businessProfileImagePath);
			}
		}
	}
	
	function DeleteBusinessVideoImage($businessId,$profileVideoName)
	{
		if($profileVideoName!="")
		{
			$businessVideoImagePath= ROOT_PATH."/upload/business/".$businessId."/probizca/profilevideo/".$profileVideoName;						
			if(file_exists($businessVideoImagePath))
			{
				unlink($businessVideoImagePath); 
			}
		}
	}
	
	function get_percentage($total, $number)
    {
      if ( $total > 0 ) {
       return round(($number * 100) / $total, 2);
      } else {
        return 0;
      }
    }
	
	function generate_password()
	{
		$returnPassword = '';
		$digits = 6; // You can adjust the number of digits in the password
	
		// Generate a random number for each digit
		for ($i = 0; $i < $digits; $i++) {
			$returnPassword .= rand(0, 9); // Append a random digit (0-9)
		}
	
		return $returnPassword;
	}	

	function correctImageOrientation(string $UploadFilePath)
	{
		if(file_exists($UploadFilePath))
		{
			$destination_extension = strtolower(pathinfo($UploadFilePath, PATHINFO_EXTENSION));
			if(in_array($destination_extension, ["jpg","jpeg"]) && exif_imagetype($UploadFilePath) === IMAGETYPE_JPEG)
			{
				if(function_exists('exif_read_data'))
				{
					$exif = exif_read_data($UploadFilePath);
					if(!empty($exif) && isset($exif['Orientation']))
					{
						$orientation = $exif['Orientation'];
						switch ($orientation)
						{
							case 2:
								$flip = 1;
								$deg = 0;
								break;
							case 3:
								$flip = 0;
								$deg = 180;
								break;
							case 4:
								$flip = 2;
								$deg = 0;
								break;
							case 5:
								$flip = 2;
								$deg = -90;
								break;
							case 6:
								$flip = 0;
								$deg = -90;
								break;
							case 7:
								$flip = 1;
								$deg = -90;
								break;
							case 8:
								$flip = 0;
								$deg = 90;
								break;
							default:
								$flip = 0;
								$deg = 0;
						}
						$img = imagecreatefromjpeg($UploadFilePath);
						if($deg !== 1 && $img !== null)
						{
							if($flip !== 0)
							{
								imageflip($img,$flip);
							}
							$img = imagerotate($img, $deg, 0);
							imagejpeg($img, $UploadFilePath);
						}
					}
				}
			}
		}
	}


	function timeAgo($date) {
		$currentTime = time();
		$givenTime = strtotime($date);
	
		$timeDifference = $currentTime - $givenTime;
	
		$seconds = floor($timeDifference);
		$minutes = floor($seconds / 60);
		$hours = floor($minutes / 60);
		$days = floor($hours / 24);
		$months = floor($days / 30);
		$years = floor($months / 12);
	
		if ($years > 0) {
			return $years === 1 ? 'a year ago' : "$years years ago";
		} else if ($months > 0) {
			return $months === 1 ? 'a month ago' : "$months months ago";
		} else if ($days > 0) {
			return $days === 1 ? 'a day ago' : "$days days ago";
		} else if ($hours > 0) {
			return $hours === 1 ? 'an hour ago' : "$hours hours ago";
		} else if ($minutes > 0) {
			return $minutes === 1 ? 'a minute ago' : "$minutes minutes ago";
		} else {
			return $seconds < 5 ? 'just now' : "$seconds seconds ago";
		}
	}


	function GenerateUserAccessToken($UniqueId)
{
	$UniqueId = trim($UniqueId);
	$ENCRYPTION_KEY = '252-85DA2S3-ADSS5D-EI5B4A234';
	$secretKey = $ENCRYPTION_KEY.''.$UniqueId;
	return base64_encode($secretKey);
}

function ValidateUserAccessToken($UniqueId,$AccessToken)
{
	$UniqueId = trim($UniqueId);
	$ENCRYPTION_KEY = '252-85DA2S3-ADSS5D-EI5B4A234';
	$secretKey = $ENCRYPTION_KEY.''.$UniqueId;
	$secretKey = base64_encode($secretKey);
	$AccessToken = trim($AccessToken);
	
	if($secretKey == $AccessToken)
		return true;
	else
		return false;
	
}

// get access token from header
function getBearerToken() {
	$headers = getAuthorizationHeader();
	
	// HEADER: Get the access token from the header
	if (!empty($headers)) {
		if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
			return $matches[1];
		}
	}
	return null;
}

//Get header Authorization
function getAuthorizationHeader(){
	$headers = null;
	
	if (isset($_SERVER['Authorization'])) {
		$headers = trim($_SERVER["Authorization"]);
	}
	else if (isset($_SERVER['HTTP_AUTHORIZATION'])) { //Nginx or fast CGI
		$headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
	} elseif (function_exists('apache_request_headers')) {
		$requestHeaders = apache_request_headers();
		// Server-side fix for bug in old Android versions (a nice side-effect of this fix means we don't care about capitalization for Authorization)
		$requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
		//print_r($requestHeaders);
		if (isset($requestHeaders['Authorization'])) {
			$headers = trim($requestHeaders['Authorization']);
		}
	}
	return $headers;
}


function isBase64Image($data) {
    // Check if the data is a valid Base64 string
    if (!preg_match('/^data:image\/(\w+);base64,/', $data, $type)) {
        return false;
    }

    // Extract the Base64 data part
    $base64Data = substr($data, strpos($data, ',') + 1);

    // Decode the Base64 data
    $decodedData = base64_decode($base64Data, true);

    // Check if decoding was successful
    if ($decodedData === false) {
        return false;
    }

    // Check if the decoded data is a valid image
    $imageInfo = @getimagesizefromstring($decodedData);

    return $imageInfo !== false;
}


function getFileExtensionFromBase64($base64String) {
    // Regular expression to extract the MIME type
    $pattern = '/^data:(.*?);base64/';
    $matches = [];

    preg_match($pattern, $base64String, $matches);

    if (!isset($matches[1])) {
        throw new Exception('Invalid base64 string: MIME type not found');
    }

    $mimeType = $matches[1];

    // Map MIME type to file extension
    $mimeToExtension = [
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'image/gif' => 'gif',
        'image/bmp' => 'bmp',
        'image/webp' => 'webp',
        'image/svg+xml' => 'svg',
        // Add more MIME types and extensions as needed
    ];

    if (!array_key_exists($mimeType, $mimeToExtension)) {
        throw new Exception("Unsupported MIME type: $mimeType");
    }

    return $mimeToExtension[$mimeType];
}

// Send SMS

function sendSMS($mobileNum, $message)
{
	// Sanitize phone number: Remove any dashes from the mobile number
	$mobileNum = str_replace("-", "", $mobileNum);

	// Normalize phone number format: Add US country code (+1) if not already present
	if (strpos($mobileNum, '+') !== 0) {
		$mobileNum = '+1' . $mobileNum;
	}

	// Security check: Skip sending SMS to specific test/blocked number
	// This prevents accidental SMS charges to a particular number
	// if ($mobileNum != '+17244394900') {

		// Configure ClickSend API credentials
		// Note: Consider moving these credentials to environment variables for security
		$config = ClickSend\Configuration::getDefaultConfiguration()
			->setUsername(COLUMBIA_USERNAME)
			->setPassword(COLUMBIA_LONG);

		// Initialize the ClickSend SMS API client with HTTP client and configuration
		$apiInstance = new ClickSend\Api\SMSApi(new GuzzleHttp\Client(), $config);

		// Create a new SMS message object and set its properties
		$msg = new \ClickSend\Model\SmsMessage();
		$msg->setBody($message);                    // Set the message content
		$msg->setTo($mobileNum);                   // Set the recipient phone number (with country code)
		$msg->setSource("PHP");                    // Set the source identifier for tracking
		$msg->setFrom(COLUMBIA_NUMBER);

		// Note: The following commented lines are example usage for reference
		// $msg->setBody('Hello from ClickSend!');
		// $msg->setTo('+5926881947');   // Include country code

		// Create a message collection to hold the SMS message(s)
		// ClickSend API supports sending multiple messages in one request
		$collection = new \ClickSend\Model\SmsMessageCollection();
		$collection->setMessages([$msg]);

		// Attempt to send the SMS message via ClickSend API
		try {
			$result = $apiInstance->smsSendPost($collection);
			// Output the API response for debugging/logging purposes
			// Consider replacing with proper logging in production
			// print_r($result);exit;
		} catch (Exception $e) {
			// Handle and display any errors that occur during SMS sending
			// Consider implementing proper error logging instead of echo
			// echo "Error sending SMS: ", $e->getMessage(), "\n";exit;
		}
	// }
}


	
	
?>
