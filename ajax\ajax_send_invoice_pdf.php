<?php

    //include files and classes
	include('../includes/config.php');
    include('../includes/commonfun.php');
    include('../class/clsDB.php');     	
	include('../mpdf/mpdf.php');
    include('../class/clsInvoice.php');
    include('../class/clsInvoiceDetails.php');
    include('../class/clsEmployee.php');
    include('../class/clsCountryStateMaster.php');
    include('../class/clsBusiness.php');
	include('../class/class.phpmailer.php');
	include('../class/class.smtp.php');
    include('../class/clsSendEmails.php');
    include('../class/clsSystemUser.php');
    include('../class/clsPriceMaster.php');
	include('../class/clsNotification.php');
	include('../class/clsAddonMaster.php');
	include('../class/clsPushNotification.php');
	ini_set('display_errors', 0);
	
	if(isset($_GET['invoiceMasterId']))
	{
		//get value
		$invoiceMasterId = DecodeQueryData($_GET['invoiceMasterId']);	
		$businessId = DecodeQueryData($_GET['businessId']);	
		$systemUserId = DecodeQueryData($_GET['systemUserId']);	
		$loggedEmployeeId = $_GET['loggedEmployeeId'];
		
		//object
        $objInvoice = new clsInvoice();
		
		//get invoice
		$row = $objInvoice->GetInvoiceDetails($invoiceMasterId);
		if($row!='')
		{
			
			$invoiceNumber  = stripslashes($row['invoiceNumber']);          	           
			$companyName  = stripslashes($row['companyName']);          	           
			$firstName  = stripslashes($row['firstName']);          	           
			$lastName  = stripslashes($row['lastName']); 
			
			$InvoiceUserName =$firstName.' '.$lastName;
			$email1  = stripslashes($row['email']);          	           
			$pdfFooterLabel  = stripslashes($row['pdfFooterLabel']);          	           
			$mobileNo  = stripslashes($row['mobileNo']);          	           
			$billingDate  = (date("m/d/Y h:i:s",strtotime($row['billingDate'])));      
			$fromDate  = (date("m/d/Y h:i:s",strtotime($row['fromDate'])));      
			$toDate  = (date("m/d/Y h:i:s",strtotime($row['toDate'])));
			
			//invoice member info
			$invoiceMemberInfo = "Bill To:<br/>";

			if($companyName!=''){ $invoiceMemberInfo .= '<b>Company: </b> '.$companyName;}
			if($firstName!='' && $lastName!=''){ $invoiceMemberInfo .= '<br/><b>Name: </b> '.$firstName.' '.$lastName;}			
			if($email1!=''){ $invoiceMemberInfo .= '<br/>Email:</b> '.$email1;}
			if($mobileNo!=''){ $invoiceMemberInfo .= '<br/>Phone No:</b> '.$mobileNo;}			
			
			$gstinInfo = "";

			if($invoiceNumber!=''){ $gstinInfo .= '<b>Invoice No. :</b> '.$invoiceNumber;}
			if($billingDate!=''){ $gstinInfo .= '<br/>Invoice Date:</b> '.$billingDate;}
			if($email1!=''){ $gstinInfo .= '<br/>Email:</b> '.$email1;}
			if($mobileNo!=''){ $gstinInfo .= '<br/>Phone No:</b> '.$mobileNo;}
			if($invoiceNumber!=''){ $contactInfo .= '<br/>GSTIN:</b> '.$invoiceNumber;}
		}
		
		$probizcaLogo = BASE_PATH.'/superadmin/images/logo.png';
		
		//object
        $objEmployee = new clsEmployee();
		
		//get business details
		$objBusiness = new clsBusiness();			
		$rows =  $objBusiness->GetBusinessDetails($businessId);
		unset($objBusiness);
		

		//get image path
		$businessLogo  = GetBusinessImagePath($businessId, $rows['logoName'],1);
		
		//get employee details
		$businessOwnerFullName ='';
		if($businessId > 0)
		{
			$empRow = $objEmployee->GetBusinessOwnerDetails($businessId);
			unset($objEmployee);
			if($empRow!='')
			{
				$firstName  = stripslashes($empRow['firstName']);
				$lastName  = stripslashes($empRow['lastName']);
				$email  = stripslashes($empRow['email']);
				$phoneNo  = stripslashes($empRow['phoneNo']);
				$address  = stripslashes($empRow['address']);
				$city  = stripslashes($empRow['city']);
				$zipCode  = stripslashes($empRow['zipCode']);
				$stateId  = stripslashes($empRow['stateId']);
				$imageName  = stripslashes($empRow['imageName']);
				
				//business owner full name
				$businessOwnerFullName = $firstName.' '.$lastName;
				$objCountryStateMaster = new clsCountryStateMaster();
				
				//get state name
				$state  = $objCountryStateMaster->GetLocationName($stateId);
				$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
				$country = $objCountryStateMaster->GetLocationName($dbCountryId);
				
				$contactInfo = "";
				
				if($phoneNo!=''){ $contactInfo .= '<b>Phone No :</b> '.$phoneNo;}
				if($email!=''){ $contactInfo .= '<br><b>Email :</b> '.$email;}
				
				$fullAddress='';
				if($address!=''){ $fullAddress .= $address.',';}
				if($city!=''){ $fullAddress .= '<br> '.$city.',';}
				if($zipCode!=''){ $fullAddress .= ' '.$zipCode;}
				if($state!=''){ $fullAddress .= ' '.$state;}
				if($country!=''){ $fullAddress .= '<br>'.$country;}
			}
		}
		
		if($systemUserId > 0)
		{
			
			$objSystemUser = new clsSystemUser();

			//get SystemUser details
			$systemUserRow = $objSystemUser->GetSystemUserDetails($systemUserId);
			unset($objSystemUser);
			if($systemUserRow!='')
			{
				$firstName  = stripslashes($systemUserRow['firstName']);
				$lastName  = stripslashes($systemUserRow['lastName']);
				$email  = stripslashes($systemUserRow['email']);
				$phoneNo  = stripslashes($systemUserRow['phoneNo']);
				$address  = stripslashes($systemUserRow['address']);
				$city  = stripslashes($systemUserRow['city']);
				$zipCode  = stripslashes($systemUserRow['zipCode']);
				$stateId  = stripslashes($systemUserRow['stateId']);
				$imageName  = stripslashes($systemUserRow['imageName']);
				$dbsystemUserRoleId  = stripslashes($systemUserRow['systemUserRoleId']);
				$isPrimary  = stripslashes($systemUserRow['isPrimary']);
				$userName  = stripslashes($systemUserRow['userName']);
				
				//systemuser full name
				$businessOwnerFullName = $firstName.' '.$lastName;
				$objCountryStateMaster = new clsCountryStateMaster();
				
				//get state name
				$state  = $objCountryStateMaster->GetLocationName($stateId);
				$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
				$country = $objCountryStateMaster->GetLocationName($dbCountryId);
				
				$contactInfo = "";
				
				if($phoneNo!=''){ $contactInfo .= '<b>Phone No :</b> '.$phoneNo;}
				if($email!=''){ $contactInfo .= '<br><b>Email :</b> '.$email;}
				
				$fullAddress='';
				if($address!=''){ $fullAddress .= $address.',';}
				if($city!=''){ $fullAddress .= '<br> '.$city.',';}
				if($zipCode!=''){ $fullAddress .= ' '.$zipCode;}
				if($state!=''){ $fullAddress .= ' '.$state;}
				if($country!=''){ $fullAddress .= '<br> '.$country;}
			}
		}
		
		$mpdf=new mPDF('c');	     
		$mpdf->mirrorMargins = 1; // Use different Odd/Even headers and footers and mirror margins
		$mpdf->defaultheaderfontsize = 10; /* in pts */
		$mpdf->defaultheaderfontstyle = B; /* blank, B, I, or BI */
		$mpdf->defaultheaderline = 1; /* 1 to include line below header/above footer */
		$mpdf->defaultfooterfontsize = 12; /* in pts */
		$mpdf->defaultfooterfontstyle = B; /* blank, B, I, or BI */
		$mpdf->defaultfooterline = 1; /* 1 to include line below header/above footer */		
		
		$mpdf->useOddEven = 0;
		$mpdf->SetHTMLHeader('
							<table width="100%" style="border-bottom: 1px solid #000000; font-family: serif; 
								font-size: 10pt; color: #000000; font-weight: bold;">
								<tr> 
									<td width="20%"><img src="'.$businessLogo.'" height="10%" width="10%"></td>
									<td width="40%" align="left">'.$businessOwnerFullName.'<br/>'.$fullAddress.'</td>
									<td width="35%" style="text-align: right;">'.$gstinInfo.'</td>
								</tr>								
							</table><br/>'); 

        $mpdf->SetHTMLFooter('
				<hr><table width="100%">
					<tr>
			
						<td width="95%" align="center">'.$pdfFooterLabel.'</td>
						<td width="5%" style="text-align: right;">{PAGENO}/{nb}</td>
					</tr>
				</table>');
	
		
		
		$mpdf->SetMargins(0, 0, 25); 
		
		
		//print invoice party details			
        $mpdfHtmls ='';	
        $mpdfHtmls .='<br/><table width="100%" style="font-family: serif; 
						font-size: 10pt; color: #000000; font-weight: bold;">
						<tr> 
							<td width="100%" align="left">'.$invoiceMemberInfo.'</td>									
						</tr>																		
					</table><br/>';		
							
		$mpdf->WriteHTML($mpdfHtmls);
		
        //print service details
		$invoices = $objInvoice->GetAllInvoiceDetails($invoiceMasterId);
        if($invoices!='')
		{	
            $mpdfHtmla ='<table width="100%" style="font-family: serif; 
								font-size: 10pt; color: #000000;" border="1" cellpadding="1" cellspacing="1">
								<tr> 
									<td width="20%" align="left"><b>Item/Service</b></td>	
									<td width="30%" align="left"><b>Description</b></td>	
									<td width="10%" align="center"><b>Price</b></td>
									<td width="10%" align="center"><b>Qty</b></td>									
									<td width="10%" align="center"><b>Rate</b></td>									
									<td width="10%" align="center"><b>Sales Tax</b></td>									
									<td width="10%" align="center"><b>Amount</b></td>																		
								</tr>																			
							</table>						
							';	
			$mpdf->WriteHTML($mpdfHtmla);
            $mpdfHtmlb = '';
            $objclsPriceMaster= new clsPriceMaster();			
			while($row = mysqli_fetch_assoc($invoices))
			{                                       	  
				$invoiceDetailId = stripslashes($row['invoiceDetailId']); 
				$invoiceMasterId = stripslashes($row['invoiceMasterId']); 
				// $serviceName = stripslashes($row['serviceName']); 
				$quantity = stripslashes($row['quantity']); 
				$amount = stripslashes($row['amount']); 
				$gstAmount = stripslashes($row['gstAmount']);
				$discountType = stripslashes($row['discountType']);														
				$discountAmount = stripslashes($row['discountAmount']);		
				$totalAmount = stripslashes($row['totalAmount']);		
				$gstChargableAmount = stripslashes($row['gstChargableAmount']);		
				$lessDiscountAmount = stripslashes($row['lessDiscountAmount']);		
				$totalPayableAmount = stripslashes($row['totalPayableAmount']);		
				$otherChargesAmount = stripslashes($row['otherChargesAmount']);		
				$chargesRemark = stripslashes($row['chargesRemark']);
				$priceMasterId = stripslashes($row['priceMasterId']);

				//get service name
				$priceRow = $objclsPriceMaster->GetPricingDetails($priceMasterId);
				
				if($priceRow!='')
				{
					$serviceName = stripslashes($priceRow['title']);
					$description = stripslashes($priceRow['description']);
					$price = stripslashes($priceRow['price']);
				}
				if(isset($row['serviceDescription']))
				$description  = stripslashes($row['serviceDescription']);
				$mpdfHtmlb .='
							<table width="100%" style="font-family: serif; 
								font-size: 10pt; color: #000000;" border="1" cellpadding="1" cellspacing="1">
								<tr> 
									<td width="20%" align="left">'.$serviceName.'</td>
									<td width="30%" align="left">'.$description.'</td>
									<td width="10%" align="right">$'.$price.'</td>
									<td width="10%" align="center">'.$quantity.'</td>									
									<td width="10%" align="right">$'.$amount.'</td>									
									<td width="10%" align="right">$'.$gstChargableAmount.'</td>									
									<td width="10%" align="right">$'.$totalAmount.'</td>																		
								</tr>																			
							</table>						
							';
							
			   				
			}
			$mpdf->WriteHTML($mpdfHtmlb);
		}			
		
		$totalAmont = 0;
		$discountAmont = 0;
		$gstAmont = 0;
		$totalAmont = $objInvoice->GetTotalInvoiceAmount($invoiceMasterId);
		$discountAmont = $objInvoice->GetTotalDiscountAmount($invoiceMasterId);
		$gstAmont = $objInvoice->GetTotalGSTAmount($invoiceMasterId);
		
		$mpdfHtmls1 .='<br/><table width="30%" align="right" style="font-family: serif; 
							font-size: 10pt; color: #000000; font-weight: bold;">
							<tr>
								<td>Discount : </td>
								<td align="right"> $'.$discountAmont.'</td>
							</tr>
							<tr>
								<td>Total Sales Tax: </td>
								<td align="right">$'.$gstAmont.'</td>

							</tr>
							<tr>
								<td>Total : </td>
								<td align="right">$'.$totalAmont.'</td>

							</tr>																						
						</table><br/>';		


		// print_r($mpdfHtmls1);exit;
							
		$mpdf->WriteHTML($mpdfHtmls1);
		
		//send pdf to the email
		    $fileName = $companyName.'.pdf';
			$uploaddir = "../upload/";
			$uploaddirPath = $uploaddir.$fileName;
			$emailAttachment = $mpdf->output($uploaddirPath,"S");
			// echo $emailAttachment;exit;
		if($systemUserId > 0) {
			// echo "Hello";exit;
			$objSendEmails = new clsSendEmails();
			$objSendEmails->SendInvoicePdf($email1,$emailAttachment,$fileName,$invoiceNumber,$billingDate,$InvoiceUserName); 
		}
		
		$objAddonMaster = new clsAddonMaster();
		$emailsmsCount = $objAddonMaster->GetPurchaseAddonDetailsInRow($businessId);
		
		if($emailsmsCount !='')
		{	

				// pks for Email / SMS
				$addondtlId_Email = $emailsmsCount['addondtlId_Email'];
				$addondtlId_Sms = $emailsmsCount['addondtlId_Sms'];	

				// EMAIL
				$defaultEmailCnt = $emailsmsCount['defaultCount'];
				$defaultEmailUsedCnt = $emailsmsCount['dfUsed'];
				$purchasedEmailCnt = $emailsmsCount['purchasedCount'];
				$purchaseUsedEmailCnt = $emailsmsCount['purUsed'];

				$remainEmailCount = ($defaultEmailCnt - $defaultEmailUsedCnt) + ($purchasedEmailCnt - $purchaseUsedEmailCnt) ;
				
				// SMS
				$defaultSMSCnt = $emailsmsCount['dfSMS'];
				$defaulSMSUsedCnt = $emailsmsCount['dfsmsU'];
				$purchasedSMSCnt = $emailsmsCount['SmsPurchaseCnt'];
				$purchaseUsedSMSCnt = $emailsmsCount['SmsPurUsed'];

				$remainSMSCount = ($defaultSMSCnt - $defaulSMSUsedCnt) + ($purchasedSMSCnt - $purchaseUsedSMSCnt) ;
			
			if($defaultEmailCnt > $defaultEmailUsedCnt)
			{
				$objSendEmails = new clsSendEmails();
				$objSendEmails->SendInvoicePdf($email1,$emailAttachment,$fileName,$invoiceNumber,$billingDate,$InvoiceUserName); 
					
				$objNotification = new clsNotification();
				$objNotification->businessId = $businessId;
				$objNotification->referenceId = $invoiceMasterId;
				$objNotification->type = 0;  
				$objNotification->userType = 0;  
				$objNotification->userId = 0;
				$objNotification->notificationType = 2;
				$objn = $objNotification->SaveNotificationLog();

				$defaultEmailUsedCnt = $defaultEmailUsedCnt + 1;
				$objAddonMaster->UpdateDefaultUsedCount($businessId,1,$defaultEmailUsedCnt,$addondtlId_Email);
				echo $EmailFlag = 1;

				if(($defaultEmailCnt * 80)/100 == $defaultEmailUsedCnt) 
				{
					// 80% EMAIL usage Notification
					AddonNotify80Percent($businessId,$employeeId,1);
				} 
				else if($defaultEmailCnt  == $defaultEmailUsedCnt) 
				{
					// EMAIL Limit Reached Notification
					addonLimitiReached($businessId,$employeeId,1);
				}
				
			}
			else if($purchasedEmailCnt > $purchaseUsedEmailCnt)
			{		
				$objSendEmails = new clsSendEmails();
				$objSendEmails->SendInvoicePdf($email1,$emailAttachment,$fileName,$invoiceNumber,$billingDate,$InvoiceUserName); 
					
				$objNotification = new clsNotification();
				$objNotification->businessId = $businessId;
				$objNotification->referenceId = $invoiceMasterId;
				$objNotification->type = 0;  
				$objNotification->userType = 0;  
				$objNotification->userId = 0;
				$objNotification->notificationType = 2;
				$objn = $objNotification->SaveNotificationLog();

				$purchaseUsedEmailCnt = $purchaseUsedEmailCnt + 1;
				$objAddonMaster->UpdatePurchaseUsedCount($businessId,1,$purchaseUsedEmailCnt,$addondtlId_Email);
				echo $EmailFlag = 1;

				if(($purchasedEmailCnt * 80)/100 == $purchaseUsedEmailCnt) // 80% EMAIL usage Notification
				{
					AddonNotify80Percent($businessId,$employeeId,1);

				}
				else if($purchasedEmailCnt  == $purchaseUsedEmailCnt)  // EMAIL Limit Reached Notification
				{
					addonLimitiReached($businessId,$employeeId,1);
				}
			}
			else
			{
				echo $EmailFlag = 0;exit;
			}
		}

		unset($objNotification);
		unset($objSendEmails);
			
	}

	function AddonNotify80Percent($businessId,$employeeId,$addonNameId)
    {
        $addonName = ($addonNameId == 1) ? "Email" : "SMS";
      
        // 80% EMAIL usage Notification
        $objPushNotification= new clsPushNotification(); 
        $objPushNotification->businessId = $businessId;		
        $objPushNotification->title = 'Your '.$addonName.' usage is at 80%';		
        $objPushNotification->description = 'You are reaching your '.$addonName.' limit for the month. For additional Email messages, you will need to upgrade your subscription plan.';		
        $objPushNotification->isSystemNotification = 1;				
        $objPushNotification->systemUserId = 0;				
        $objPushNotification->notificationDate = date("Y-m-d H:i:s");
        $retNotificationId=$objPushNotification->SaveNotification(0);
        if($retNotificationId != '')
            {
                $objPushNotification= new clsPushNotification();
                $objPushNotification->businessId = $businessId;	
                $objPushNotification->customerId = 0;	
                $objPushNotification->affiliateId = 0;	
                $objPushNotification->notificationId = $retNotificationId;	
                $objPushNotification->employeeId = $employeeId;	
                $retnotificationDetailId=$objPushNotification->SaveNotificationDetails(0);
            }
    }

    function addonLimitiReached($businessId,$employeeId,$addonNameId)
    {
        $addonName = ($addonNameId == 1) ? "Email" : "SMS";

        // EMAIL Limit Reached Notification
        $objPushNotification= new clsPushNotification();
        $objPushNotification->businessId = $businessId;		
        $objPushNotification->title = 'Your '.$addonName.' usage has reached it’s limit';		
        $objPushNotification->description = 'Your '.$addonName.' limit for the month has been surpassed. For additional Email messages, you will need to upgrade your subscription plan.';		
        $objPushNotification->isSystemNotification = 1;				
        $objPushNotification->systemUserId = 0;				
        $objPushNotification->notificationDate = date("Y-m-d H:i:s");
        $retNotificationId=$objPushNotification->SaveNotification(0);
        if($retNotificationId != '')
            {
                $objPushNotification= new clsPushNotification();
                $objPushNotification->businessId = $businessId;	
                $objPushNotification->customerId = 0;	
                $objPushNotification->affiliateId = 0;	
                $objPushNotification->notificationId = $retNotificationId;	
                $objPushNotification->employeeId = $employeeId;	
                $retnotificationDetailId=$objPushNotification->SaveNotificationDetails(0);
            }
    }
	exit;
?>