<?php
$useragent = $_SERVER['HTTP_USER_AGENT'];
$iPod = stripos($useragent, "iPod");
$iPad = stripos($useragent, "iPad");
$iPhone = stripos($useragent, "iPhone");
$Android = stripos($useragent, "Android");
$iOS = stripos($useragent, "iOS");
//-- You can add billion devices 

$DEVICE = ($iPod || $iPad || $iPhone || $Android || $iOS);
// echo 'hi';exit;
$marginTop = 'mt-2';
if ($DEVICE > 0) $marginTop = 'mt-3'; 
?>


<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous" />
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js" integrity="sha384-fbbOQedDUMZZ5KreZpsbe1LCZPVmfTnH7ois6mU1QK+m14rQ1l2bGBq41eYeM/fS" crossorigin="anonymous"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous" />
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js" integrity="sha384-fbbOQedDUMZZ5KreZpsbe1LCZPVmfTnH7ois6mU1QK+m14rQ1l2bGBq41eYeM/fS" crossorigin="anonymous"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css">
  <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>

  <!-- Hero Section CSS Start -->
  <?php if ($this->templateLayoutId == 1) { ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-1/hero-1.css" />

  <?php } elseif ($this->templateLayoutId == 2) { ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-2/hero-2.css" />

  <?php } elseif ($this->templateLayoutId == 3) { ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-3/hero-3.css" />

  <?php } elseif ($this->templateLayoutId == 4) { ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-4/hero-4.css" />

  <?php } elseif ($this->templateLayoutId == 5) { ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-5/hero-5.css" />

  <?php } elseif ($this->templateLayoutId == 6) {  ?>
    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-6/hero-6.css" />

  <?php } elseif ($this->templateLayoutId == 7) {  ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-7/hero-7.css" />

  <?php } elseif ($this->templateLayoutId == 8) {  ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-8/hero-8.css" />

  <?php } elseif ($this->templateLayoutId == 9) {  ?>

    <link rel="stylesheet" href="<?php echo BASE_PATH; ?>/templates/SystemDefault/layouts/hero-9/hero-9.css" />

  <?php }  ?>
  <!-- Hero Section CSS End -->


  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.css">
  <title>Document</title>
  <style>
    <?php if ($this->templateType == 'C') {
      include_once('assets/css/style.php');
    }

    ?>.card-header i {
      position: static !important;
    }

    /* calender css  */

    /* th.fc-widget-header {
      background-color: #ffffff !important;
      color: #000000 !important;
    } */

    .card-body {
      max-width: 100% !important;
      width: 100% !important;
      padding-bottom: 5px !important;
    }

    .fc .fc-row .fc-content-skeleton td {
      text-align: center !important;
      padding: 2px !important;
    }

    .fc td,
    .fc th {
      border: 0 !important;
    }

    .sentiment-wrapper>div>div {
      padding: 16px 0px 0 !important;
      margin: 0;
      border-radius: 10px;
    }

    .fc-view-container {
      border: none !important;
      margin-top: 2px;
      display: block;
      height: 100%;
    }

    .sentiment-wrapper {
      padding: 0 !important;
    }

    .fc button {
      display: inline-block;
      font-weight: 400;
      color: #6b6f82;
      text-align: center;
      vertical-align: middle;
      user-select: none;
      border: none;
      /* 
      background-color: <?php if (($this->calBgColor) != '') {
                          echo $this->calBgColor;
                        ?><?php
                        }

                          ?>;

      border: 1px solid <?php if (($this->calBgColor) != '') {
                          echo $this->calBgColor;
                        ?><?php
                        }

                          ?>; */
      padding: 0.5rem 0.5rem;
      /* font-size: .7rem; */
      font-size: 9px;
      border-radius: 0.25rem;
      transition: color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
      /* 
      color: <?php if (($this->calTextColor) != '') {
                echo $this->calTextColor;
              ?><?php
              }

                ?>;

      border-color: <?php if (($this->calBgColor) != '') {
                      echo $this->calBgColor;
                    ?><?php
                    }

                      ?>; */
    }

    .fc button:hover {
      color: #000 !important;
      background-color: transparent !important;
      border-color: transparent !important;
    }

    .fc-unthemed .fc-today {
      /* color: <?php if (($this->calBgColor) != '') {
                  echo $this->calBgColor;
                ?><?php
                }

                  ?>;

      background: <?php if (($this->calTextColor) != '') {
                    echo $this->calTextColor;
                  ?><?php
                  }

                    ?>; */

      color: #000 !important;
      background-color: transparent !important;
      border-color: transparent !important;
    }

    .fc-day-grid-event {
      min-height: 26px !important;
    }

    .fc-center {
      text-align: end;
    }

    .fc-toolbar {
      flex-direction: row;
    }

    .fc-unthemed .fc-row {
      border-color: #E4E5EC;
      height: auto !important;
    }

    .fc-row.fc-rigid .fc-content-skeleton {
      position: relative;
    }

    .fc-toolbar.fc-header-toolbar {
      margin-bottom: 1.5em;
      / margin-top: 1em;/
    }

    /* .fc-view-container {
      border-style: solid;

      border-width: <?php if (($this->calBorderThikness) != 'none') {
                      echo $this->calBorderThikness;
                    ?><?php
                    } else {
                      echo "none";
                    }

                      ?>;

      border-color: <?php if (($this->calBorderThikness) != 'none') {
                      echo $this->calBorderColor;
                    ?><?php
                    } else {
                      echo "transparent";
                    }

                      ?>;
    } */
    /* 
    .card-header i {
      top: 17px;
      position: absolute;
      right: 20px;
      font-size: 23px;
    } */

    .fc-right .fc-button-group {
      / background-color: #22388a;/
    }

    .fc button:not(:disabled):not(.disabled):active:focus,
    .fc button:focus {
      box-shadow: none !important;
    }

    th.fc-widget-header {
      /* background-color: <?php if (($this->calBgColor) != '') {
                              echo $this->calBgColor;
                            ?><?php
                            }

                              ?>;

      color: <?php if (($this->calTextColor) != '') {
                echo $this->calTextColor;
              ?><?php
              }

                ?>; */
    }


    /* .fc-day-grid-event {
      background-color: <?php if (($this->calEventBgColor) != '') {
                          echo $this->calEventBgColor;
                        ?><?php
                        }

                          ?> !important;
      border: none;
    } */

    /* .fc-event span {
      color: <?php if (($this->calEventTextColor) != '') {
                echo $this->calEventTextColor;
              ?><?php
              }

                ?> !important;
    } */

    .fc-event {
      line-height: 1.2;
    }

    /* .fc-unthemed .fc-row {
            min-height: 50px !important;
         } */

    .fc-day-grid-container {
      height: auto !important;
    }

    .color-picker-submit-btn {
      height: 32px;
      margin-right: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
    }


    @media screen and (max-width: 480px) {
      .fc button {
        padding: 0.3rem 0.5rem;
      }

      .fc-toolbar h2 {
        /* font-size: 1em; */
        font-size: 11px;
        margin-right: 5px;
      }

      th.fc-widget-header {
        font-size: .5rem !important;
      }

      .fc-day-top {
        font-size: .5rem !important;
      }

      .fc-title {
        font-size: .5rem !important;
      }

      .fc-day-grid-event.fc-h-event.fc-event.fc-not-start.fc-end {
        opacity: .65 !important;
        margin-left: 5px !important;
        padding: 3px !important;
        height: 11px;
        border-radius: 3px !important;
      }

      .fc-ltr .fc-h-event.fc-not-end {
        height: 11px;
        padding: 3px !important;
        margin-right: 5px;
        border-radius: 3px !important;
        margin-left: 5px !important;
      }

      .fc-event span {
        color: #FFF;
        vertical-align: text-top;
        font-weight: 300;
      }

      /* .fc-unthemed .fc-row {
               min-height: 40px !important;
            } */
    }

    @media screen and (min-width: 480px) {
      .fc-toolbar h2 {
        font-size: 18px;
      }

      th.fc-widget-header {
        font-size: .8rem !important;
      }

      .fc-day-top {
        font-size: .8rem !important;
      }

      .fc-day-grid-event.fc-h-event.fc-event.fc-not-start.fc-end {
        opacity: .65 !important;
        margin-left: 5px !important;
        padding: 5px !important;
        height: 15px;
        border-radius: 3px !important;
      }

      .fc-ltr .fc-h-event.fc-not-end {
        height: auto;
        margin-right: 5px;
        border-radius: 3px !important;
        margin-left: 5px !important;
      }

      .fc-event span {
        color: #FFF;
        vertical-align: text-top;
        font-weight: 300;
      }

      .fc-title {
        font-size: .7rem !important;
      }
    }

    @media screen and (max-width: 380px) {
      .mobile-padding {
        padding: 0;
      }

    }

    /*
      .responsive-font{
         font-size: 23px !important;
      } */

    .form-innersection {
      display: flex;
      justify-content: space-between;
      background-color: #ffffff !important;
      padding-right: 0;
    }

    @media screen and (max-width:430px) {
      .form-innersection {
        flex-direction: column;
        padding: auto;
      }
    }
  </style>

  <style>
    .sample-slider {
      width: auto;
      height: 100%;
      border: 1px solid #f1f1f1;
      min-height: 400px;
      border-radius: 20px;
      margin: 25px;
    }

    .swiper-wrapper {
      cursor: grab;
      transform: translate3d(-1590px, 0px, 0px);
      transition-duration: 0ms;
    }

    .sample-slider img {
      width: 100%;
      height: 100%;
      /* height: 200px; */
    }

    .swiper-button-next,
    .swiper-rtl .swiper-button-prev,
    .swiper-button-prev,
    .swiper-rtl .swiper-button-next {
      width: 20px;
      height: 20px;
      padding: 25px 15px;
      background-color: #283b916c;
      border-radius: 0;
      color: #fff;
    }

    .swiper-button-next:hover,
    .swiper-rtl .swiper-button-prev:hover,
    .swiper-button-prev:hover,
    .swiper-rtl .swiper-button-next:hover {
      background-color: #283B91;
    }

    .swiper-button-next {
      right: 0px;
    }

    .swiper-button-prev {
      left: 0px;
    }

    .swiper-button-next:after,
    .swiper-rtl .swiper-button-prev:after,
    .swiper-button-prev:after,
    .swiper-rtl .swiper-button-next:after {
      font-size: 15px;
    }

    .swiper-wrapper {
      height: fit-content;
    }

    .swiper-slide {
      /* width: 100% !important; */
      /* margin: auto; */
    }

    .form-section ul li {
      position: relative;
      display: inline-block;
      padding: 10px 15px;
      background-color: #F1F1F1;
      /* border-bottom: 0; */
      cursor: pointer;
      font-weight: 600;
      margin: 5px;
      border-radius: 20px;
      font-size: 14px;
      color: #000 !important;
    }
    .event-discription {
               max-height: 400px;
               overflow-y: auto;
         }

         @media screen and (max-width: 768px) {
               .event-discription {
                  max-height: 300px;
                  overflow-y: auto;
               }
         }
    
  </style>

</head>

<body>
  <input type="hidden" name="templateId" id="templateId" value="<?php echo ($this->templateId); ?>">
  <input type="hidden" name="previewtemplateId" id="previewtemplateId" value="<?php echo ($this->previewtemplateId); ?>">
  <input type="hidden" name="businessId" id="businessId" value="<?php echo ($this->businessId); ?>">
  <input type="hidden" name="templateCustomSettingId" id="templateCustomSettingId" value="<?php echo ($this->templateCustomSettingId); ?>">
  <input type="hidden" name="businessemployeeId" id="businessemployeeId" value="<?php echo ($this->businessemployeeId); ?>">
  <input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($this->employeeId); ?>">
  <input type="hidden" name="customerId" id="customerId" value="<?php echo ($this->refId); ?>">

  
  <div class="main-body" style="">
  
    <div class="main-container ">
      <div class="business-card" style="position:relative;">
        <?php if ($this->employeeId != $this->refId) { ?>
          <div class="login-icon-section" id="login-icon-section">
            <a href="<?php echo BASE_PATH; ?>/customer/index.html?businessId=<?php echo EncodeQueryData($this->businessId); ?>&id=<?php echo EncodeQueryData($this->refId); ?>" target="_blank" data-toggle="tooltip" title="Click here to login"><span><i class="fa-solid fa-right-to-bracket" style="color: #22388a;background-color: #ffffff;border-radius: 10px;font-size: 18px;padding: 10px;"></i></span></a>
          </div>
        <?php } ?>
        <!-- Hero Setion Start -->
        <!-- echo "Template Tyle - ".$this->templateType; -->
        <?php
        if ($this->templateLayoutId == 1) {
          include('layouts/hero-1/hero-1.php');
        } elseif ($this->templateLayoutId == 2) {
          include('layouts/hero-2/hero-2.php');
        } elseif ($this->templateLayoutId == 3) {
          include('layouts/hero-3/hero-3.php');
        } elseif ($this->templateLayoutId == 4) {
          include('layouts/hero-4/hero-4.php');
        } elseif ($this->templateLayoutId == 5) {
          include('layouts/hero-5/hero-5.php');
        } elseif ($this->templateLayoutId == 6) {
          include('layouts/hero-6/hero-6.php');
        } elseif ($this->templateLayoutId == 7) {
          include('layouts/hero-7/hero-7.php');
        } elseif ($this->templateLayoutId == 8) {
          include('layouts/hero-8/hero-8.php');
        } elseif ($this->templateLayoutId == 9) {
          include('layouts/hero-9/hero-9.php');
        }
        ?>

        <!-- Hero Setion End -->

        <!-- Dynamic Sections Start -->
        <div id="aspect-content">
          <!-- Gallary Section Start-->
          <?php if (count($this->org_ImageGallary) > 0) { ?>
            <div class="aspect-tab ">
              <div class="collapsible">
                <!-- <input id="item-18" type="checkbox" class="aspect-input" name="aspect">
                <label for="item-18" class="aspect-label"></label> -->

                <div class="aspect-content " style="<?php if ($this->templateType == 'C') { ?> background-color:<?php echo ($this->galleryTitleColor); ?>; <?php } ?>">
                  <div class="aspect-info ">
                    <i style="color:<?php if ($this->templateType == 'C') {
                                      echo ($this->galleryTitileTextColor);
                                    } else {
                                      echo '#000000';
                                    } ?>;" class="fa-brands fa-microsoft mobile-icon"></i>
                    <span class="aspect-name text-<?php if ($this->templateType == 'C') {
                                                    echo $this->galleryTextAlignment;
                                                  } ?>" style="color:<?php if ($this->templateType == 'C') {
                                                                                                                                                  echo ($this->galleryTitileTextColor);
                                                                                                                                                } ?>;font-style:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                  echo ($this->gallerySectionTextType);
                                                                                                                                                                                                                                                } ?>;font-family:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                                                                                                    echo ($this->gallerySectionTitleFontType);
                                                                                                                                                                                                                                                                                                                                                  } ?>;font-size:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                                                                                                                                                                                                        echo ($this->galleryTextSize);
                                                                                                                                                                                                                                                                                                                                                                                                                                                      } ?>;padding-right: 3em;">
                      <?php echo $this->businessGalleryTitle; ?>
                    </span>
                  </div>
                  <i class="fa-solid fa-chevron-down arrow-icon" style=""></i>

                </div>
              </div>
              <div class="content-tab">

                <div class="aspect-tab-content">
                  <div class="tabset">
                    <div class="tab-menu" style="padding: 8px 25px;">

                      <ul class="custom-scroll">
                        <?php
                        foreach ($this->org_ImageGallary as $key => $gallerys) { ?>
                          <li><a href="#" class="tab-a <?php if ($key == 0) {
                                                          echo "active-a";
                                                        } ?> gallery-a" data-id="tab-<?php echo $gallerys['businessAlbumId']; ?>" style="width: max-content;">
                              <?php echo ($gallerys['albumTitle']); ?>
                            </a></li>
                        <?php } ?>
                      </ul>

                    </div><!--end of tab-menu-->


                    <?php
                    $z = 1;
                    $gallerysLength = count($this->org_ImageGallary);
                    foreach ($this->org_ImageGallary as $key => $gallerys) {

                      $galleryDatatCount = Count($gallerys['albumImageGallary']);

                    ?>
                      <div class="tab <?php if ($key == 0) {
                                        echo "tab-active";
                                      } ?> gallery-tab" data-id="tab-<?php echo $gallerys['businessAlbumId']; ?>" style="">
                        <?php if ($galleryDatatCount == 1) { ?>
                          <div class="sentiment-wrapper">
                            <div>
                              <div>

                                <div class="opinion-header">
                                  <?php foreach ($gallerys['albumImageGallary'] as $gallerys1) {  ?>
                                    <div class="single-element">
                                      <?php if ($gallerys1['gallaryType'] == 1 || $gallerys1['gallaryType'] == '') { ?>
                                        <img src="<?php echo ($gallerys1['gallaryImagePath']);  ?>">
                                        <p style="padding: 23px 10px;font-size: 16px;text-align:center;">
                                          <?php echo ($gallerys1['gallaryImageTitle']); ?>
                                        </p>
                                      <?php } elseif ($gallerys1['gallaryType'] == 2) {
                                        if (strpos($gallerys1['gallaryURL'], 'youtube') !== false || strpos($gallerys1['gallaryURL'], 'youtu.be') !== false) {
                                          $finalgalleryURL = getYoutubeEmbedUrl($gallerys1['gallaryURL']);
                                        } else {
                                          $finalgalleryURL = $gallerys1['gallaryURL'];
                                        } ?>
                                        <div style="border-radius: 10px;  height: 500px;text-align: center;">
                                          <iframe src="<?php echo ($finalgalleryURL); ?>" style="width: 100%;height: 95%;"></iframe>
                                          <p style="padding: 23px 10px;font-size: 16px;text-align:center;">
                                            <?php echo ($gallerys1['gallaryImageTitle']); ?>
                                          </p>
                                        </div>
                                      <?php } elseif ($gallerys1['gallaryType'] == 3) { ?>
                                        <div style="border-radius: 10px;  height: 500px;text-align: center;">
                                          <iframe class="img-fluid img-border-style infographics-section-thickness" src="<?php echo ($gallerys1['gallaryImagePath']);  ?>" style="height:100%; width:100%; border-style:none;">
                                          </iframe>
                                        </div>
                                      <?php } ?>
                                    </div>
                                  <?php } ?>
                                </div>
                              </div>
                            </div>

                          </div>
                        <?php } elseif ($galleryDatatCount > 1) { ?>
                          <div class="swiper sample-slider">
                            <div class="swiper-wrapper">
                              <?php foreach ($gallerys['albumImageGallary'] as $gallerys1) {  ?>
                                <div class="swiper-slide">
                                  <?php if ($gallerys1['gallaryType'] == 1 || $gallerys1['gallaryType'] == '') { ?>
                                    <img src="<?php echo ($gallerys1['gallaryImagePath']);  ?>">
                                    <p style="padding: 23px 10px;font-size: 16px;text-align:center;">
                                      <?php echo ($gallerys1['gallaryImageTitle']); ?>
                                    </p>
                                  <?php } elseif ($gallerys1['gallaryType'] == 2) {
                                    if (strpos($gallerys1['gallaryURL'], 'youtube') !== false || strpos($gallerys1['gallaryURL'], 'youtu.be') !== false) {
                                      $finalgalleryURL = getYoutubeEmbedUrl($gallerys1['gallaryURL']);
                                    } else {
                                      $finalgalleryURL = $gallerys1['gallaryURL'];
                                    } ?>
                                    <div style="border-radius: 10px;  height: 500px;text-align: center;">
                                      <iframe src="<?php echo ($finalgalleryURL); ?>" style="width: 100%;height: 95%;"></iframe>
                                      <p style="padding: 23px 10px;font-size: 16px;text-align:center;">
                                        <?php echo ($gallerys1['gallaryImageTitle']); ?>
                                      </p>
                                    </div>
                                  <?php } elseif ($gallerys1['gallaryType'] == 3) { ?>
                                    <div style="border-radius: 10px;  height: 500px;text-align: center;">
                                      <iframe class="img-fluid img-border-style infographics-section-thickness" src="<?php echo ($gallerys1['gallaryImagePath']);  ?>" style="height:100%; width:100%; border-style:none;">
                                      </iframe>
                                    </div>
                                    <p style="padding: 23px 10px;font-size: 16px;text-align:center;">
                                      <?php echo ($gallerys1['gallaryImageTitle']); ?>
                                    </p>
                                    <div class="text-center <?php echo $marginTop; ?>">
                                      <button type="button" id="" class="btn btn-primary mb-2 isViewPdf" data-toggle="modal" data-target="#exampleModal" pdfsrc="<?php echo ($gallerys1['gallaryImagePath']);  ?>">View Pdf</button>
                                    </div>
                                  <?php } ?>

                                </div>
                              <?php } ?>

                            </div>
                            <!-- <div class="swiper-pagination"></div> -->
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                          </div>
                        <?php } ?>
                      </div>
                      <!--end of tab one-->
                    <?php
                      $z++;
                    } ?>



                  </div>
                  <!--end of container-->
                </div>
              </div>

            </div>
          <?php } ?>
          <!-- Gallary Section End-->

          <!-- Infographics Section Start-->
          <?php
          // echo "<pre>";
          // print_r($this->org_infographics);
          // exit;
          if (count($this->org_infographics) > 0) { ?>
            <?php foreach ($this->org_infographics as $gallery) { ?>
              <div class="aspect-tab ">
                <div class="collapsible">
                  <div class="aspect-content" style="background-color:<?php if ($this->templateType == 'C') {
                                                                        echo ($this->infoTitleColor);
                                                                      } ?>;">
                    <div class="aspect-info ">
                      <i style="color:<?php if ($this->templateType == 'C') {
                                        echo ($this->infoTitileTextColor);
                                      } else {
                                        echo '#000000';
                                      } ?>;" class="fa-regular fa-image mobile-icon"></i>
                      <span class="aspect-name text-<?php if ($this->templateType == 'C') {
                                                      echo $this->infoTextAlignment;
                                                    } ?>" style="color:<?php if ($this->templateType == 'C') {
                                                                          echo ($this->infoTitileTextColor);
                                                                        } ?>;font-style:<?php if ($this->templateType == 'C') {
                                                                                                                                                              echo ($this->infoSectionTextType);
                                                                                                                                                            } ?>;font-family:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                          echo ($this->infoSectionTitleFontType);
                                                                                                                                                                                                                                                        } ?>;font-size:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                                                                                                    echo ($this->infoTextSize);
                                                                                                                                                                                                                                                                                                                                                  } ?>;padding-right: 3em;">
                        <?php echo ($gallery['infographicTitle']); ?>
                      </span>
                    </div>
                    <i class="fa-solid fa-chevron-down arrow-icon" style=""></i>


                  </div>
                </div>
                <div class="content-tab">

                  <div class="aspect-tab-content">
                    <div class="tabset">
                      <div class="tab-menu" style="padding: 8px 25px;">
                        <?php if (!empty($gallery['infographicDetails'])) { ?>
                          <ul class="custom-scroll">

                            <?php foreach ($gallery['infographicDetails'] as $key => $infographicDetails1) {

                              $DataCount = count($infographicDetails1['InfographicAlbumDetails']);
                            ?>
                              <li><a href="#" style="width: max-content;" class="tab-a <?php if ($key == 0) {
                                                                                          echo "active-a";
                                                                                        } ?> info-a" data-id="tab-<?php echo ($infographicDetails1['infographicDetailId']); ?>">
                                  <?php echo ($infographicDetails1['title']); ?>
                                  <?php //echo count($infographicDetails1['InfographicAlbumDetails']); 
                                  ?>
                                </a></li>
                            <?php } ?>

                          </ul>
                        <?php } else { ?>
                          <h6 style="margin: 10px auto;margin-top: 10px;text-align: center;color:#666666">No Data Available</h6>
                        <?php } ?>
                      </div>
                      <!--end of tab-menu-->


                      <?php
                      if (!empty($gallery['infographicDetails'])) {
                        $x = 1;
                        $length = count($gallery['infographicDetails']);

                        foreach ($gallery['infographicDetails'] as $key => $infographicDetails1) {

                          $infoDataCount = count($infographicDetails1['InfographicAlbumDetails']);
                      ?>
                          <div class="tab <?php if ($key == 0) {
                                            echo "tab-active";
                                          } ?> info-tab" data-id="tab-<?php echo ($infographicDetails1['infographicDetailId']); ?>" style="">


                            <?php if ($infoDataCount == 1) { ?>
                              <div class="sentiment-wrapper">
                                <div>
                                  <div>
                                    <div class="opinion-header">

                                      <?php foreach ($infographicDetails1['InfographicAlbumDetails'] as $album) { ?>
                                        <div class="single-element">
                                          <?php
                                          if ($album['isPaidContent'] == 0 || ($album['isPaidContent'] == 1 && $album['isPaidContentForCustomer']) || (($this->isPrimary == 1) == 1)) {

                                            if ($album['infographicsAlbumType'] == 1 && $album['infographicAlbumImagePath'] != '') { ?>
                                              <img class="img-fluid img-border-style infographics-section-thickness" style="width: 100%;height: 100%;" src="<?php echo ($album['infographicAlbumImagePath']); ?>" itemprop="thumbnail" alt="Image description">
                                            <?php }
                                            if ($album['infographicsAlbumType'] == 3 && $album['infographicAlbumImagePath'] != '') { ?>
                                              <?php if (!$DEVICE) { ?>
                                                <div style="border-radius: 10px;  height: 500px;text-align: center;">
                                                  <iframe class="img-fluid img-border-style infographics-section-thickness" src="<?php echo ($album['infographicAlbumImagePath']); ?>" style="height:100%; width:100%; border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                        echo ($this->infographicBorderColor);
                                                                                                                                                                                                                                                      } ?>;border-width:
                                              <?php if ($this->templateType == 'C') {
                                                  echo ($this->infographicThickness);
                                                } ?>;">
                                                  </iframe>
                                                </div>
                                              <?php } ?>

                                              <div class="text-center <?php echo $marginTop; ?>">
                                                <button type="button" id="" class="btn btn-primary mb-2 isViewPdf" data-toggle="modal" data-target="#exampleModal" pdfsrc="<?php echo ($album['infographicAlbumImagePath']); ?>" style="background-color: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                            echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                                          } ?>;border: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                          echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                                                        } ?>;">
                                                  View Pdf
                                                </button>
                                              </div>

                                            <?php }
                                            if ($album['infographicsAlbumType'] == 2 && $album['videoURL'] != '') {
                                              if (strpos($album['videoURL'], 'youtube') !== false || strpos($album['videoURL'], 'youtu.be') !== false) {
                                                $infoURL = getYoutubeEmbedUrl($album['videoURL']);
                                              } else {
                                                $infoURL = $album['videoURL'];
                                              }
                                            ?>
                                              <div style="border-radius: 10px;  height: 500px;text-align: center;">

                                                <iframe class="img-border-style infographics-section-thickness" src="<?php echo ($infoURL); ?>" style="height:100%; width:100%;
                                              border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                echo ($this->infographicBorderColor);
                                                                              } ?>;border-width:
                                              <?php if ($this->templateType == 'C') {
                                                echo ($this->infographicThickness);
                                              } ?>;">
                                                </iframe>

                                              </div>

                                            <?php }
                                            if ($album['infographicsAlbumType'] == 4 && $album['infographicsAlbumURL'] != '') {
                                              if (strpos($album['infographicsAlbumURL'], 'youtube') !== false || strpos($album['infographicsAlbumURL'], 'youtu.be') !== false) {
                                                $infoURL = getYoutubeEmbedUrl($album['infographicsAlbumURL']);
                                              } else {
                                                $infoURL = $album['infographicsAlbumURL'];
                                              }

                                              $fileExtenstion = pathinfo($infoURL, PATHINFO_EXTENSION);

                                            ?>
                                              <?php if (!$DEVICE && $fileExtenstion == 'pdf') { ?>
                                                <div style="border-radius: 10px;  height: 500px;text-align: center;">

                                                  <iframe class="img-fluid img-border-style infographics-section-thickness" src="<?php echo ($infoURL); ?>" style="height:100%; width:100%;
                                              border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                echo ($this->infographicBorderColor);
                                                                              } ?>;border-width:
                                              <?php if ($this->templateType == 'C') {
                                                  echo ($this->infographicThickness);
                                                } ?>;">
                                                  </iframe>

                                                </div>
                                              <?php } ?>
                                              <?php if ($fileExtenstion == 'pdf') { ?>
                                                <div class="text-center <?php echo $marginTop; ?>">
                                                  <button type="button" id="" class="btn btn-primary mb-2 isViewPdf" data-toggle="modal" data-target="#exampleModal" pdfsrc="<?php echo ($infoURL); ?>" style="background-color: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                    echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                  } ?>;border: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                  echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                                } ?>;">
                                                    View Pdf
                                                  </button>
                                                </div>
                                            <?php
                                              }
                                            } ?>
                                            <?php if ($album['isDisplayTitle'] == 0) { ?>
                                              <h5 class="" style="margin: 0 auto; margin-top: 20px;text-align:center;margin-bottom: 10px;">
                                                <?php echo ($album['title']); ?>
                                              </h5>
                                            <?php } ?>
                                            <div style="height:auto; margin: 0 20px;" class="row mb-4  pl-0 pr-0 infoClass1">
                                              <span class="sangram" style="font-size:14px;">
                                                <p class="infoClass discFontSize" style="<?php if (!empty($matches)) {
                                                                                            echo 'color:red !important';
                                                                                          } ?> font-size:14px;">
                                                  <?php echo htmlspecialchars_decode($album['infodescription']); ?>
                                                </p>
                                              </span>
                                            </div>
                                          <?php } else { ?>

                                            <div class="text-center">
                                              <i aria-hidden="true" class="fa-solid fa-tty mt-3" style="color: white;font-size: 65px;background-color: black;padding: 3% 3% 3% 3%;margin-top: 10px;"></i>
                                              <h4 class="text-center " style="margin-top: 20px;">
                                                <b>Paid Content</b>
                                              </h4>
                                              <h5 class="text-center" style="color:<?php echo ($this->navigationArrowColor); ?>;font-style:<?php echo ($this->infoSectionTextType); ?>;font-family:<?php echo ($this->infoSectionTitleFontType); ?>;">
                                                <b>Contact Business Owner To View</b>
                                              </h5>
                                            </div>

                                          <?php } ?>



                                        </div>
                                      <?php } ?>

                                    </div>
                                  </div>
                                </div>
                              </div>

                            <?php } elseif ($infoDataCount > 1) { ?>
                              <div class="swiper sample-slider">
                                <div class="swiper-wrapper">
                                  <?php foreach ($infographicDetails1['InfographicAlbumDetails'] as $album) { ?>
                                    <div class="swiper-slide">
                                      <?php
                                      if ($album['isPaidContent'] == 0 || ($album['isPaidContent'] == 1 && $album['isPaidContentForCustomer']) || (($this->isPrimary == 1) == 1)) {

                                        if ($album['infographicsAlbumType'] == 1 && $album['infographicAlbumImagePath'] != '') { ?>
                                          <div style="width: 60%;height:fit-content;margin: 10px auto;margin-top: 30px;">
                                            <img class="img-fluid img-border-style infographics-section-thickness" style="width: 100%;height: 100%;object-fit:contain;" src="<?php echo ($album['infographicAlbumImagePath']); ?>" itemprop="thumbnail" alt="Image description">
                                          </div>
                                        <?php }
                                        if ($album['infographicsAlbumType'] == 3 && $album['infographicAlbumImagePath'] != '') { ?>
                                          <?php if (!$DEVICE) { ?>
                                            <div style="border-radius: 10px;  height: 500px;text-align: center;">

                                              <iframe class=" img-fluid img-border-style infographics-section-thickness" src="<?php echo ($album['infographicAlbumImagePath']); ?>" style="height:100%; width:100%; border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                      echo ($this->infographicBorderColor);
                                                                                                                                                                                                                                                    } ?>;border-width:
                                              <?php if ($this->templateType == 'C') {
                                                echo ($this->infographicThickness);
                                              } ?>;">
                                              </iframe>

                                            </div>
                                          <?php } ?>

                                          <div class="text-center <?php echo $marginTop; ?>">
                                            <button type="button" id="" class="btn btn-primary mb-2 isViewPdf" data-toggle="modal" data-target="#exampleModal" pdfsrc="<?php echo ($album['infographicAlbumImagePath']); ?>" style="background-color: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                        echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                                      } ?>;border: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                      echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                                                    } ?>;">
                                              View Pdf
                                            </button>
                                          </div>

                                        <?php }
                                        if ($album['infographicsAlbumType'] == 2 && $album['videoURL'] != '') {
                                          if (strpos($album['videoURL'], 'youtube') !== false || strpos($album['videoURL'], 'youtu.be') !== false) {
                                            $infoURL = getYoutubeEmbedUrl($album['videoURL']);
                                          } else {
                                            $infoURL = $album['videoURL'];
                                          }
                                        ?>
                                          <div style="border-radius: 10px;  height: 500px;text-align: center;">

                                            <iframe class="img-border-style infographics-section-thickness" src="<?php echo ($infoURL); ?>" style="height:100%; width:100%;
                                              border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                echo ($this->infographicBorderColor);
                                                                              } ?>;border-width:
                                              <?php if ($this->templateType == 'C') {
                                                echo ($this->infographicThickness);
                                              } ?>;">
                                            </iframe>

                                          </div>

                                        <?php }
                                        if ($album['infographicsAlbumType'] == 4 && $album['infographicsAlbumURL'] != '') {
                                          if (strpos($album['infographicsAlbumURL'], 'youtube') !== false || strpos($album['infographicsAlbumURL'], 'youtu.be') !== false) {
                                            $infoURL = getYoutubeEmbedUrl($album['infographicsAlbumURL']);
                                          } else {
                                            $infoURL = $album['infographicsAlbumURL'];
                                          }

                                          // echo  $infoURL;
                                          $fileExtenstion = pathinfo($infoURL, PATHINFO_EXTENSION);

                                        ?>

                                          <div style="border-radius: 10px;  height: 500px;text-align: center;">
                                            <iframe class="img-fluid img-border-style infographics-section-thickness" src="<?php echo ($infoURL); ?>" style="height:100%; width:100%;">
                                            </iframe>
                                          </div>
                                        <?php
                                        } ?>
                                        <?php if ($album['isDisplayTitle'] == 0) { ?>
                                          <h5 class="text" style="margin: 0 auto;margin-top: 20px;text-align:center;margin-bottom: 10px;">
                                            <?php echo ($album['title']); ?>
                                          </h5>
                                        <?php } ?>
                                        <div style="height:auto; margin: 0 20px;" class="row mb-4 pl-0 pr-0 infoClass1">
                                          <span class="sangram" style="font-size:14px;">
                                            <p class="infoClass discFontSize" style="<?php if (!empty($matches)) {
                                                                                        echo 'color:red !important';
                                                                                      } ?> font-size:14px;">
                                              <?php echo htmlspecialchars_decode($album['infodescription']); ?>
                                            </p>
                                          </span>
                                        </div>
                                      <?php } else { ?>
                                        <div class="text-center">
                                          <i aria-hidden="true" class="fa-solid fa-tty mt-3" style="color: white;font-size: 65px;background-color: black;padding: 3% 3% 3% 3%;margin-top: 10px;"></i>
                                          <h4 class="text-center " style="margin-top: 20px;">
                                            <b>Paid Content</b>
                                          </h4>
                                          <h5 class="text-center" style="color:<?php echo ($this->navigationArrowColor); ?>;font-style:<?php echo ($this->infoSectionTextType); ?>;font-family:<?php echo ($this->infoSectionTitleFontType); ?>;">
                                            <b>Contact Business Owner To View</b>
                                          </h5>
                                        </div>
                                      <?php } ?>

                                    </div>

                                  <?php } ?>

                                </div>
                                <!-- <div class="swiper-pagination"></div> -->
                                <div class="swiper-button-prev"></div>
                                <div class="swiper-button-next"></div>
                              </div>
                            <?php } else { ?>
                              <?php if ($infographicDetails1['isPaidContent'] == 0 || ($infographicDetails1['isPaidContent'] == 1 && $infographicDetails1['isPaidContentForCustomer']) || ($this->isPrimary == 1)) {
                                if ($infographicDetails1['infographicsTypes'] == '2' && ($infographicDetails1['videoUrlPath']) != '') {
                                  if (strpos($infographicDetails1['videoUrlPath'], 'youtube') !== false || strpos($infographicDetails1['videoUrlPath'], 'youtu.be') !== false) {
                                    $infovideoURL = getYoutubeEmbedUrl($infographicDetails1['videoUrlPath']);
                                  } else {
                                    $infovideoURL = $infographicDetails1['videoUrlPath'];
                                  }

                              ?>

                                  <div class="embed-responsive embed-responsive-16by9 abcd <?php echo $infovideoURL ?>" style="border-radius: 10px; height: 500px; border-style:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                  echo $borderStyle;
                                                                                                                                                                                } ?>;border-color:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                    echo ($this->infographicBorderColor);
                                                                                                                                                                                                  } ?>;border-width:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                                echo ($this->infographicThickness);
                                                                                                                                                                                                                                                                              } ?>;">

                                    <iframe class="img-border-style infographics-section-thickness" style="width: 100%;height: 100%;" src="<?php echo ($infovideoURL); ?>"></iframe>
                                  </div>
                                <?php }
                                if ($infographicDetails1['infographicsTypes'] == '1' && ($infographicDetails1['infographicImagePath']) != '') { ?>
                                  <img class="img-fluid img-border-style infographics-section-thickness" src="<?php echo ($infographicDetails1['infographicImagePath']); ?>" style="border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                      echo ($this->infographicBorderColor);
                                                                                                                                                                                                                    } ?>;border-width:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                        echo ($this->infographicThickness);
                                                                                                                                                                                                                                      } ?>;">
                                <?php }
                                if ($infographicDetails1['infographicsTypes'] == '3' && ($infographicDetails1['infographicImagePath']) != '') { ?>
                                  <?php if (!$DEVICE) { ?>
                                    <div class="embed-responsive embed-responsive-16by9" style="border-radius: 10px;  height: 500px;">
                                      <iframe class="img-border-style infographics-section-thickness" style="width: 100%;height: 100%;" src="<?php echo ($infographicDetails1['infographicImagePath']); ?>" style="border-style:none;border-color:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                    echo ($this->infographicBorderColor);
                                                                                                                                                                                                                                                  } ?>;border-width:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                      echo ($this->infographicThickness);
                                                                                                                                                                                                                                                                    } ?>;"></iframe>

                                    </div>
                                  <?php } ?>

                                  <div class="text-center <?php echo $marginTop; ?>">
                                    <button type="button" class="btn btn-primary isViewPdf" pdfsrc="<?php echo ($infographicDetails1['infographicImagePath']); ?>" style="background-color: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                              echo ($this->infoTitileTextColor);
                                                                                                                                                                                            } ?>;border: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                            echo ($this->infoTitileTextColor);
                                                                                                                                                                                                          } ?>;" data-toggle="modal" data-target="#exampleModal">
                                      View Pdf
                                    </button>
                                    <!-- <button type="button" class="btn btn-primary mb-2 isViewPdf" data-toggle="modal" pdfsrc="<?php echo ($infographicDetails1['infographicImagePath']); ?>" style="background-color: <?php echo ($this->infoTitileTextColor); ?>;border: <?php echo ($this->infoTitileTextColor); ?>;">
                                    View Pdf 1
                                  </button> -->
                                  </div>
                                <?php }
                                if ($infographicDetails1['infographicsTypes'] == '4' && ($infographicDetails1['infographicsURL']) != '') {
                                  if (strpos($infographicDetails1['infographicsURL'], 'youtube') !== false || strpos($infographicDetails1['infographicsURL'], 'youtu.be') !== false) {
                                    $infoURL = getYoutubeEmbedUrl($infographicDetails1['infographicsURL']);
                                  } else {
                                    $infoURL = $infographicDetails1['infographicsURL'];
                                  }
                                  $fileExtenstion = pathinfo($infoURL, PATHINFO_EXTENSION);
                                ?>
                                  <div class="embed-responsive embed-responsive-16by9" style="border-radius: 10px; height: 500px; border-style:<?php if ($this->templateType == 'C') {
                                                                                                                                                  echo $borderStyle;
                                                                                                                                                } ?>;border-color:<?php if ($this->templateType == 'C') {
                                                                                                                                                                    echo ($this->infographicBorderColor);
                                                                                                                                                                  } ?>;border-width:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                              echo ($this->infographicThickness);
                                                                                                                                                                                                                                            } ?>;">

                                    <?php if (!$DEVICE && $fileExtenstion == 'pdf') { ?>
                                      <iframe class="img-border-style infographics-section-thickness" style="width: 100%;height: 100%;" src="<?php echo ($infoURL); ?>"></iframe>
                                    <?php } else { ?>
                                      <iframe class="img-border-style infographics-section-thickness" style="width: 100%;height: 100%;" src="<?php echo ($infoURL); ?>"></iframe>

                                    <?php }
                                    if ($fileExtenstion == 'pdf') { ?>
                                      <div class="text-center <?php echo $marginTop; ?>">
                                        <button type="button" class="btn btn-primary mb-2 isViewPdf" data-toggle="modal" data-target="#exampleModal" pdfsrc="<?php echo ($infoURL); ?>" style="background-color: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                    echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                  } ?>;border: <?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                  echo ($this->infoTitileTextColor);
                                                                                                                                                                                                                                } ?>;">
                                          View Pdf
                                        </button>
                                      </div>
                                    <?php } ?>
                                  </div>
                                <?php } ?>

                                <?php
                                $string = $infographicDetails1['infographicDescription'];

                                $regex = "/<iframe|video\s*(.*)\>(.*)<\/iframe|video>/";
                                $code = preg_match($regex, $string, $matches);

                                if (preg_match('/<iframe.*src=\"(.*)\".*><\/iframe>/isU', $string)) {
                                  $counter = 1;
                                } else {
                                  $counter = 0;
                                }

                                $pattern = "/<(p|span|b|strong|i|u) ?.*>(.*)<\/(p|span|b|strong|i|u)>/"; // Allowed tags are: <p>, <span>, <b>, <strong>, <i> and <u>
                                preg_match($pattern, $string, $matches);


                                ?>
                                <?php if ($infographicDetails1['isDisplayTitle'] == '0') { ?>
                                  <h5 class="text" style="margin: 0 auto; margin-top: 20px;text-align:center;margin-bottom: 10px;">
                                    <?php echo ($infographicDetails1['title']); ?>
                                  </h5>
                                <?php }  ?>

                                <!-- margin: 0 14px; -->
                                <div style="height:auto;" class="row   pl-0 pr-0 <?php if ($counter == 1) { ?> embed-responsive embed-responsive-16by9 <?php } ?> infoClass1">
                                  <span class="sangram" style="font-size:14px;color:<?php echo ($this->infoTitileTextColor); ?>!important';">
                                    <p class="infoClass discFontSize" style="<?php if (!empty($matches)) {
                                                                                echo 'color:red !important';
                                                                              } ?>; font-size:14px;">
                                      <?php echo htmlspecialchars_decode($infographicDetails1['infographicDescription']); ?>
                                    </p>
                                  </span>
                                </div>
                                <!-- Inforgraphic Album Details code Start -->

                                <!-- Inforgraphic Album Details code End -->
                              <?php } else { ?>
                                <div class="text-center">
                                  <i aria-hidden="true" class="fa-solid fa-tty mt-3" style="color: white;font-size: 65px;background-color: black;padding: 3% 3% 3% 3%;margin-top: 10px;"></i>
                                  <h4 class="text-center " style="margin-top: 20px;">
                                    <b>Paid Content</b>
                                  </h4>
                                  <h5 class="text-center">
                                    <b>Contact Business Owner To View</b>
                                  </h5>
                                </div>
                              <?php } ?>
                            <?php }  ?>
                          </div>


                        <?php $x++;
                        } ?>
                        <!-- </div> -->
                      <?php } ?>
                      <!--end of tab one-->
                    </div>

                  </div>
                </div>
              </div>
            <?php } ?>
          <?php } ?>
          <!-- Infographics Section End-->
          <!-- calendar section start -->
          <?php if ($this->isDisplayCalendar > 0) { ?>
            <div class="aspect-tab">
              <div class="collapsible">

                <!-- <input id="item-2" type="checkbox" class="aspect-input" name="aspect" />
            <label for="item-2" class="aspect-label"></label> -->

                <div class="aspect-content" style="background-color:<?php if ($this->templateType == 'C') {
                                                                      echo ($this->calSectionColor);
                                                                    } ?>;">
                  <div class="aspect-info">
                    <!-- <div class="chart-pie positive">
                          <span class="chart-pie-count">33</span>
                          <div>
                              <div class="first-fill"></div>
                              <div class="second-fill" style="transform: rotate(119deg)"></div>
                          </div>
                      </div> -->
                    <i style="color:<?php if ($this->templateType == 'C') {
                                      echo ($this->calSectiontextColor);
                                    } else {
                                      echo '#000000';
                                    } ?>;" class="fa-regular fa-calendar-days mobile-icon"></i>
                    <span class="aspect-name text-<?php if ($this->templateType == 'C') {
                                                    echo $this->calSectionTextAlignment;
                                                  } ?>" style="color:<?php if ($this->templateType == 'C') {
                                                                        echo ($this->calSectiontextColor);
                                                                      } ?>;font-style:<?php if ($this->templateType == 'C') {
                                                                                                                                                                    echo ($this->calTextType);
                                                                                                                                                                  } ?> !important;font-family:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                          echo ($this->calTitleFontType);
                                                                                                                                                                                                                                                                        } ?>;font-size:<?php if ($this->templateType == 'C') {
                                                                                                                                                                                                                                                                                                                                                            echo ($this->calTextSize);
                                                                                                                                                                                                                                                                                                                                                          } ?>;padding-right: 3em;">Calendar</span>
                  </div>
                  <i class="fa-solid fa-chevron-down arrow-icon" style=""></i>

                </div>
              </div>
              <div class="content-tab">

                <div class="aspect-tab-content">
                  <div class="sentiment-wrapper">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-12 mobile-padding">
                          <div id="calendar"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          <?php } ?>
          <!-- calendar section End -->

          <!-- Forms section start -->
          <?php if (count($this->org_BusinessForms) > 0) { ?>
            <div class="aspect-tab ">
              <div class="collapsible">
                <div class="aspect-content" style="background-color:<?php if ($this->templateType == 'C') {
                                                                      echo ($this->formTitleColor);
                                                                    } ?>">
                  <div class="aspect-info">
                    <i style="color:<?php if ($this->templateType == 'C') {
                                      echo ($this->formTextColor);
                                    } else {
                                      echo '#000000';
                                    } ?>;" class="fa-solid fa-file-invoice mobile-icon"></i>

                    <span class="aspect-name text-<?php if ($this->templateType == 'C') {
                                                    echo ($this->formTitleTextAlignment);
                                                  } ?>" style="color:<?php if ($this->templateType == 'C') {
                                                                        echo ($this->formTextColor);
                                                                      } ?>;font-size:<?php if ($this->templateType == 'C') {
                                                                      echo ($this->formTextSize);
                                                                    } ?>;padding-right: 3em;">Forms</span>
                    <!-- font-family:<?php if ($this->templateType == 'C') {
                                        echo ($this->formTitleFontType);
                                      } ?>; font-style:<?php if ($this->templateType == 'C') {
                                                                                                                                  echo ($this->formTextType);
                                                                                                                                } ?>;-->
                  </div>
                  <i class="fa-solid fa-chevron-down arrow-icon" style=""></i>


                </div>
              </div>
              <div class="content-tab">

                <div class="aspect-tab-content">
                  <div class="sentiment-wrapper form-section">

                    <ul class="tab-ul" style="display: flex;flex-direction: row;flex-wrap: wrap;width: 100%;padding: 0 20px 10px;">
                      <?php foreach ($this->org_BusinessForms as $businessForm) { ?>
                        <li>
                          <a style="width: max-content; font-size: 16px; padding-right: 5px;" class="surveyPopup" href="<?php echo BASE_PATH; ?>/formpopup.html?id=<?php echo EncodeQueryData($businessForm['businessFormId']); ?>&slug=<?php echo ($this->businessSlug); ?>&businessId=<?php echo ($this->businessId); ?>&templateType=<?php echo ($this->templateType); ?>" class="tab-a" data-id="tab-1">
                            <span style="padding-right: 10px;"> <?php echo ($businessForm['formTitle']); ?> </span>
                            <span style="font-size: 15px;color: #000; padding-left: 10px; border-left: 2px solid #000000a8;">View</span>
                          </a>
                        </li>
                      <?php } ?>

                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <?php } ?>
          <!-- Forms section End -->
        </div>


        <?php if (!empty($this->org_socialMedia)) {
          $currentArray = $this->org_socialMedia; ?>
          <div style="padding: 0 20px;">
                <div class="divider"></div>
                <div class="spacer-sm"></div>
                <div class="footer-social-media">

                  <?php if ($currentArray['betterBusinessBureau'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['betterBusinessBureau']; ?>" target="_blank"> <i class="fa-solid fa-earth-americas" style="color: #0060F0;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['spotify'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['spotify']; ?>" target="_blank"> <i class="fa-brands fa-spotify" style="color: #1ed760;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['soundcloud'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['soundcloud']; ?>" target="_blank"> <i class="fa-brands fa-soundcloud" style="color: #ff7700 ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['circle'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['circle']; ?>" target="_blank"> <i class="fa-regular fa-circle" style="color: #506bf0 ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['facebook'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['facebook']; ?>" target="_blank"> <i class="fa-brands fa-facebook" style="color: #1877F2;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['youtube'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['youtube']; ?>" target="_blank"> <i class="fa-brands fa-youtube" style="color: #CD201F ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['instagram'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['instagram']; ?>" target="_blank"> <i class="fa-brands fa-square-instagram" style="background: linear-gradient(115deg, #f9ce34, #ee2a7b, #6228d7);-webkit-background-clip: text; background-clip: text;
                  color: transparent; font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['linkedin'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['linkedin']; ?>" target="_blank"> <i class="fa-brands fa-linkedin" style="color: #0A66C2;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['pinterest'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['pinterest']; ?>" target="_blank"> <i class="fa-brands fa-pinterest" style="color: #E60023 ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['reddit'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['reddit']; ?>" target="_blank"> <i class="fa-brands fa-reddit" style="color: #FF4500;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['snapshot'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['snapshot']; ?>" target="_blank"> <i class="fa-brands fa-square-snapchat" style="color: #FFFC00 ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['twitter'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['twitter']; ?>" target="_blank"> <i class="fa-brands fa-twitter" style="color: #1DA1F2;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['tumblr'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['youtube']; ?>" target="_blank"><i class="fa fa-youtube"></i></a>

                  <?php }
                  if ($currentArray['whatsapp'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['whatsapp']; ?>" target="_blank"> <i class="fa-brands fa-whatsapp" style="color: #25d366 ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['cashapp'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['cashapp']; ?>" target="_blank"> <i class="fa-solid fa-dollar-sign" style="color: #85BB65  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['paypal'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['paypal']; ?>" target="_blank"> <i class="fa-brands fa-paypal" style="background: linear-gradient(-120deg, #003087, #009cde, #012169);-webkit-background-clip: text; background-clip: text;color: transparent; font-size: 39px;"></i>
                    </a>

                  <?php }
                  if ($currentArray['square'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['square']; ?>" target="_blank"> <i class="fa-solid fa-circle-stop" style="color: #000  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['podcast'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['podcast']; ?>" target="_blank"> <i class="fa-solid fa-podcast" style="color: #000  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['hangout'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['hangout']; ?>" target="_blank"> <i class="fa-brands fa-weibo" style="color: #e7162d  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['joinme'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['joinme']; ?>" target="_blank"> <i class="fa-solid fa-link" style="color: #000  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['skype'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['skype']; ?>" target="_blank"> <i class="fa-brands fa-skype" style="color: #00aff0   ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['vimeo'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['vimeo']; ?>" target="_blank"> <i class="fa-brands fa-vimeo-v" style="color: #008CFF  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['zoom'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['zoom']; ?>" target="_blank"> <i class="fa-solid fa-video" style="color: #2D8CFF   ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['website'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['website']; ?>" target="_blank"> <i class="fa-solid fa-globe" style="color: #00a2ed   ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['google'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['google']; ?>" target="_blank"> <i class="fa-brands fa-square-google-plus" style="color: #4285F4 ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['zelle'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['zelle']; ?>" target="_blank"> <i class="fa-solid fa-z" style="color: #6d1fd4  ;font-size: 39px;"></i> </a>

                  <?php }
                  if ($currentArray['tikTokUrl'] != '') { ?>

                    <a class="media-icon" href="<?php echo $currentArray['tikTokUrl']; ?>" target="_blank"> <i class="fa-brands fa-tiktok" style="color: #000000 ;font-size: 39px;"></i> </a>

                  <?php } ?>




                </div>

              <div class="spacer-sm"></div>
              <div class="divider"></div>
              <!-- Dynamic Sections End -->
          </div>
          <?php  } ?>

          <div class="spacer-sm"></div>
          <div class="powered-by">
            <p>Powered by</p>
            <div class="powered-by-logo">
              <a href="https://www.probizca.com" target="_blank">
                <img src="[[BUSINESS_WHITE_LOGO]]" class="img-fluid" height="40" width="150">
              </a>
            </div>
          </div>
      </div>


    </div>
    <div>
      <div style=" <?php if ((isset($_SESSION["loggedEmployeeId"]) == $this->businessemployeeId)  && $_SESSION["isPrimary"] == 1 && $this->previewtemplateId == 0) { ?>  display:block;   <?php } else { ?> display:none; <?php } ?>">
        <div class="dropper-icon">
          <i class="fa-solid fa-eye-dropper" style="color: #000000;"></i>
        </div>
      </div>

      <?php if ($this->templateLayoutId == 1) {
        include('layouts/hero-1/hero-1-colorpicker.php');
      } elseif ($this->templateLayoutId == 2) {
        include('layouts/hero-2/hero-2-colorpicker.php');
      } elseif ($this->templateLayoutId == 3) {
        include('layouts/hero-3/hero-3-colorpicker.php');
      } elseif ($this->templateLayoutId == 4) {
        include('layouts/hero-4/hero-4-colorpicker.php');
      } elseif ($this->templateLayoutId == 5) {
        include('layouts/hero-5/hero-5-colorpicker.php');
      } elseif ($this->templateLayoutId == 6) {
        include('layouts/hero-6/hero-6-colorpicker.php');
      } elseif ($this->templateLayoutId == 7) {
        include('layouts/hero-7/hero-7-colorpicker.php');
      } elseif ($this->templateLayoutId == 8) {
        include('layouts/hero-8/hero-8-colorpicker.php');
      } elseif ($this->templateLayoutId == 9) {
        include('layouts/hero-9/hero-9-colorpicker.php');
      }
      ?>

    </div>
  </div>
 

  <!--share Modal -->
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="modal fade" id="modal1" role="dialog">
          <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
              <div class="modal-header">
                <h5>ProBizCa Sharing Via:</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
              </div>
              <div class="modal-body" style="padding:20px 29px;">

                <form class="form form-horizontal" id="serviceForm" data-parsley-validate method="post" action="<?php echo BASE_PATH; ?>/sharemessagesubmit.html" enctype="multipart/form-data">
                  <input type="hidden" name="shareType" id="shareType" value="<?php echo ($this->shareType); ?>">
                  <input type="hidden" name="slug" id="slug" value="<?php echo ($this->businessSlug); ?>">
                  <input type="hidden" name="businessName" id="businessName" value="<?php echo ($this->currentOrganizationDisplayname); ?>">
                  <input type="hidden" name="sharedMessage" id="sharedMessage" value="<?php echo strip_tags($this->sharedMessage); ?>">
                  <input type="hidden" name="businessId" id="businessId" value="<?php echo ($this->businessId); ?>">
                  <input type="hidden" name="employeeId" id="employeeId" value="<?php echo ($this->employeeId); ?>">
                  <input type="hidden" name="refId" id="refId" value="<?php echo ($this->refId); ?>">
                  <input type="hidden" name="refType" id="refType" value="<?php echo ($this->refType); ?>">
                  <input type="hidden" name="isPrimary" id="isPrimary" value="<?php echo ($this->isPrimary); ?>">
                  <input type="hidden" name="profileImageFilePath" id="profileImageFilePath" value="<?php echo ($this->profileImageFilePath); ?>">
                  <input type="hidden" name="referenceId" id="referenceId" value="<?php echo ($this->referenceId); ?>">
                  <input type="hidden" name="custId" id="custId" value="<?php echo ($this->custId); ?>">
                  
                  <input type="hidden" name="customerCount" id="customerCount" value="<?php echo ($this->customerCount); ?>">
                  <input type="hidden" name="contactLimit" id="contactLimit" value="<?php echo ($this->contactLimit); ?>">

                  <div class="row g-3 align-items-center mb-3 ">

                    <div class="flex col-12" style="text-align: end;display: flex;">

                      <div class="form-check" style="margin-right: 30px;">
                        <input class="form-check-input" type="radio" name="isEmail" value="3" id="isEmail" checked>
                        <label class="form-check-label" for="flexRadioDefault1">
                          Email
                        </label>
                      </div>
                      <div class="form-check" style="margin-right: 30px;">
                        <input class="form-check-input" type="radio" name="isEmail" value="1" id="isText">
                        <label class="form-check-label" for="flexRadioDefault2">
                          Text
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="isEmail" value="2" id="isQrCode">
                        <label class="form-check-label" for="flexRadioDefault2">
                          QR Code
                        </label>
                      </div>
                    </div>
                  </div>
                  <br />

                  <div class="form-group" id="qrcodeDiv">
                    <div class="row justify-content-center">
                      <div class="col-sm-12 text-center">
                        <!-- <img src="https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=<?php echo ($this->businessQrCodePath); ?>&choe=UTF-8" height="150" width="150" title="<?php echo ($this->businessQrCodePath); ?>" /> -->
                        <img src="<?php echo BASE_PATH; ?>/templates/SystemDefault/qrcode.html?s=qrl&d=<?php echo ($this->businessQrCodePath); ?>" height="150" width="150" title="<?php echo ($this->businessQrCodePath); ?>" />
                      </div>
                      <div class="col-sm-12 text-center">
                        <span>Scan this QR Code to connect with</span>
                      </div>
                      <div class="col-sm-12 text-center">
                        <h5>[[DISPLAY_NAME]]</h5>
                      </div>
                      <div class="col-sm-12 text-center">
                        <span>Create | Share | Grow</span>
                      </div>
                    </div>
                  </div>
                  <div class="form-group details">
                    <div class="row">
                      <div class="col-sm-4"><label for="contactFirstName"><span class="glyphicon glyphicon-user"></span>First Name <span style="color:red;">*</span></label></div>
                      <div class="col-sm-8">
                        <input type="text" name="contactFirstName" class="form-control" id="contactFirstName" placeholder="Enter First Name" oninput="charactersOnly(this.id);" required>
                        <span id="contactFirstNameSpan" style="color: red; font-size: 12px;"></span>
                      </div>

                    </div>
                  </div>
                  <div class="form-group details">
                    <div class="row">
                      <div class="col-sm-4"><label for="contactLastName"><span class="glyphicon glyphicon-user"></span>Last Name <span style="color:red;">*</span></label></div>
                      <div class="col-sm-8">
                        <input type="text" name="contactLastName" class="form-control" id="contactLastName" placeholder="Enter Last Name" oninput="charactersOnly(this.id);" required>
                        <span id="contactLastNameSpan" style="color: red; font-size: 12px;"></span>
                      </div>
                    </div>
                  </div>
                  <div class="form-group textDiv">
                    <div class="row">
                      <div class="col-sm-4">
                        <label for="contactphone"><span class="glyphicon glyphicon-eye-open"></span> Phone
                          Number <span style="color:red;">*</span></label>
                      </div>
                      <div class="col-sm-8">
                        <input type="tel" class="form-control phone" autocomplete="off" id="contactphone" name="contactphone" placeholder="Enter Phone Number">
                        <span id="contactphoneSpan" style="color: red; font-size: 12px;"></span>
                      </div>
                    </div>
                  </div>
                  <div class="form-group textDiv">
                    <div class="row">
                      <div class="col-sm-4">
                        <label for="contactemail"><span class="glyphicon glyphicon-eye-open"></span>
                          Email <span style="color:red;">*</span></label>
                      </div>
                      <div class="col-sm-8">
                        <input type="email" class="form-control" id="contactemail" name="contactemail" placeholder="Enter Email" pattern="^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$">
                        <span id="contactemailSpan" style="color: red; font-size: 12px;"></span>
                      </div>
                    </div>
                  </div>
                  <div class="form-group details">
                    <div class="row">
                      <div class="col-sm-4">
                        <label for="txtMessage"><span class="glyphicon glyphicon-eye-open"></span>
                          Message</label>
                      </div>
                      <div class="col-sm-8">
                        <!-- <textarea id="contactmessage" class="form-control summernote" rows='5' name="contactmessage" placeholder="Enter Message Here"><?php echo preg_replace("/<\/?(div|b|span)[^>]*\>/i", "", htmlspecialchars_decode($this->sharedMessage)); ?></textarea> -->
                        <textarea id="contactmessage" class="form-control summernote" rows='5' name="contactmessage" placeholder="Enter Message Here"><?php echo strip_tags($this->sharedMessage); ?></textarea>

                      </div>
                    </div>
                  </div>
                  <?php if($this->contactLimit == $this->customerCount){ ?>
                    <span class="text-danger"> Your contact enroll limit is over</span>
                  <?php } ?>
                  <button type="button" id="btnFormSave" class="btn btn-primary btn-block" <?php if($this->contactLimit == $this->customerCount){ ?> disabled <?php } ?>>
                    Submit
                  </button>


                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- add Modal -->
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="modal fade" id="addIcon" role="dialog">
          <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
              <div class="modal-header">
                <h5>ProBizCa Sharing To:</h5>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
              </div>
              <div class="modal-body" style="padding:30px 29px;">
                <div style="display: flex; justify-content: space-around;">
                  <button style="padding: 8px 20px;" type="button" class="btn btn-info"><i style="font-size: 22px; color: #fff;" class="fa-solid fa-house"></i></button>
                  <button style="padding: 8px 20px;" type="button" class="btn btn-info">
                    <i style="color: #ffffff; font-size: 22px" class="fa-solid fa-copy"></i>
                  </button>
                  <button style="padding: 8px 20px;" type="button" class="btn btn-info">
                    <i style="color: #ffffff; font-size: 22px" class="fa-solid fa-id-card"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


  <!-- Modal -->
  <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">View PDF</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <?php if ($DEVICE == 1) { ?>
            <!-- <iframe id="viewPdfModalForMobile" src="" frameBorder="0" scrolling="auto" height="100%" width="100%" style="height: 500px;"></iframe> -->
             <div id="pdfCanvasWrapper" style="width: 100%; height: 500px; overflow: auto;">
              <canvas id="pdfViewerCanvas" style="width: 100%;"></canvas>
            </div>

          <?php } else { ?>
            <!-- <iframe id="viewPdfModalForWeb" src="" frameBorder="0" scrolling="auto" height="100%" width="100%" style="height: 500px;"></iframe> -->
             <div id="pdfCanvasWrapper" style="width: 100%; height: 500px; overflow: auto;">
              <canvas id="pdfViewerCanvas" style="width: 100%;"></canvas>
            </div>
          <?php } ?>
        </div>
        <div class="modal-footer">
          <!-- <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button> -->
          <!-- <button type="button" class="btn btn-primary">Save changes</button> -->
        </div>
      </div>
    </div>
  </div>

  <!-- PDF Modal Start -->
  <!-- PDF Modal End -->

  <!-- Calendar Event Modal start  -->
  <div class="modal fade" id="eventModal" tabindex="-1" role="dialog" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
          <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" id="eventDetails">
          <!-- Event details will be displayed here -->
        </div>
      </div>
    </div>
  </div>
  <!-- Calendar Event Modal End  -->

<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>

  <script src="<?php echo BASE_PATH; ?>/superadmin/vendors/alertifyjs/alertify.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.js"></script>
  <!-- <script src="spectrum.min.js"></script> -->
  <script src="<?php echo BASE_PATH; ?>/templates/SystemDefault/assets/js/convert-color-name-strings-to-HEX-code.js"></script>
  <!-- <link rel="stylesheet" type="text/css" href="spectrum.min.css" /> -->

  <script>

    // character only validation
		function charactersOnly(id) {
			var element = document.getElementById(id);
			
			// Check for URL patterns first
			var urlPattern = /https?:\/\/[^\s]+|www\.[^\s]+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/gi;
			if (urlPattern.test(element.value)) {
				element.value = "";
				
				// Show error message for the specific field
				var errorSpanId = id.replace(/contact|txt/, 'contact') + 'Span';
				$('#' + errorSpanId).fadeIn().html("URLs are not allowed in this field.");
				setTimeout(function() {
					$('#' + errorSpanId).fadeOut('slow');
				}, 5000);
				
				return;
			}
			
			// Remove non-alphabetical characters
			element.value = element.value.replace(/[^a-zA-Z]/gi, "");
		}

    $(document).ready(function() {

      $('.slider-backdrop').css("display", 'none');
      $('.spinner').css("display", 'none');

      var primaryColor = $("#hidden-icon-bg").val();
      var headerBg = $("#hidden-bg").val();
      // Automatically change background color on page load (onload)
      changeBackgroundColor();
      changeHeaderBackgroundColor();

      // calender event //
      var isPublicUrl = 1;
      var businessId = $('#businessId').val();
      var calendar = $('#calendar').fullCalendar({
        header: {
          left: 'prev,next',
          center: 'title',
          right: 'month,basicWeek,basicDay'
        },
        buttonIcons: {
          prev: 'chevron-left',
          next: 'chevron-right'
        },
        buttonText: {
          today: 'Today',
          month: 'Month',
          week: 'Week',
          day: 'Day',
          list: 'list'
        },
        navLinks: true, // can click day/week names to navigate views
        editable: true,
        eventLimit: true,
        events: "<?php echo BASE_PATH; ?>/ajax/ajax_get_business_calendar_events.html?isPublicUrl=" + isPublicUrl + "&businessId=" + businessId,
        displayEventTime: false,
        slotLabelFormat: "HH:mm a",
        droppable: true,
        eventMouseover: function(data, event, view) {
          console.log(data);
          if (event.data == "task") {
            // alert("Task");
          }
          // if (data.end != null) {
          //   tooltip = '<div class="tooltiptopicevent" style="border-top: 5px solid transparent;top: -20px;font-size:13px;position: absolute;width:auto;color:white;height:auto;background:#1C2331;position:absolute;z-index:10001;padding:10px 10px 10px 10px ;  line-height: 100%;">' + '<b class="text-light bg-dark"><center>' + data.title + '</center></b><br> ' + '' + '<b>Description : </b> ' + ' ' + data.description + '</br>' + '<b>Start : </b> ' + ' ' + data.start.format('MM/DD/YYYY hh:mm A') + '</br>' + '<b>End : </b> ' + ' ' + data.end.format('MM/DD/YYYY hh:mm A') + '</div>';
          //   $("body").append(tooltip);
          // } else {
          //   tooltip = '<div class="tooltiptopicevent" style="border-top: 5px solid transparent;top: -20px;font-size:13px;position: absolute;width:auto;color:white;height:auto;background:#1C2331;position:absolute;z-index:10001;padding:10px 10px 10px 10px ;  line-height: 100%;">' + '<b class="text-light bg-dark"><center>' + data.title + '</center></b><br> ' + '' + '<b>Description : </b> ' + ' ' + data.description + '</br>' + '<b>Start : </b> ' + ' ' + data.start.format('MM/DD/YYYY hh:mm A') + '</br>' + '<b>End : </b> ' + ' ' + data.start.format('MM/DD/YYYY hh:mm A') + '</div>';
          //   $("body").append(tooltip);
          // }
          $(this).mouseover(function(e) {
            $(this).css('z-index', 10000);
            $('.tooltiptopicevent').fadeIn('500');
            $('.tooltiptopicevent').fadeTo('10', 1.9);
          }).mousemove(function(e) {
            $('.tooltiptopicevent').css('top', e.pageY + 10);
            $('.tooltiptopicevent').css('left', e.pageX + 20);
          });
        },
        eventDrop: function(event, dayDelta) {
          alert(event.start);
        },
        eventMouseout: function(data, event, view) {
          $(this).css('z-index', 8);
          $('.tooltiptopicevent').remove();
        },
        eventRender: function(event, element, view) {
          // element.html("<img src='http://localhost/probizca/upload/superadmin/1/PROFILE_LARGE.jpg?randId=79' width='25' height='28'>");
          if (event.allDay === 'true') {
            event.allDay = true;
          } else {
            event.allDay = false;
          }
        },
        eventClick: function(data, event, view) {
          $('#eventModalLabel').text(data.title);

          var eventDetailsHTML = '';

          // if (data.start != null) {
          //   eventDetailsHTML += '<p><strong>Start:</strong> ' + data.start.format('MM/DD/YYYY hh:mm A') + '</p>';
          // }

          if (data.end != null) {
            eventDetailsHTML += '<p><strong>Start:</strong> ' + data.start.format('MM/DD/YYYY hh:mm A') + '</p>' + '<p><strong>End:</strong> ' + data.end.format('MM/DD/YYYY hh:mm A') + '</p>';
          } else {
            eventDetailsHTML += '<p><strong>Start:</strong> ' + data.start.format('MM/DD/YYYY hh:mm A') + '</p>' + '<p><strong>End:</strong> ' + data.start.format('MM/DD/YYYY hh:mm A') + '</p>';
          }

          if (data.description) {
            eventDetailsHTML += '<p  class="event-discription"><strong>Description:</strong> ' + data.description + '</p>';
          }

          if (data.url) {
            eventDetailsHTML += '<p><strong>URL:</strong> ' + data.url + '</p>';
          }

          $('#eventDetails').html(eventDetailsHTML);
          $('#eventModal').modal('show');
        }

        //   eventClick: function(calEvent, jsEvent, view) {
        //   // Handle the event click here
        //   $('#eventModalLabel').text(calEvent.title);
        //   $('#eventDetails').html(
        //     '<p><strong>Description:</strong> ' + calEvent.description + '</p>' +
        //     '<p><strong>Start:</strong> ' + calEvent.start.format('MM/DD/YYYY hh:mm A') + '</p>' +
        //     '<p><strong>End:</strong> ' + calEvent.end.format('MM/DD/YYYY hh:mm A') + '</p>'
        //   );
        //   $('#eventModal').modal('show');
        // }

      });

      $('#external-events .fc-event').each(function() {
        // Different colors for events
        $(this).css({
          'backgroundColor': $(this).data('color'),
          'borderColor': $(this).data('color')
        });
      });

      var colorData;
      $('#external-events .fc-event').mousemove(function() {
        colorData = $(this).data('color');
      })

      // calender end //

      // Tab Section JS //

      $('.gallery-a').click(function() {
        $(".gallery-tab").removeClass('tab-active');
        $(".tab[data-id='" + $(this).attr('data-id') + "']").addClass("tab-active");
        $(".gallery-a").removeClass('active-a');
        $(this).parent().find(".gallery-a").addClass('active-a');
      });

      $('.info-a').click(function() {
        $(".info-tab").removeClass('tab-active');
        $(".tab[data-id='" + $(this).attr('data-id') + "']").addClass("tab-active");
        $(".info-a").removeClass('active-a');
        $(this).parent().find(".info-a").addClass('active-a');
      });


      //   $('.custom-scroll').on('click', '.info-a', function () {
      //     const dataId = $(this).attr('data-id');
      //     var a =  $(this);
      //     // console.log('a'+a);
      //     // Deactivate the currently active tab content section
      //     $('.info-tabs').find('.info-tab').removeClass('tab-active');

      //     // var id =$('.info-tabs').attr('info-tabsId')
      //     // alert(id)

      //     // Activate the corresponding tab content section
      //     $(".info-tab[data-id='" + dataId + "']").addClass("tab-active");

      //     // Deactivate all links within the .custom-scroll container
      //     $(this).closest('.custom-scroll').find('.info-a').removeClass('active-a');

      //     // Activate the clicked link
      //     $(this).addClass('active-a');

      // });

      //check email already exist
      $("#contactemail").blur(function() {
        // alert('hi');
        var email = $("#contactemail").val();
        var businessId = $("#businessId").val();
        var contactemailSpan = $("#contactemailSpan");
        var pattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

        if (pattern.test(email)) {

          $.ajax({
            type: "GET",
            url: "<?php echo BASE_PATH; ?>/ajax/ajax_check_duplicate_customer_email.html",
            data: {
              email: email,
              businessId: businessId
            },
            success: function(data) {
              if (data > 0) {
                // Swal.fire({
                //   type: "danger",
                //   title: 'Email already exist!',
                //   confirmButtonClass: 'btn btn-danger',
                // })
                contactemailSpan.text('Email already exist!');
                $("#contactemail").val('');

                setTimeout(function() {
                  $('#contactemailSpan').fadeOut('slow')
                }, 5000);
                $("#contactemail").val('').focus();
                $("#btnSave").removeAttr("disabled", true);
              }
            }
          });
        } else {
          contactemailSpan.text('Please enter a valid email address');
          $("#contactemail").val('');

          setTimeout(function() {
            $('#contactemailSpan').fadeOut('slow')
          }, 5000);
        }
      });

    });

    // Step 1: Define the color value in a JavaScript variable
    // var primaryColor = "#227AFF";
    // var headerBg = "rgba(34, 122, 255, 0.07)";

    /* check color darkness */
    function isTooDark(hexcolor) {
      var r = parseInt(hexcolor.substr(1, 2), 16);
      var g = parseInt(hexcolor.substr(3, 2), 16);
      var b = parseInt(hexcolor.substr(4, 2), 16);
      var yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
      // alert(yiq)
      // Return new color if to dark, else return the original
      return (yiq < 120) ? '#ffffff' : '#191919';
    }

    /* check color darkness End */

    $("#saveButton").on('click', function() {
      var headerBg = $("#hidden-bg").val();
      var themeButtonColor = $("#hidden-icon-bg").val();

      const hexColorPattern = /^#([0-9A-Fa-f]{6})$/;



      Swal.fire({
        title: 'Confirmation',
        text: "Continue with Changes?",
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ok',
        confirmButtonClass: 'btn btn-outline-primary btn-glow',
        cancelButtonClass: 'btn btn-outline-danger btn-glow ml-1',
        buttonsStyling: false,
      }).then(function(result) {
        if (result.value) {


          if (!hexColorPattern.test(headerBg)) {
            headerBg = colourNameToHex(headerBg);
          }
          if (!hexColorPattern.test(themeButtonColor)) {
            themeButtonColor = colourNameToHex(themeButtonColor);
          }

          // alert(themeButtonColorhex);
          ChangeTheamColors(headerBg, themeButtonColor);
        }

      });

    });

    $("#resetButton").on('click', function() {
      Swal.fire({
        title: 'Confirmation',
        text: "Continue with Reset Changes?",
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ok',
        confirmButtonClass: 'btn btn-outline-primary btn-glow',
        cancelButtonClass: 'btn btn-outline-danger btn-glow ml-1',
        buttonsStyling: false,
      }).then(function(result) {
        if (result.value) {
          var businessId = $("#businessId").val();
          var templateId = $("#templateId").val();
          var templateCustomSettingId = $("#templateCustomSettingId").val();
          var logoWidth = '250';

          $.ajax({
            type: "GET",
            url: "<?php echo BASE_PATH; ?>/ajax/ajax_reset_themecolors.html",
            data: {
              businessId: businessId,
              templateId: templateId,
              templateCustomSettingId: templateCustomSettingId,
              logoWidth: logoWidth

            },
            success: function() {

              location.reload();
            }
          });

        }
      });

    });


    function ChangeTheamColors(headerBg, themeButtonColor) {

      var businessId = $("#businessId").val();
      var templateId = $("#templateId").val();
      var templateCustomSettingId = $("#templateCustomSettingId").val();
      var logoWidth = $("#logoWidth").val();


      $.ajax({
        type: "GET",
        url: "<?php echo BASE_PATH; ?>/ajax/ajax_apply_custom_settings_for_herosection.html",
        data: {
          businessId: businessId,
          templateId: templateId,
          templateCustomSettingId: templateCustomSettingId,
          themeHeaderBackgroundColor: headerBg,
          themeButtonColor: themeButtonColor,
          logoWidth: logoWidth,

        },
        success: function() {
          location.reload();

        }
      });
    }


    $(".modal1").on('click', function() {
      setTimeout(function() {
        $('#contactmessage').summernote({
          height: 100,
          toolbar: false
        });
      }, 500);
    });



    //$("#textDiv").hide();
    $("#qrcodeDiv").hide();
    $('#isEmail').on('click', function() {
      $(".textDiv").show();
      $("#emailDiv").show();
      $(".details").show();
      $("#btnFormSave").show();
      $("#qrcodeDiv").hide();
    });

    $('#isText').on('click', function() {
      $("#emailDiv").show();
      $("#qrcodeDiv").hide();
      $(".textDiv").show();
      $(".details").show();
      $("#btnFormSave").show();
    });

    $('#isQrCode').on('click', function() {
      $("#emailDiv").hide();
      $("#qrcodeDiv").show();
      $(".textDiv").hide();
      $(".details").hide();
      $("#btnFormSave").hide();
    });


    $('#btnFormSave').click(function() {
      var slug = $('#slug').val();
      var contactFirstName = $("input[name=contactFirstName]").val();
      if (contactFirstName == '') {
        $('#contactFirstNameSpan').fadeIn().html("Please Enter first name.");
        setTimeout(function() {
          $('#contactFirstNameSpan').fadeOut('slow')
        }, 5000);
        return false;
      }
      var contactLastName = $("input[name=contactLastName]").val();
      if (contactLastName == '') {
        $('#contactLastNameSpan').fadeIn().html("Please Enter last name.");
        setTimeout(function() {
          $('#contactLastNameSpan').fadeOut('slow')
        }, 5000);
        return false;
      }

      var contactemail = $("input[name=contactemail]").val();
      if (contactemail == '') {
        $('#contactemailSpan').fadeIn().html("Please Enter email.");
        setTimeout(function() {
          $('#contactemailSpan').fadeOut('slow')
        }, 5000);
        return false;
      }

      var contactphone = $("input[name=contactphone]").val();
      if (contactphone == '') {
        $('#contactphoneSpan').fadeIn().html("Please Enter Phone Number.");
        setTimeout(function() {
          $('#contactphoneSpan').fadeOut('slow')
        }, 5000);
        return false;
      }

      // $('#btnFormSave').html('Please wait...<i class="fa fa-circle-o-notch fa-spin" aria-hidden="true"></i>');
      // $('#btnFormSave').attr('disabled', true);
      $.ajax({
        url: '<?php echo BASE_PATH; ?>/sharemessagesubmit.html',
        type: 'POST',
        data: $('#serviceForm').serialize(),
        success: function(responseObj) {
          // alert(responseObj);
          // console.log(responseObj);
          var obj = jQuery.parseJSON(responseObj);
          if (obj.status == "alreadyExistBusiness") {
            $('#btnFormSave').html('Submit');
            $('#btnFormSave').attr('disabled', false);
            alertify.error("This email is already registered with a business.");

          } else if (obj.status == "success") {
            // alertify.success("Registered successfully.");
            location.reload();
          }
          else if (obj.status == "limitover")
          {
            // alert("hello "+ obj.status);
            Swal.fire({
              title: 'Warning',
              text: "contact enroll limit is over.",
              type: 'warning',
            })
            // alertify.error("contact enroll limit is over.");
            // location.reload();
          }
        }
      });

    });

    function checkiscliked(Id) {
      // alert(Id);
      // Hide all tab-panels except the one with the specified Id
      $('.tab-panels .tab-panel').hide();
      $('#tab-' + Id).show();

      // Uncheck all tabs except the one with the specified Id
      $('input[name="tabset"]').prop('checked', false);
      $('#tab-' + Id).prop('checked', true);
    }

    $('.tab-a').click(function(event) {
      event.preventDefault(); // Prevent the default behavior of anchor tags
      // moveLeft();
    });

    $('.tab-a').click(function(event) {
      event.preventDefault(); // Prevent the default behavior of anchor tags
      // moveRight();
    });

    const swiper = new Swiper('.sample-slider', {
      loop: true, // loop
      // effect: "cube", // make slider cube
      cubeEffect: {
        slideShadows: true, // Presence of shadows on the slide surface
        shadow: true, // Presence of shadows below the slide
        shadowOffset: 100, // Position of shadows below the slide (in pixels)
        shadowScale: 1, // Size Ratio of shadows below the slide (0~1)
      },
      grabCursor: true, // grab cursor
      speed: 1500, // slide speed
      pagination: { // pagination(dots)
        el: '.swiper-pagination',
      },
      navigation: { // navigation(arrows)
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
    })



       $('.isViewPdf').click(function() {
      var DEVICE = '<?php echo $DEVICE; ?>';
      // $('.slider').slick('slickPause').slick('slickSetOption', 'pauseOnDotsHover', false);

      var pdfsrc = $(this).attr('pdfsrc');
      // alert(pdfsrc);
    //   $('#viewPdfModalForWeb').attr('src', '');
    //   $('#viewPdfModalForMobile').attr('src', '');
    //   if (pdfsrc) {
    //     var mobilepdfSrc = 'https://drive.google.com/viewerng/viewer?embedded=true&url=' + pdfsrc + '#toolbar=0&scrollbar=0';

    //     $('#viewPdfModalForMobile').attr('src', pdfsrc);
    //     $('#viewPdfModalForWeb').attr('src', pdfsrc);
    //     // $('#pdfModal').modal('show');
    //   }

      renderPdfWithPDFjs(pdfsrc)
    });
    
   function renderPdfWithPDFjs(pdfUrl) {
  // Correct path to PDF.js worker
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

  const container = document.getElementById('pdfCanvasWrapper');
  container.innerHTML = ''; // Clear previous canvases

  const loadingTask = pdfjsLib.getDocument(pdfUrl);

  loadingTask.promise.then(function(pdf) {
    const totalPages = pdf.numPages;

    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      pdf.getPage(pageNum).then(function(page) {
        const scale = 0.7;
        const viewport = page.getViewport({ scale });

        const canvas = document.createElement('canvas');
        canvas.style.display = 'block';
        canvas.style.margin = '0 auto 20px auto';

        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
          canvasContext: context,
          viewport: viewport
        };

        page.render(renderContext);
        container.appendChild(canvas);
      });
    }
  }).catch(function(error) {
    console.error("PDF loading error:", error);
    alert("Failed to load PDF.");
  });
}

    // $("#pdfModal").on('hide.bs.modal', function() {
    //   $('#viewPdfModalForWeb').attr('src', '');
    //   $('#viewPdfModalForMobile').attr('src', '');

    //   // $('.slider').slick('slickPlay').slick('slickSetOption', 'pauseOnDotsHover', true);
    // });

    // $('#closePdfPopup').click(function () {
    // 		$('#pdfModal').hide();
    // 		$('.modal-backdrop').hide();
    // 		$('.modalRomove').removeClass('show');
    //     // location.reload();
    // 	})



    // iframe video script start

    var videoLink = $('.el-video-link-js'),
      popup_id = $('#el-video-popup'),
      videoLink = $('.el-video-link-js'),
      videoBody = $('.el-video-body-js'),
      videoCloseButton = $('.el-video-button-js'),
      popupOverlay = $('.el-video-overlay_popup');


    videoLink.click(function(e) {
      e.preventDefault();

      linkVideoSrc = $(this).attr('data-href');
      var videoSrc = $('#id1').val();
      popup_id.find('.el-video-popup__box-js').append('<iframe src="' + videoSrc + '" frameborder="0" allowfullscreen="" allow="autoplay">');
      popup_id.find(videoCloseButton).css("display", "block");
      console.log(linkVideoSrc);

      popup_id.show();
      popupOverlay.show();

      // Add the conditional CSS change
      if (popup_id.is(":visible")) {
        popup_id.find(videoCloseButton).css("display", "flex");
      }
    });

    $(videoCloseButton).click(function(e) {
      e.preventDefault();
      $(this).closest(videoBody).find(videoLink).css("display", "block");
      $(this).css("display", "none");
      popup_id.find('.el-video-popup__box-js iframe').remove();
      popupOverlay.hide();
      popup_id.hide();
    });
    $(popupOverlay).click(function() {

      popupOverlay.hide();
      popup_id.hide();
      popup_id.find('.el-video-popup__box-js iframe').remove();
    });

    // iframe video script end
  </script>


  <script>
    $('.color-picker').spectrum({
      type: "component"
    });

    $(".picker").spectrum({
      color: tinycolor,
      type: sting, // text, component, color, flat
      showInput: bool,
      showInitial: bool,
      allowEmpty: bool,
      showAlpha: bool,
      disabled: bool,
      localStorageKey: string,
      showPalette: bool,
      showPaletteOnly: bool,
      togglePaletteOnly: bool,
      showSelectionPalette: bool,
      clickoutFiresChange: bool,
      containerClassName: string,
      replacerClassName: string,
      preferredFormat: string,
      maxSelectionSize: int,
      palette: [
        [string]
      ],
      selectionPalette: [string],
      // specify locale
      locale: string,
      // or directly change the translations
      cancelText: string,
      chooseText: string,
      togglePaletteMoreText: string,
      togglePaletteLessText: string,
      clearText: string,
      noColorSelectedText: string,
    });
  </script>

  <script>
    const eyeDropperDiv = document.querySelector('.dropper-icon');
    const colorPickerDialogue = document.querySelector('.cp-dialogue');
    const saveButton = document.getElementById('saveButton');
    const resetButton = document.getElementById('resetButton');

    eyeDropperDiv.addEventListener('click', () => {
      colorPickerDialogue.style.display = 'block';
    });

    saveButton.addEventListener('click', () => {
      colorPickerDialogue.style.display = 'none';
    });

    resetButton.addEventListener('click', () => {
      colorPickerDialogue.style.display = 'none';
    });

    // Click event listener on the document to close the dialogue if clicked outside
    document.addEventListener('click', (event) => {
      if (!colorPickerDialogue.contains(event.target) && !eyeDropperDiv.contains(event.target)) {
        colorPickerDialogue.style.display = 'none';
      }
    });
  </script>
  <script>
    const collapsibles = document.querySelectorAll('.collapsible');
    const contents = document.querySelectorAll('.content-tab');
    const arrowIcons = document.querySelectorAll('.arrow-icon');


    collapsibles.forEach((contents, index) => {
      contents.addEventListener('click', () => {
        setTimeout(() => {
          document.querySelector(".fc-button-group .fc-month-button").click();
        }, 5);
      });
    });

    const states = Array.from(collapsibles).map(() => ({
      isOpen: false,
    }));

    collapsibles.forEach((collapsible, index) => {
      collapsible.addEventListener('click', () => {
        states.forEach((state, i) => {
          if (i !== index && state.isOpen) {
            contents[i].classList.remove('active');
            arrowIcons[i].style.transform = 'rotate(0deg)';
            state.isOpen = false;
          }
        });

        contents[index].classList.toggle('active');
        arrowIcons[index].style.transform = states[index].isOpen ? 'rotate(0deg)' : 'rotate(180deg)';
        states[index].isOpen = !states[index].isOpen;
      });
    });

    $(document).ready(function() {

      // Update the progress bar value and image dimensions based on user input
      const progressFill = document.getElementById('progressFill');
      const progressInput = document.getElementById('progressInput');
      const imageDisplay = document.getElementById('imageDisplay');
      const maxAttributeValue = progressInput.getAttribute('max');

      const logowidth = '<?php echo ($this->logowidth); ?>';


      // Set the initial width of the image and progress bar
      const initialWidth = parseInt(logowidth);
      const maxProgressWidth = parseInt(maxAttributeValue); // Corresponds to the initial image size
      const minImageWidth = 20; // Define the minimum image width (you can adjust this value)

      // Set the initial value of the progressInput to the initial image width
      progressInput.value = initialWidth;

      progressFill.style.width = initialWidth + 'px';
      imageDisplay.style.width = initialWidth + 'px';

      // Ensure that the height is always half of the width
      imageDisplay.style.height = (initialWidth / 4) + 'px';

      // Add an event listener to respond to user input
      progressInput.addEventListener('input', function() {
        const progressValue = parseInt(this.value);

        // Ensure the progressValue stays within the defined range
        if (progressValue < minImageWidth) {
          this.value = minImageWidth;
          // alert("minImageWidth"+minImageWidth);
        } else if (progressValue > maxProgressWidth) {
          this.value = maxProgressWidth;
          // alert("maxProgressWidth"+maxProgressWidth);

        }

        // Calculate the new progress bar width based on the user input
        const newProgressWidth = (progressValue / maxProgressWidth) * 100;

        // Update the progress bar width
        progressFill.style.width = newProgressWidth + 'px';

        // Set the new width and height of the image
        imageDisplay.style.width = progressValue + 'px';
        imageDisplay.style.height = (progressValue / 4) + 'px';

        // Update the hidden input field with the new image width
        $('#logoWidth').val(progressValue);
      });

    });

    function numberOnly(id) {
			var element = document.getElementById(id);
			element.value = element.value.replace(/[^0-9]/gi, "");
			// element.value = element.value.replace(/[^0-9.]/g, ''); // Allow digits and a single decimal point
		}
  </script>
</body>

</html>
